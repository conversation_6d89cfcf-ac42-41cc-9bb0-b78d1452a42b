<?php

defined( 'ABSPATH' ) || exit;

/**
 * Addon Name: Restrict Content Pro Integration
 * Addon URI: https://masteriyo.com/wordpress-lms/
 * Addon Type: integration
 * Description: Elevate course monetization and access control in your course with Masteriyo's Restrict Content Pro Integration.
 * Author: Masteriyo
 * Author URI: https://masteriyo.com
 * Requires: Restrict Content Pro
 * Plan: Starter,Pro,Elite,Growth,Scale
 */

use Masteriyo\Pro\Addons;
use Masteriyo\Addons\RCPIntegration\Helper;
use Masteriyo\Addons\RCPIntegration\RCPIntegrationAddon;

define( 'MASTERIYO_RCP_INTEGRATION_ADDON_FILE', __FILE__ );
define( 'MASTERIYO_RCP_INTEGRATION_ADDON_BASENAME', plugin_basename( __FILE__ ) );
define( 'MASTERIYO_RCP_INTEGRATION_ADDON_DIR', __DIR__ );
define( 'MASTERIYO_RCP_INTEGRATION_ASSETS', __DIR__ . '/assets' );
define( 'MASTERIYO_RCP_INTEGRATION_TEMPLATES', __DIR__ . '/templates' );
define( 'MASTERIYO_RCP_INTEGRATION_ADDON_SLUG', 'rcp-integration' );

if ( ( new Addons() )->is_active( MASTERIYO_RCP_INTEGRATION_ADDON_SLUG ) && ! Helper::is_rcp_active() ) {
	add_action(
		'masteriyo_admin_notices',
		function() {
			printf(
				'<div class="notice notice-warning is-dismissible"><p><strong>%s </strong>%s</p><button type="button" class="notice-dismiss"><span class="screen-reader-text">%s</span></button></div>',
				esc_html( 'Masteriyo PRO:' ),
				esc_html__( 'Restrict Content Pro Integration addon requires Restrict Content Pro to be installed and activated.', 'learning-management-system' ),
				esc_html__( 'Dismiss this notice.', 'learning-management-system' )
			);
		}
	);
}

if ( ! Helper::is_rcp_active() ) {
	add_filter(
		'rcp-integration_activation_requirements',
		function ( $result, $request, $controller ) {
			$result = __( 'Restrict Content Pro is to be installed and activated for this addon to work properly.', 'learning-management-system' );
			return $result;
		},
		10,
		3
	);

	add_filter(
		'masteriyo_pro_addon_data',
		function( $data, $slug ) {
			if ( 'rcp-integration' === $slug ) {
				$data['requirement_fulfilled'] = masteriyo_bool_to_string( Helper::is_rcp_active() );
			}

			return $data;
		},
		10,
		2
	);
}

// Bail early if the addon is not active.
if ( ! ( ( new Addons() )->is_active( MASTERIYO_RCP_INTEGRATION_ADDON_SLUG ) && Helper::is_rcp_active() ) ) {
	return;
}

// Initialize Restrict Content Pro integration addon.
RCPIntegrationAddon::instance()->init();
