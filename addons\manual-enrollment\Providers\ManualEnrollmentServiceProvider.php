<?php
/**
 * Manual Enrollment service provider.
 *
 * @since 2.4.4
 */

namespace Masteriyo\Addons\ManualEnrollment\Providers;

defined( 'ABSPATH' ) || exit;

use Masteriyo\Addons\ManualEnrollment\ManualEnrollmentAddon;
use League\Container\ServiceProvider\AbstractServiceProvider;

/**
 * Manual Enrollment service provider.
 *
 * @since 2.4.4
 */
class ManualEnrollmentServiceProvider extends AbstractServiceProvider {
	/**
	 * The provided array is a way to let the container
	 * know that a service is provided by this service
	 * provider. Every service that is registered via
	 * this service provider must have an alias added
	 * to this array or it will be ignored
	 *
	 * @since 2.4.4
	 *
	 * @var array
	 */
	protected $provides = array();

	/**
	 * This is where the magic happens, within the method you can
	 * access the container and register or retrieve anything
	 * that you need to, but remember, every alias registered
	 * within this method must be declared in the `$provides` array.
	 *
	 * @since 2.4.4
	 */
	public function register() {
	}
}
