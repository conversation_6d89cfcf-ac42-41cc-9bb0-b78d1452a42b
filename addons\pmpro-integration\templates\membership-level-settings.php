<?php
/**
 * Template for displaying membership level course setting.
 *
 * @since 2.6.9
 * @package Masteriyo\Addons\PMPROIntegration
 */

defined( 'ABSPATH' ) || exit;
?>
<div id="masteriyo-settings">
	<?php wp_nonce_field( 'mas_pmpro_settings', '_mas_pmpro_nonce' ); ?>
	<h3><?php esc_html_e( 'Masteriyo', 'learning-management-system' ); ?></h3>
	<table class="form-table">
		<tbody>
			<tr id="masteriyo-settings-membership-level-courses">
				<th scope="row" valign="top"><label for="_mas_courses"><?php esc_html_e( 'Courses', 'learning-management-system' ); ?></label></th>
				<td>
					<select data-placeholder="<?php esc_attr_e( 'Select Courses', 'learning-management-system' ); ?>"  class="mas-select2" name="_mas_courses[]" id="_mas_courses" multiple="multiple">
						<?php foreach ( $courses as $course_id => $course_title ) : ?>
							<option value="<?php echo esc_attr( $course_id ); ?>" <?php selected( in_array( $course_id, $membership_level_courses, true ) ); ?>>
								<?php echo esc_html( $course_title ); ?>
							</option>
						<?php endforeach; ?>
					</select>
				</td>
			</tr>
		</tbody>
	</table>
</div>
<script>
	jQuery(document).ready(function($){
		$('.mas-select2').select2({
			placeholder: "<?php echo esc_js( __( 'Select Courses', 'learning-management-system' ) ); ?>",
			closeOnSelect: false,
			width: '100%',
		})
	});
</script>
