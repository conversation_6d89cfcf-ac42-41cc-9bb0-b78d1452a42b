<?php
/**
 * Course Bundle class.
 *
 * @since 2.12.0
 *
 * @package Masteriyo\PostType;
 */

namespace Masteriyo\Addons\CourseBundle\PostType;

defined( 'ABSPATH' ) || exit;


use Masteriyo\PostType\PostType;

/**
 * Zoom class.
 */
class CourseBundle extends PostType {

	/**
	 * Post slug.
	 *
	 * @since 2.12.0
	 *
	 * @var string
	 */
	protected $slug = PostType::COURSE_BUNDLE;

	/**
	 * Constructor.
	 */
	public function __construct() {
		$debug                  = masteriyo_is_post_type_debug_enabled();
		$permalinks             = masteriyo_get_permalink_structure();
		$course_bundles_page_id = masteriyo_get_page_id( 'course-bundles' );

		if ( $course_bundles_page_id && get_post( $course_bundles_page_id ) ) {
			$has_archive = urldecode( get_page_uri( $course_bundles_page_id ) );
		} else {
			$has_archive = 'course-bundles';
		}

		$this->labels = array(
			'name'                  => _x( 'Course Bundle', 'Course Bundle General Name', 'learning-management-system' ),
			'singular_name'         => _x( 'Course Bundle', 'Course Bundle Singular Name', 'learning-management-system' ),
			'menu_name'             => __( 'Course Bundles', 'learning-management-system' ),
			'name_admin_bar'        => __( 'Course Bundle', 'learning-management-system' ),
			'archives'              => __( 'Course Bundle Archives', 'learning-management-system' ),
			'attributes'            => __( 'Course Bundle Attributes', 'learning-management-system' ),
			'parent_item_colon'     => __( 'Parent Course Bundle:', 'learning-management-system' ),
			'all_items'             => __( 'All Course Bundles', 'learning-management-system' ),
			'add_new_item'          => __( 'Add New Item', 'learning-management-system' ),
			'add_new'               => __( 'Add New', 'learning-management-system' ),
			'new_item'              => __( 'New Course Bundle', 'learning-management-system' ),
			'edit_item'             => __( 'Edit Course Bundle', 'learning-management-system' ),
			'update_item'           => __( 'Update Course Bundle', 'learning-management-system' ),
			'view_item'             => __( 'View Course Bundle', 'learning-management-system' ),
			'view_items'            => __( 'View Course Bundles', 'learning-management-system' ),
			'search_items'          => __( 'Search Course Bundle', 'learning-management-system' ),
			'not_found'             => __( 'Not found', 'learning-management-system' ),
			'not_found_in_trash'    => __( 'Not found in Trash.', 'learning-management-system' ),
			'featured_image'        => __( 'Featured Image', 'learning-management-system' ),
			'set_featured_image'    => __( 'Set featured image', 'learning-management-system' ),
			'remove_featured_image' => __( 'Remove featured image', 'learning-management-system' ),
			'use_featured_image'    => __( 'Use as featured image', 'learning-management-system' ),
			'insert_into_item'      => __( 'Insert into Course Bundle', 'learning-management-system' ),
			'uploaded_to_this_item' => __( 'Uploaded to this Course Bundle', 'learning-management-system' ),
			'items_list'            => __( 'Course Bundles list', 'learning-management-system' ),
			'items_list_navigation' => __( 'Course Bundles list navigation', 'learning-management-system' ),
			'filter_items_list'     => __( 'Filter course bundles list', 'learning-management-system' ),
		);

		$this->args = array(
			'label'               => __( 'Course Bundle', 'learning-management-system' ),
			'description'         => __( 'Course Bundle Description', 'learning-management-system' ),
			'labels'              => $this->labels,
			'supports'            => array( 'title', 'custom-fields' ),
			'taxonomies'          => array( 'bundle_cat', 'bundle_tag' ),
			'hierarchical'        => false,
			'menu_position'       => 5,
			'public'              => $debug,
			'show_ui'             => $debug,
			'show_in_menu'        => $debug,
			'show_in_admin_bar'   => $debug,
			'show_in_nav_menus'   => $debug,
			'show_in_rest'        => false,
			'map_meta_cap'        => true,
			'capability_type'     => array( 'bundle', 'bundles' ),
			'exclude_from_search' => true,
			'publicly_queryable'  => true,
			'can_export'          => true,
			'delete_with_user'    => false,
			'rewrite'             => $permalinks['course_bundle_rewrite_slug'] ? array(
				'slug'       => $permalinks['course_bundle_rewrite_slug'],
				'with_front' => false,
				'feeds'      => true,
			) : false,
			'has_archive'         => $has_archive,
		);
	}
}
