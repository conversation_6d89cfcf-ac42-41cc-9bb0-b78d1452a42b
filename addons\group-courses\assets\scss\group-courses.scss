.masteriyo-group-courses-modal {
	.masteriyo-overlay {
		align-items: center;
	}
}

.masteriyo-group-course-popup {
	border-radius: 8px;
	background: #fff;
	box-shadow: 0px 0px 25px 0px rgba(10, 10, 10, 0.1);
	display: flex;
	width: 520px;
	padding: 28px 28px 30px 28px;
	flex-direction: column;
	gap: 32px;
}

.masteriyo-group-course {
	&__wrapper {
		position: relative;
	}

	&__heading {
		color: #222222;
		font-size: 24px;
		font-weight: 700;
		line-height: 32px;
		padding-bottom: 16px;
		border-bottom: 1px solid #e5e5e5;
		margin-bottom: 18px;
	}

	&__exit-popup {
		position: absolute;
		top: 0;
		right: 0;
		border-radius: 60px;
		background: #eef1f7;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 7px;
		cursor: pointer;

		svg {
			width: 14px;
			height: 14px;
		}
	}

	&__title {
		color: #646464;
		font-size: 16px;
		font-weight: 400;
		line-height: 26px;
	}

	&__empty-state {
		display: none;

		&--image {
			text-align: center;
			margin-bottom: 32px;

			img {
				width: 260px;
				height: auto;
				object-fit: cover;
			}
		}

		&--content {
			text-align: center;

			&-title {
				color: #383838;
				font-size: 22px;
				font-weight: 700;
				line-height: 29px;
				margin-bottom: 8px;
			}

			&-desc {
				color: #4e4e4e;
				font-size: 14px;
				font-weight: 400;
				line-height: 16px;

				.masteriyo-group-course--link {
					color: #5b5fc7;
					font-style: italic;
					text-decoration: underline;
				}
			}
		}
	}

	&__lists {
		&--label {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 12px;
		}

		&--heading {
			color: #646464;
			font-size: 14px;
			font-weight: 600;
			line-height: 24px;
		}

		&--sync-button {
			color: #7a7a7a;
			font-size: 12px;
			font-weight: 500;
			line-height: 26px;
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 6px;
			text-decoration: none;
			margin-top: 14px;
			transition: all 0.3s ease-in-out;

			&:hover {
				text-decoration: underline;
			}

			svg {
				width: 16px;
				height: 16px;
			}
		}

		&--list {
			margin-left: 0;
			margin-bottom: 24px;

			&-item {
				list-style: none;
				display: flex;
				align-items: center;
				position: relative;
				margin-bottom: 10px;

				&:last-child {
					margin-bottom: 0;
				}

				input[type='checkbox'] {
					position: absolute;
					z-index: 1;
					left: 12px;
					width: 18px;
					height: 18px;
				}

				&_label {
					width: 100%;
					border-radius: 4px;
					background: #f0f0f0;
					padding: 12px;
					padding-left: 40px;
					display: flex;
					align-items: center;
					gap: 8px;
					cursor: pointer;
					color: #646464;
					font-size: 14px;
					font-weight: 500;
					line-height: 22px;

					&_disabled {
						color: #a7a7a7;
					}
				}

				&_members {
					position: absolute;
					right: 12px;
					top: 50%;
					transform: translateY(-50%);
					display: flex;
					align-items: center;
					gap: 4px;
					color: #7a7a7a;
					font-size: 14px;
					font-weight: 500;
					line-height: 22px;

					svg {
						width: 20px;
						height: 20px;
					}
				}

				&_disabled {
					cursor: none;
					pointer-events: none;
				}

				input[type='checkbox']:checked
					~ .masteriyo-group-course__lists--list-item_label {
					background: #f3f7fd;
				}
			}
		}

		.masteriyo-error-message {
			color: #d8000c;
			background-color: #ffdddd;
			padding: 10px;
			border: 1px solid #d8000c;
			border-radius: 4px;
			margin-bottom: 25px;
			font-size: 14px;
		}

		&--footer {
			display: flex;
			align-items: center;
			justify-content: flex-end;
			gap: 14px;

			&_link {
				color: #646464;
				font-size: 14px;
				font-weight: 400;
				line-height: 22px;
				text-decoration-line: underline;
				transition: all 0.3s ease-in-out;

				&:hover {
					color: #222;
				}
			}

			&_checkout-button {
				display: flex;
				padding: 10px 16px 10px 12px;
				justify-content: center;
				align-items: center;
				gap: 8px;
				border-radius: 4px;
				background: #3e6dd0;
				color: #ffffff;
				font-size: 14px;
				font-weight: 400;
				line-height: 22px;
				text-decoration: none;
				transition: all 0.3s ease-in-out;

				&:hover {
					color: #ffffff;
					background: #1b4aab;
				}

				&:focus {
					color: #ffffff;
				}

				svg {
					width: 20px;
					height: 20px;

					path {
						stroke: #ffffff;
					}
				}

				&--disabled {
					background: #e2e2e2;
					color: #a7a7a7;
					pointer-events: none;
					cursor: not-allowed;

					svg {
						path {
							stroke: #a7a7a7;
						}
					}
				}
			}
		}
	}

	&__group {
		&-button {
			width: 100%;
			border-radius: 4px;
			background: #f4f4f4;
			padding: 16px 20px 20px 20px;
			margin-top: 24px;
		}

		&-title {
			color: #4e4e4e;
			font-size: 16px;
			font-weight: 500;
			line-height: 34px;
			margin-top: 0;
			margin-bottom: 8px;
			display: flex;
			align-items: center;
			gap: 8px;
			flex-direction: column;
			border-bottom: 1px solid #e1e1e1;

			.title-line {
				flex: 1 0 0;

				&::after {
					content: '';
					width: 100%;
					height: 1px;
					background: #dddddd;
					display: block;
				}
			}
		}

		&-desc {
			color: #646464;
			font-size: 14px;
			font-style: italic;
			font-weight: 400;
			line-height: 24px;
			margin-top: 0;
			margin-bottom: 16px;
		}
	}

	&__buy-now-button {
		border-radius: 3px;
		background: #26c164;
		padding: 10px 16px;
		color: #ffffff;
		font-size: 14px;
		font-weight: 500;
		line-height: 24px;
		text-decoration: none;
		display: block;
		text-align: center;
		transition: all 0.3s ease-in-out;

		&:hover {
			color: #ffffff;
			background: #08903e;
		}

		&:focus {
			color: #ffffff;
		}
	}

	&__checkout {
		&-title {
			color: #07092f;
			font-size: 26px;
			line-height: 34px;
			font-weight: 500;
			margin-top: 0;
			margin-bottom: 28px;
		}

		&-table {
			border-radius: 5px;
			background: #f9f9f9;
			padding: 20px;

			table {
				width: 100%;
				border-collapse: collapse;
				border: 0;
				margin-bottom: 0;

				tr {
					th {
						color: #383838;
						font-size: 14px;
						font-weight: 600;
						line-height: 18px;
						padding: 0 6px 16px 6px;
						border: 0;
						border-bottom: 1px solid #e1e1e1;
						width: 90%;
						text-align: left;
					}

					td {
						color: #4e4e4e;
						font-size: 14px;
						font-weight: 500;
						line-height: 18px;
						padding: 16px 6px 0px;
						border: 0;
					}
				}
			}
		}
	}
}

.masteriyo-checkout {
	&-summary {
		&-order-details {
			li {
				.masteriyo-badge {
					background: #26c164 !important;
					margin-left: 2px;
				}
			}
		}
	}
}

.masteriyo-single-body__aside--price {
	.masteriyo-group-course__group-button {
		margin-top: 0;
	}
}
