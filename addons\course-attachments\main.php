<?php

defined( 'ABSPATH' ) || exit;

/**
 * Addon Name: Course Attachments
 * Addon URI: https://masteriyo.com/wordpress-lms/
 * Description: If you need to add some materials for your courses that visitors need to download then this feature will come handy.
 * Author: Masteriyo
 * Author URI: https://masteriyo.com
 * Addon Type: enhancement
 * Plan: Basic,Starter,Pro,Elite,Growth,Scale
 */

use Masteriyo\Pro\Addons;

define( 'MASTERIYO_COURSE_ATTACHMENTS_ADDON_FILE', __FILE__ );
define( 'MASTERIYO_COURSE_ATTACHMENTS_ADDON_BASENAME', plugin_basename( __FILE__ ) );
define( 'MASTERIYO_COURSE_ATTACHMENTS_ADDON_DIR', __DIR__ );
define( 'MASTERIYO_COURSE_ATTACHMENTS_TEMPLATES', __DIR__ . '/templates' );
define( 'MASTERIYO_COURSE_ATTACHMENTS_ADDON_SLUG', 'course-attachments' );

// Bail early if the addon is not active.
if ( ! ( new Addons() )->is_active( MASTERIYO_COURSE_ATTACHMENTS_ADDON_SLUG ) ) {
	return;
}

/**
 * Include service providers for course attachments.
 */
add_filter(
	'masteriyo_service_providers',
	function( $providers ) {
		return array_merge( $providers, require_once __DIR__ . '/config/providers.php' );
	}
);

/**
 * Initialize Masteriyo Course Attachments.
 */
add_action(
	'masteriyo_before_init',
	function() {
		masteriyo( 'addons.course-attachments' )->init();
	}
);
