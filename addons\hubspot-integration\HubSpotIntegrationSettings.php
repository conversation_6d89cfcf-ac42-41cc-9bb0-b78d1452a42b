<?php

/**
 * HubSpot integration settings class.
 *
 * @package Masteriyo\Addons\HubSpotIntegration
 *
 * @since 2.14.4
 */

namespace Masteriyo\Addons\HubSpotIntegration;

defined( 'ABSPATH' ) || exit;


use Masteriyo\EmailMarketingAndCRM\IntegrationSettings;

/**
 * HubSpot integration settings class.
 *
 * @since 2.14.4
 */
class HubSpotIntegrationSettings extends IntegrationSettings {

	/**
	 * The settings data.
	 *
	 * @since 2.14.4
	 *
	 * @var array
	 */
	protected static $data = array(
		'enable_forced_email_subscription' => false,
		'is_connected'                     => false,
		'access_token'                     => '',
		'list'                             => '',
		'subscriber_consent_message'       => 'I would like to receive the newsletters.',
	);


	/**
	 * Get the option name for the settings.
	 *
	 * @since 2.14.4
	 *
	 * @return string
	 */
	protected static function get_option_name() {
		return 'masteriyo_hubspot_integration_settings';
	}

	/**
	 * Get the HubSpot access token.
	 *
	 * @since 2.14.4
	 *
	 * @return string The HubSpot access token.
	 */
	public static function get_access_token() {
		return static::get( 'access_token' );
	}
}
