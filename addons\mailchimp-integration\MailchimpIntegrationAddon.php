<?php
/**
 * Masteriyo Mailchimp addon.
 *
 * @package Masteriyo\Addons\MailchimpIntegration
 *
 * @since 2.14.4 [Free]
 */
namespace Masteriyo\Addons\MailchimpIntegration;

use Masteriyo\Addons\MailchimpIntegration\Controllers\MailchimpIntegrationController;
use Masteriyo\Addons\MailchimpIntegration\API\API;
use Masteriyo\Constants;
use Masteriyo\Traits\Singleton;

defined( 'ABSPATH' ) || exit;

/**
 * Main Masteriyo MailchimpIntegration class.
 *
 * @class Masteriyo\Addons\MailchimpIntegration
 *
 * @since 2.14.4 [Free]
 */
class MailchimpIntegrationAddon {

	use Singleton;

	/**
	 * Initialize.
	 *
	 * @since 2.14.4 [Free]
	 */
	public function init() {
		$this->init_hooks();
	}

	/**
	 * Initialize hooks.
	 *
	 * @since 2.14.4 [Free]
	 */
	public function init_hooks() {
		add_filter( 'masteriyo_checkout_fields', array( $this, 'add_mailchimp_consent_checkbox_field_for_checkout' ) );
		add_filter( 'masteriyo_registration_form_fields', array( $this, 'add_mailchimp_consent_checkbox' ) );
		add_action( 'masteriyo_registration_form_before_submit_button', array( $this, 'render_container_for_mailchimp' ), 10, 2 );
		add_action( 'masteriyo_checkout_form_content', array( $this, 'render_container_for_mailchimp' ), 125 );
		add_filter( 'masteriyo_get_template', array( $this, 'change_template_for_mailchimp' ), 10, 5 );
		add_action( 'masteriyo_created_customer', array( $this, 'handle_customer_creation' ), 10, 3 );
		add_filter( 'masteriyo_rest_response_setting_data', array( $this, 'append_setting_in_response' ) );
		add_action( 'masteriyo_new_setting', array( $this, 'save_settings' ) );
		add_filter( 'masteriyo_rest_api_get_rest_namespaces', array( $this, 'register_rest_namespaces' ) );
	}

	/**
	 * Adds "mailchimp-permission-check" to the checkout form fields.
	 *
	 * @since 2.14.4 [Free]
	 *
	 * @param array $fields The checkout form fields.
	 * @return array The updated checkout form fields.
	 */
	public function add_mailchimp_consent_checkbox_field_for_checkout( $fields ) {
		$api_key = MailchimpIntegrationSettings::get_api_key();

		if ( empty( $api_key ) || ! masteriyo_string_to_bool( MailchimpIntegrationSettings::get( 'is_connected' ) ) ) {
			return $fields;
		}

		$forced_email_subscription = masteriyo_string_to_bool( MailchimpIntegrationSettings::get( 'forced_email_subscription' ) );

		if ( $forced_email_subscription ) {
			return $fields;
		}

		$fields['mailchimp-permission-check'] = array(
			'label'        => __( 'Mailchimp Consent Checkbox', 'learning-management-system' ),
			'enable'       => true,
			'required'     => false,
			'type'         => 'checkbox',
			'class'        => array( 'form-row-wide' ),
			'autocomplete' => 'no',
			'priority'     => 125,
		);

		return $fields;
	}

	/**
	 * Adds "mailchimp-permission-check" to the registration form fields.
	 *
	 * @since 2.14.4 [Free]
	 *
	 * @param array $fields The registration form fields.
	 * @return array The updated registration form fields.
	 */
	public function add_mailchimp_consent_checkbox( $fields ) {
		$fields[] = 'mailchimp-permission-check';

		return $fields;
	}

	/**
	 * Renders a container for Mailchimp email subscription.
	 *
	 * @since 2.14.4 [Free]
	 */
	public function render_container_for_mailchimp() {
		$api_key = MailchimpIntegrationSettings::get_api_key();

		if ( empty( $api_key ) || ! masteriyo_string_to_bool( MailchimpIntegrationSettings::get( 'is_connected' ) ) ) {
			return;
		}

		$forced_email_subscription = masteriyo_string_to_bool( MailchimpIntegrationSettings::get( 'forced_email_subscription' ) );

		if ( $forced_email_subscription ) {
			return;
		}

		$consent_message = MailchimpIntegrationSettings::get( 'subscriber_consent_message' );
		$consent_message = empty( $consent_message ) ? 'I would like to receive the newsletters.' : $consent_message;

		/* translators: %s is the consent message */
		$consent_message = sprintf( esc_html__( '%s', 'masteriyo' ), $consent_message ); // phpcs:ignore

		masteriyo_get_template( 'mailchimp-integration/consent-form-mailchimp.php', array( 'consent_message' => $consent_message ) );
	}

	/**
	 * Changes the template for Mailchimp integration specific templates.
	 *
	 * This function changes the template for Mailchimp integration specific templates.
	 * It changes the template only if it matches the template name in the template map.
	 *
	 * @since 2.14.4 [Free]
	 *
	 * @param string $template The template path.
	 * @param string $template_name The template name.
	 *
	 * @return string The updated template path or the original template path.
	 */
	public function change_template_for_mailchimp( $template, $template_name ) {

		$template_map = array(
			'mailchimp-integration/consent-form-mailchimp.php' => 'consent-form-mailchimp.php',
		);

		if ( isset( $template_map[ $template_name ] ) ) {
			$new_template = trailingslashit( Constants::get( 'MASTERIYO_MAILCHIMP_INTEGRATION_TEMPLATES' ) ) . $template_map[ $template_name ];

			return file_exists( $new_template ) ? $new_template : $template;
		}

		return $template;
	}

	/**
	 * Handles customer creation in Mailchimp integration.
	 *
	 * @since 2.14.4 [Free]
	 *
	 * @param \Masteriyo\Models\User    $user The user object.
	 * @param string                    $password_generated The generated password.
	 *
	 * @param array                     $args The form arguments.
	 */
	public function handle_customer_creation( $user, $password_generated, $args ) {

		if ( ! $user instanceof \Masteriyo\Models\User ) {
			return;
		}

		$forced_email_subscription = masteriyo_string_to_bool( MailchimpIntegrationSettings::get( 'forced_email_subscription' ) );
		$user_consent              = sanitize_text_field( masteriyo_array_get( $args, 'mailchimp-permission-check' ) );
		$user_consent              = 'on' === $user_consent || '1' === $user_consent ? true : false;

		if ( ! $user_consent && ! $forced_email_subscription ) {
			return;
		}

		$email = $user->get_email();
		$list  = MailchimpIntegrationSettings::get( 'list' );
		if ( ! empty( $list ) ) {
			$list_id = $list['value'];
		}

		$interests = MailchimpIntegrationSettings::get( 'interest' );

		$mailchimp_interested_list = array();
		foreach ( $interests as $interest ) {
			if ( isset( $interest['value'] ) ) {
				$mailchimp_interested_list[ $interest['value'] ] = true;
			}
		}

		$api = new API( MailchimpIntegrationSettings::get_api_key() );

		$data     = array(
			'email_address' => $email,
			'merge_fields'  => array(
				'FNAME'    => $user->get_first_name(),
				'LNAME'    => $user->get_last_name(),
				'USERNAME' => $user->get_username(),
			),
			'status'        => 'subscribed',
			'interests'     => (object) $mailchimp_interested_list,
		);
		$response = (object) $api->create_contact( $list_id, $data );
		if ( is_wp_error( $response ) ) {
			masteriyo_get_logger()->error( $response->get_error_message() . " For email: {$email}", array( 'source' => 'mailchimp-integration' ) );
			return;
		}

		masteriyo_get_logger()->info( "Mailchimp contact created for email: {$email}", array( 'source' => 'mailchimp-integration' ) );
	}

	/**
	 * Appends Mailchimp Integration settings to the response data.
	 *
	 * @since 2.14.4 [Free]
	 *
	 * @param array $data Response data.
	 * @return array Modified response data.
	 */
	public function append_setting_in_response( $data ) {
		$data['integrations']['mailchimp_integration'] = MailchimpIntegrationSettings::all( array( 'api_key' ) );

		return $data;
	}

	/**
	 * Saves Mailchimp Integration settings.
	 *
	 * @since 2.14.4 [Free]
	 *
	 * @return void
	 */
	public function save_settings() {
		if ( ! masteriyo_is_rest_api_request() ) {
			return;
		}

		$request = masteriyo_current_http_request();

		if ( ! isset( $request['integrations']['mailchimp_integration'] ) ) {
			return;
		}

		$settings                               = masteriyo_array_only( $request['integrations']['mailchimp_integration'], array_keys( MailchimpIntegrationSettings::all() ) );
		$settings                               = wp_parse_args( $settings, MailchimpIntegrationSettings::all() );
		$settings['forced_email_subscription']  = sanitize_text_field( $settings['forced_email_subscription'] ?? false );
		$settings['list']                       = $settings['list'] ?? '';
		$settings['group']                      = $settings['group'] ?? '';
		$settings['interest']                   = $settings['interest'] ?? '';
		$settings['subscriber_consent_message'] = sanitize_text_field( $settings['subscriber_consent_message'] ?? '' );

		unset( $settings['api_key'] );

		MailchimpIntegrationSettings::set_props( $settings );
		MailchimpIntegrationSettings::save();
	}


	/**
	 * Registers the Mailchimp Integration namespace to the REST API.
	 *
	 * @since 2.14.4 [Free]
	 *
	 * @param array $namespaces List of namespaces and their controllers.
	 *
	 * @return array Modified list of namespaces and their controllers.
	 */
	public function register_rest_namespaces( $namespaces ) {
		$namespaces['masteriyo/v1']['mailchimp-integration'] = MailchimpIntegrationController::class;

		return $namespaces;
	}
}
