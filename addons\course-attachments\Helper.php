<?php
/**
 * Masteriyo Course attachment helper functions.
 *
 * @package Masteriyo\Addons\CourseAttachments
 *
 * @since 2.3.0
 */

namespace Masteriyo\Addons\CourseAttachments;

use Masteriyo\Constants;

defined( 'ABSPATH' ) || exit;

class Helper {
	/**
	 * Return svg file based on svg file name.
	 *
	 * @since 2.3.0
	 *
	 * @param string $name SVG file name.
	 * @param boolean $echo Whether to echo the contents or not.
	 *
	 * @return string|void
	 */
	public static function get_svg( $name, $echo = false ) {
		global $wp_filesystem;

		$credentials = request_filesystem_credentials( '', 'direct' );

		// Bail early if the credentials is wrong.
		if ( ! $credentials ) {
			return;
		}

		\WP_Filesystem( $credentials );

		$file_name     = Constants::get( 'MASTERIYO_COURSE_ATTACHMENTS_ADDON_DIR' ) . "/svgs/{$name}.svg";
		$file_contents = '';

		if ( file_exists( $file_name ) && is_readable( $file_name ) ) {
			$file_contents = $wp_filesystem->get_contents( $file_name );
		}

		/**
		 * Filters svg file content.
		 *
		 * @since 1.0.0
		 *
		 * @param string $file_content SVG file content.
		 * @param string $name SVG file name.
		 */
		$file_contents = apply_filters( 'masteriyo_pro_course_attachments_svg_icon', $file_contents, $name );

		$svg_args = array(
			'svg'   => array(
				'class'           => true,
				'aria-hidden'     => true,
				'aria-labelledby' => true,
				'role'            => true,
				'xmlns'           => true,
				'width'           => true,
				'height'          => true,
				'viewbox'         => true, // <= Must be lower case!
				'fill'            => true,
			),
			'g'     => array( 'fill' => true ),
			'title' => array( 'title' => true ),
			'path'  => array(
				'd'    => true,
				'fill' => true,
			),
		);

		if ( $echo ) {
			echo wp_kses( $file_contents, $svg_args );
		} else {
			return $file_contents;
		}
	}

	/**
	 * Return svg file from mime type.
	 *
	 * @since 2.3.0
	 *
	 * @param string $mime_type Mime type.
	 * @param boolean $echo Whether to echo the contents or not.
	 *
	 * @return string|void
	 */
	public static function get_svg_from_mime_type( $mime_type, $echo = false ) {
		$mime_type_maps = array(
			'image/png'                => 'image',
			'image/jpgeg'              => 'image',
			'image/jpg'                => 'image',
			'image/bmp'                => 'image',
			'image/gif'                => 'image',
			'image/pipeg'              => 'image',
			'image/tiff'               => 'image',
			'application/pdf'          => 'pdf',
			'application/x-compressed' => 'zip',
			'application/x-compress'   => 'zip',
			'application/x-zip'        => 'zip',
			'application/zip'          => 'zip',
			'application/msword	'      => 'word',
			'application/vnd.ms-works' => 'word',
			'application/vnd.openxmlformats-officedocument.wordprocessingml.document' => 'word',
		);

		$name = isset( $mime_type_maps[ $mime_type ] ) ? $mime_type_maps[ $mime_type ] : 'file';

		if ( $echo ) {
			self::get_svg( $name, true );
		} else {
			return self::get_svg( $name, false );
		}
	}
}
