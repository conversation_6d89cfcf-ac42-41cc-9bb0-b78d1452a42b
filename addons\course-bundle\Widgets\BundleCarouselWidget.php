<?php
/**
 * Masteriyo bundle carousel elementor widget class.
 *
 * @package Masteriyo\Addons\CourseBundle\Widgets
 *
 * @since 2.14.0
 */

namespace Masteriyo\Addons\CourseBundle\Widgets;

use Elementor\Controls_Manager;
use Masteriyo\Enums\PostStatus;
use Masteriyo\PostType\PostType;

defined( 'ABSPATH' ) || exit;

/**
 * Masteriyo bundle carousel elementor widget class.
 *
 * @package Masteriyo\Addons\ElementorIntegration\Widgets
 *
 * @since 2.14.0
 */
class BundleCarouselWidget extends BundleListWidget {
	/**
	 * Get widget script dependencies.
	 *
	 * @since 2.14.0
	 *
	 * @return array
	 */
	public function get_script_depends() {
		return array( 'masteriyo-widget-carousel' );
	}

	/**
	 * Get widget style dependencies.
	 *
	 * @since 2.14.0
	 *
	 * @return array
	 */
	public function get_style_depends() {
		return array( 'masteriyo-widget-swiper' );
	}

	/**
	 * Get widget name.
	 *
	 * @since 2.14.0
	 *
	 * @return string
	 */
	public function get_name() {
		return 'masteriyo-bundle-carousel';
	}

	/**
	 * Get widget title.
	 *
	 * @since 2.14.0
	 *
	 * @return string
	 */
	public function get_title() {
		return __( 'Course Bundle Carousel', 'learning-management-system' );
	}

	/**
	 * Get icon class for the widget.
	 *
	 * @since 2.14.0
	 *
	 * @return string
	 */
	public function get_icon() {
		return 'masteriyo-bundle-carousel-widget-icon';
	}

	/**
	 * Register controls for configuring widget content.
	 *
	 * @since 2.14.0
	 */
	protected function register_content_controls() {
		$this->register_general_content_controls_section();
		$this->register_filter_controls_section();
		$this->register_sorting_controls_section();
		$this->register_carousel_settings_controls_section();
	}

	/**
	 * Register general content controls section.
	 *
	 * @since 2.14.0
	 */
	protected function register_general_content_controls_section() {
		$this->start_controls_section(
			'general',
			array(
				'label' => __( 'General', 'learning-management-system' ),
				'tab'   => Controls_Manager::TAB_CONTENT,
			)
		);

		$this->add_control(
			'limit',
			array(
				'label'   => __( 'Limit', 'learning-management-system' ),
				'type'    => Controls_Manager::NUMBER,
				'min'     => -1,
				'max'     => 100,
				'step'    => 1,
				'default' => 12,
			)
		);

		$this->end_controls_section();
	}

	/**
	 * Registers the carousel settings controls section for the Category Carousel widget.
	 *
	 * This method adds various controls for configuring the carousel behavior, such as
	 * enabling/disabling arrows and dots, setting the transition duration, enabling
	 * centered slides, smooth scrolling, reverse direction, autoplay, and more.
	 *
	 * @since 2.14.0
	 *
	 * The controls are added to the 'Category Carousel' section in the Elementor widget
	 * settings panel.
	 */
	protected function register_carousel_settings_controls_section() {
		$this->start_controls_section(
			'bundle_carousel_section',
			array(
				'label' => __( 'Carousel Settings' ),
				'type'  => Controls_Manager::TAB_CONTENT,
			)
		);

		$this->add_control(
			'bundle_carousel_arrows',
			array(
				'label'        => __( 'Arrows', 'learning-management-system' ),
				'type'         => Controls_Manager::SWITCHER,
				'label_on'     => __( 'Show', 'learning-management-system' ),
				'label_off'    => __( 'Hide', 'learning-management-system' ),
				'return_value' => 'yes',
				'default'      => 'yes',
			)
		);

		$this->add_control(
			'bundle_carousel_dots',
			array(
				'label'        => __( 'Dots', 'learning-management-system' ),
				'type'         => Controls_Manager::SWITCHER,
				'label_on'     => __( 'Show', 'learning-management-system' ),
				'label_off'    => __( 'Hide', 'learning-management-system' ),
				'return_value' => 'yes',
				'default'      => '',
			)
		);

		$this->add_control(
			'bundle_carousel_transition',
			array(
				'label'   => __( 'Transition Duration', 'learning-management-system' ),
				'type'    => Controls_Manager::NUMBER,
				'default' => '600',
			)
		);

		$this->add_control(
			'bundle_carousel_center_slides',
			array(
				'label'        => __( 'Centered Slides', 'learning-management-system' ),
				'type'         => Controls_Manager::SWITCHER,
				'label_on'     => __( 'Yes', 'learning-management-system' ),
				'label_off'    => __( 'No', 'learning-management-system' ),
				'return_value' => 'yes',
				'default'      => '',
			)
		);

		$this->add_control(
			'bundle_carousel_scroll',
			array(
				'label'        => __( 'Smooth Scrolling', 'learning-management-system' ),
				'type'         => Controls_Manager::SWITCHER,
				'label_on'     => __( 'Yes', 'learning-management-system' ),
				'label_off'    => __( 'No', 'learning-management-system' ),
				'return_value' => 'yes',
				'default'      => '',
			)
		);

		$this->add_control(
			'bundle_carousel_autoplay',
			array(
				'label'        => __( 'Auto Play', 'learning-management-system' ),
				'type'         => Controls_Manager::SWITCHER,
				'label_on'     => __( 'Yes', 'learning-management-system' ),
				'label_off'    => __( 'No', 'learning-management-system' ),
				'return_value' => 'yes',
				'default'      => 'yes',
			)
		);

		$this->add_control(
			'bundle_carousel_autoplay_speed',
			array(
				'label'     => __( 'Auto Play Speed', 'learning-management-system' ),
				'type'      => Controls_Manager::NUMBER,
				'default'   => 2500,
				'condition' => array(
					'bundle_carousel_autoplay' => 'yes',
				),
			)
		);

		$this->add_control(
			'bundle_carousel_reverse_direction',
			array(
				'label'        => __( 'Reserve Direction', 'learning-management-system' ),
				'type'         => Controls_Manager::SWITCHER,
				'label_on'     => __( 'Yes', 'learning-management-system' ),
				'label_off'    => __( 'No', 'learning-management-system' ),
				'return_value' => 'yes',
				'default'      => '',
				'condition'    => array(
					'bundle_carousel_autoplay' => 'yes',
				),
			)
		);

		$this->add_control(
			'bundle_carousel_infinite_loop',
			array(
				'label'        => __( 'Infinite Loop', 'learning-management-system' ),
				'type'         => Controls_Manager::SWITCHER,
				'label_on'     => __( 'Yes', 'learning-management-system' ),
				'label_off'    => __( 'No', 'learning-management-system' ),
				'return_value' => 'yes',
				'default'      => '',
			)
		);

		$this->add_control(
			'bundle_carousel_pause_onhover',
			array(
				'label'        => __( 'Pause on Hover', 'learning-management-system' ),
				'type'         => Controls_Manager::SWITCHER,
				'label_on'     => __( 'Yes', 'learning-management-system' ),
				'label_off'    => __( 'No', 'learning-management-system' ),
				'return_value' => 'yes',
				'default'      => 'yes',
			)
		);

		$this->add_control(
			'bundle_carousel_rewind',
			array(
				'label'        => __( 'Rewind', 'learning-management-system' ),
				'type'         => Controls_Manager::SWITCHER,
				'label_on'     => __( 'Yes', 'learning-management-system' ),
				'label_off'    => __( 'No', 'learning-management-system' ),
				'return_value' => 'yes',
				'default'      => 'yes',
			)
		);

		$this->add_responsive_control(
			'slides_per_view',
			array(
				'label'   => __( 'Slides Per View', 'learning-management-system' ),
				'type'    => Controls_Manager::SELECT,
				'options' => array(
					'1' => '1',
					'2' => '2',
					'3' => '3',
				),
				'min'     => 1,
				'max'     => 3,
				'step'    => 1,
				'default' => 3,
			)
		);

		$this->add_responsive_control(
			'space_between',
			array(
				'label'   => __( 'Space Between Slides', 'learning-management-system' ),
				'type'    => Controls_Manager::NUMBER,
				'default' => 0,
			)
		);

		$this->end_controls_section();
	}

	/**
	 * Render HTML for frontend.
	 *
	 * @since 2.6.7
	 */
	protected function render() {
		$settings = $this->get_settings();

		$limit     = max( absint( $settings['limit'] ), 1 );
		$columns   = max( absint( $settings['slides_per_view'] ), 1 );
		$tax_query = array(
			'relation' => 'AND',
		);

		$args = array(
			'post_type'      => PostType::COURSE_BUNDLE,
			'status'         => array( PostStatus::PUBLISH ),
			'posts_per_page' => $limit,
			'order'          => 'DESC',
			'orderby'        => 'date',
			'tax_query'      => $tax_query,
		);

		if ( ! empty( $settings['include_instructors'] ) ) {
			$args['author__in'] = $settings['include_instructors'];
		}

		if ( ! empty( $settings['exclude_instructors'] ) ) {
			$args['author__not_in'] = $settings['exclude_instructors'];
		}

		$order = strtoupper( $settings['sorting_order'] );

		switch ( $settings['order_by'] ) {
			case 'date':
				$args['orderby'] = 'date';
				$args['order']   = ( 'ASC' === $order ) ? 'ASC' : 'DESC';
				break;

			case 'price':
				$args['orderby']  = 'meta_value_num';
				$args['meta_key'] = '_price';
				$args['order']    = ( 'DESC' === $order ) ? 'DESC' : 'ASC';
				break;

			case 'title':
				$args['orderby'] = 'title';
				$args['order']   = ( 'DESC' === $order ) ? 'DESC' : 'ASC';
				break;

			default:
				$args['orderby'] = 'date';
				$args['order']   = ( 'ASC' === $order ) ? 'ASC' : 'DESC';
				break;
		}

		$bundles_query = new \WP_Query( $args );
		$bundles       = array_filter( array_map( 'masteriyo_get_course_bundle', $bundles_query->posts ) );

		$show_carousel_arrows    = 'yes' === $settings['bundle_carousel_arrows'] ? true : false;
		$show_carousel_dots      = 'yes' === $settings['bundle_carousel_dots'] ? true : false;
		$show_carousel_scrollbar = 'yes' === $settings['bundle_carousel_scroll'] ? true : false;

		$slider_data = array(
			'columns'           => $columns,
			'space_between'     => isset( $settings['space_between'] ) ? absint( $settings['space_between'] ) : 0,
			'reverse_direction' => 'yes' === $settings['bundle_carousel_reverse_direction'],
			'delay'             => $settings['bundle_carousel_autoplay_speed'],
			'infinite_loop'     => 'yes' === $settings['bundle_carousel_infinite_loop'],
			'autoplay'          => 'yes' === $settings['bundle_carousel_autoplay'],
			'speed'             => $settings['bundle_carousel_transition'],
			'navigation'        => 'yes' === $settings['bundle_carousel_arrows'],
			'pagination'        => 'yes' === $settings['bundle_carousel_dots'],
			'centeredSlides'    => 'yes' === $settings['bundle_carousel_center_slides'],
			'pauseOnHover'      => 'yes' === $settings['bundle_carousel_pause_onhover'],
			'scrollbar'         => 'yes' === $settings['bundle_carousel_scroll'],
			'rewind'            => 'yes' === $settings['bundle_carousel_rewind'],
			'breakpoints'       => array(
				320  => array(
					'slidesPerView' => isset( $settings['slides_per_view_mobile'] ) ? absint( $settings['slides_per_view_mobile'] ) : 1,
					'spaceBetween'  => isset( $settings['space_between_mobile'] ) ? absint( $settings['space_between_mobile'] ) : 0,
				),
				768  => array(
					'slidesPerView' => isset( $settings['slides_per_view_tablet'] ) ? absint( $settings['slides_per_view_tablet'] ) : 2,
					'spaceBetween'  => isset( $settings['space_between_tablet'] ) ? absint( $settings['space_between_tablet'] ) : 0,
				),
				1024 => array(
					'slidesPerView' => isset( $settings['slides_per_view'] ) ? absint( $settings['slides_per_view'] ) : 3,
					'spaceBetween'  => isset( $settings['space_between'] ) ? absint( $settings['space_between'] ) : 0,
				),
			),
		);

		add_filter( 'masteriyo_is_course_bundle_carousel_enabled', '__return_true' );

		printf( '<div class="masteriyo-bundle-list-display-section masteriyo-course-bundle-carousel" data-settings="%s">', esc_attr( wp_json_encode( $slider_data ) ) );

		masteriyo_set_loop_prop( 'columns', $columns );

		if ( count( $bundles ) > 0 ) {
			$original_bundle = isset( $GLOBALS['course_bundle'] ) ? $GLOBALS['course_bundle'] : null;

			masteriyo_course_bundle_loop_start();

			foreach ( $bundles as $bundle ) {
				$GLOBALS['course_bundle'] = $bundle;
				$card_class               = empty( $settings['card_hover_animation'] ) ? '' : sprintf( 'elementor-animation-%s', $settings['card_hover_animation'] );

				masteriyo_get_template(
					'course-bundle/content-course-bundle.php',
					array(
						'card_class' => $card_class,
					)
				);
			}

			$GLOBALS['course_bundle'] = $original_bundle;

			masteriyo_course_bundle_loop_end();
			masteriyo_reset_loop();

			if ( $show_carousel_scrollbar ) :
				?>
					<div class="swiper-scrollbar"></div>
				<?php
			endif;

			if ( $show_carousel_arrows ) :
				?>
				<div class="swiper-button-next"></div>
				<div class="swiper-button-prev"></div>
				<?php
			endif;

			if ( $show_carousel_dots ) :
				?>
				<div class="swiper-pagination"></div>
				<?php
			endif;
		}
		echo '</div>';

		remove_filter( 'masteriyo_is_course_bundle_carousel_enabled', '__return_false' );
	}
}
