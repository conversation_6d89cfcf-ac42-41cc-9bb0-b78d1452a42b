<?php
/**
 * Store global Razorpay options.
 *
 * @since 2.7.1
 * @package \Masteriyo\Addons\Razorpay
 */

namespace Masteriyo\Addons\Razorpay;

defined( 'ABSPATH' ) || exit;


class Setting {

	/**
	 * Global option name.
	 *
	 * @since 2.7.1
	 */
	const OPTION_NAME = 'masteriyo_razorpay_settings';

	/**
	 * Data.
	 *
	 * @since 2.7.1
	 *
	 * @var array
	 */
	protected static $data = array(
		'enable'               => false,
		'title'                => 'Razorpay',
		'sandbox'              => true,
		'description'          => 'Pay with Razorpay',
		'test_publishable_key' => '',
		'test_secret_key'      => '',
		'live_publishable_key' => '',
		'live_secret_key'      => '',
	);

	/**
	 * Read the settings.
	 *
	 * @since 2.7.1
	 */
	protected static function read() {
		$settings   = get_option( self::OPTION_NAME, self::$data );
		self::$data = masteriyo_parse_args( $settings, self::$data );

		return self::$data;
	}

	/**
	 * Return all the settings.
	 *
	 * @since 2.7.1
	 *
	 * @return mixed
	 */
	public static function all() {
		return self::read();
	}

	/**
	 * Return global Razorpay field value.
	 *
	 * @since 2.7.1
	 *
	 * @param string $key
	 *
	 * @return string|array
	 */
	public static function get( $key ) {
		self::read();

		return masteriyo_array_get( self::$data, $key, null );
	}

	/**
	 * Set global Razorpay field.
	 *
	 * @since 2.7.1
	 *
	 * @param string $key Setting key.
	 * @param mixed $value Setting value.
	 */
	public static function set( $key, $value ) {
		masteriyo_array_set( self::$data, $key, $value );
		self::save();
	}

	/**
	 * Set multiple settings.
	 *
	 * @since 2.7.1
	 *
	 * @param array $args
	 */
	public static function set_props( $args ) {
		self::$data = masteriyo_parse_args( $args, self::$data );
	}

	/**
	 * Save the settings.
	 *
	 * @since 2.7.1
	 */
	public static function save() {
		update_option( self::OPTION_NAME, self::$data );
	}
}
