<?php
/**
 * EDD Integration helper functions.
 *
 * @since 2.6.8
 * @package Masteriyo\Addons\EDDIntegration
 */

namespace Masteriyo\Addons\EDDIntegration;

defined( 'ABSPATH' ) || exit;


class Helper {

	/**
	 * Return if Easy Digital Downloads is active.
	 *
	 * @since 2.6.8
	 *
	 * @return boolean
	 */
	public static function is_edd_active() {
		if ( masteriyo_check_plugin_active_in_network( 'easy-digital-downloads/easy-digital-downloads.php' ) || masteriyo_check_plugin_active_in_network( 'easy-digital-downloads-pro/easy-digital-downloads.php' ) ) {
			return true;
		}
		$active_plugins = get_option( 'active_plugins', array() );
		return masteriyo_array_has_any(
			array_flip( $active_plugins ),
			array(
				'easy-digital-downloads/easy-digital-downloads.php',
				'easy-digital-downloads-pro/easy-digital-downloads.php',
			)
		);
	}
}
