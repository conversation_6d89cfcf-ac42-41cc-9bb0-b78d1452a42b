<?php

defined( 'ABSPATH' ) || exit;

/**
 * Addon Name: Course Bundle
 * Addon URI: https://masteriyo.com/wordpress-lms/
 * Description: Bundle and sell multiple courses as a package with Course Bundle, offering learners a discounted set of related courses.
 * Author: Masteriyo
 * Author URI: https://masteriyo.com
 * Addon Type: feature
 * Plan: Starter,Pro,Elite,Growth,Scale
 */

use Masteriyo\Pro\Addons;

define( 'MASTERIYO_COURSE_BUNDLE_ADDON_FILE', __FILE__ );
define( 'MASTERIYO_COURSE_BUNDLE_ADDON_BASENAME', plugin_basename( __FILE__ ) );
define( 'MASTERIYO_COURSE_BUNDLE_ADDON_DIR', __DIR__ );
define( 'MASTERIYO_COURSE_BUNDLE_ADDON_SLUG', 'course-bundle' );
define( 'MASTERIYO_COURSE_BUNDLE_TEMPLATES', __DIR__ . '/templates' );

if ( ! ( new Addons() )->is_active( MASTERIYO_COURSE_BUNDLE_ADDON_SLUG ) ) {
	return;
}

require_once __DIR__ . '/helper/course-bundle.php';
require_once __DIR__ . '/helper/Template.php';
require_once __DIR__ . '/helper/TemplateHooks.php';

/**
 * Include service providers for course bundle.
 */
add_filter(
	'masteriyo_service_providers',
	function( $providers ) {
		return array_merge( $providers, require_once __DIR__ . '/config/providers.php' );
	}
);

/**
 * Initialize Masteriyo course bundle.
 */
add_action(
	'masteriyo_before_init',
	function() {
		masteriyo( 'addons.course-bundle' )->init();
	}
);
