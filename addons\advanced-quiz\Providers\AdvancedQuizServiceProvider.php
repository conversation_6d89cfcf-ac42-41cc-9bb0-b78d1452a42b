<?php
/**
 * Advanced Quiz service provider.
 *
 * @since 2.4.0
 * @package \Masteriyo\Addons\AdvancedQuiz
 */

namespace Masteriyo\Addons\AdvancedQuiz\Providers;

use Masteriyo\Addons\AdvancedQuiz\Audio;
use Masteriyo\Addons\AdvancedQuiz\Video;
use Masteriyo\Addons\AdvancedQuiz\AdvancedQuizAddon;
use Masteriyo\Addons\AdvancedQuiz\Models\FillInTheBlanks;
use League\Container\ServiceProvider\AbstractServiceProvider;

defined( 'ABSPATH' ) || exit;


/**
 * Advanced Quiz service provider.
 *
 * @since 2.4.0
 */
class AdvancedQuizServiceProvider extends AbstractServiceProvider {
	/**
	 * The provided array is a way to let the container
	 * know that a service is provided by this service
	 * provider. Every service that is registered
	 * this service provider must have an alias added
	 * to this array or it will be ignored
	 *
	 * @since 2.4.0
	 *
	 * @var array
	 */
	protected $provides = array(
		'addons.advanced-quiz',
		'\Masteriyo\Addons\AdvancedQuiz\AdvancedQuizAddon',
		'mto-question.audio',
		'mto-question.video',
		'mto-question.fill-in-the-blanks',

	);

	/**
	 * This is where the magic happens, within the method you can
	 * access the container and register or retrieve anything
	 * that you need to, but remember, every alias registered
	 * within this method must be declared in the `$provides` array.
	 *
	 * @since 2.4.0
	 */
	public function register() {
		$this->getContainer()->add( 'addons.advanced-quiz', AdvancedQuizAddon::class, true );

		$this->getContainer()->add( 'question.audio', Audio::class )
			->addArgument( 'question.store' );

		$this->getContainer()->add( 'question.video', Video::class )
			->addArgument( 'question.store' );

		$this->getContainer()->add( 'question.fill-in-the-blanks', FillInTheBlanks::class )
			->addArgument( 'question.store' );

		// Register based on post type.
		$this->getContainer()->add( 'mto-question.audio', Audio::class )
			->addArgument( 'mto-question.store' );

		$this->getContainer()->add( 'mto-question.video', Video::class )
			->addArgument( 'mto-question.store' );

		$this->getContainer()->add( 'mto-question.fill-in-the-blanks', FillInTheBlanks::class )
			->addArgument( 'question.store' );
	}
}
