<?php
/**
 * Coupons Controller class.
 *
 * @since 2.5.12
 *
 * @package Masteriyo\Addons\Coupons
 */

namespace Masteriyo\Addons\Coupons\Controllers;

defined( 'ABSPATH' ) || exit;

use Masteriyo\Addons\Coupons\Enums\AppliesToType;
use Masteriyo\Addons\Coupons\Enums\CouponDiscountType;
use Masteriyo\Addons\Coupons\Enums\CouponStatus;
use Masteriyo\Addons\Coupons\Enums\DeductionMethodType;
use Masteriyo\Helper\Permission;
use Masteriyo\PostType\PostType;
use Masteriyo\RestApi\Controllers\Version1\PostsController;

/**
 * CouponsController class.
 */
class CouponsController extends PostsController {
	/**
	 * Endpoint namespace.
	 *
	 * @var string
	 */
	protected $namespace = 'masteriyo/pro/v1';

	/**
	 * Route base.
	 *
	 * @var string
	 */
	protected $rest_base = 'coupons';

	/**
	 * Post type.
	 *
	 * @var string
	 */
	protected $post_type = PostType::COUPON;

	/**
	 * Object type.
	 *
	 * @var string
	 */
	protected $object_type = 'coupon';

	/**
	 * Permission class.
	 *
	 * @since 2.5.12
	 *
	 * @var Masteriyo\Helper\Permission;
	 */
	protected $permission = null;

	/**
	 * Constructor.
	 *
	 * @since 2.5.12
	 *
	 * @param Permission $permission
	 */
	public function __construct( ?Permission $permission = null ) {
		$this->permission = $permission;
	}

	/**
	 * Register routes.
	 *
	 * @since 2.5.12
	 *
	 * @return void
	 */
	public function register_routes() {
		register_rest_route(
			$this->namespace,
			'/' . $this->rest_base,
			array(
				array(
					'methods'             => \WP_REST_Server::READABLE,
					'callback'            => array( $this, 'get_items' ),
					'permission_callback' => array( $this, 'get_items_permissions_check' ),
					'args'                => $this->get_collection_params(),
				),
				array(
					'methods'             => \WP_REST_Server::CREATABLE,
					'callback'            => array( $this, 'create_item' ),
					'permission_callback' => array( $this, 'create_item_permissions_check' ),
					'args'                => $this->get_endpoint_args_for_item_schema( \WP_REST_Server::CREATABLE ),
				),
			)
		);

		register_rest_route(
			$this->namespace,
			'/' . $this->rest_base . '/(?P<id>[\d]+)',
			array(
				'args'   => array(
					'id' => array(
						'description' => __( 'Unique identifier for the resource.', 'learning-management-system' ),
						'type'        => 'integer',
					),
				),
				array(
					'methods'             => \WP_REST_Server::READABLE,
					'callback'            => array( $this, 'get_item' ),
					'permission_callback' => array( $this, 'get_item_permissions_check' ),
					'args'                => array(
						'context' => $this->get_context_param(
							array(
								'default' => 'view',
							)
						),
					),
				),
				array(
					'methods'             => \WP_REST_Server::EDITABLE,
					'callback'            => array( $this, 'update_item' ),
					'permission_callback' => array( $this, 'update_item_permissions_check' ),
					'args'                => $this->get_endpoint_args_for_item_schema( \WP_REST_Server::EDITABLE ),
				),
				array(
					'methods'             => \WP_REST_Server::DELETABLE,
					'callback'            => array( $this, 'delete_item' ),
					'permission_callback' => array( $this, 'delete_item_permissions_check' ),
					'args'                => array(
						'force' => array(
							'default'     => false,
							'description' => __( 'Whether to bypass trash and force deletion.', 'learning-management-system' ),
							'type'        => 'boolean',
						),
					),
				),
				'schema' => array( $this, 'get_public_item_schema' ),
			)
		);

		register_rest_route(
			$this->namespace,
			'/' . $this->rest_base . '/delete',
			array(
				array(
					'methods'             => \WP_REST_Server::DELETABLE,
					'callback'            => array( $this, 'delete_items' ),
					'permission_callback' => array( $this, 'delete_items_permissions_check' ),
					'args'                => array(
						'ids'      => array(
							'required'    => true,
							'description' => __( 'Coupon IDs.', 'learning-management-system' ),
							'type'        => 'array',
						),
						'force'    => array(
							'default'     => false,
							'description' => __( 'Whether to bypass trash and force deletion.', 'learning-management-system' ),
							'type'        => 'boolean',
						),
						'children' => array(
							'default'     => false,
							'description' => __( 'Whether to delete the children(sections, lessons, quizzes and questions) under the course.', 'learning-management-system' ),
							'type'        => 'boolean',
						),
					),
				),
			)
		);

		register_rest_route(
			$this->namespace,
			'/' . $this->rest_base . '/restore',
			array(
				array(
					'methods'             => \WP_REST_Server::CREATABLE,
					'callback'            => array( $this, 'restore_items' ),
					'permission_callback' => array( $this, 'delete_items_permissions_check' ),
					'args'                => array(
						'ids' => array(
							'required'    => true,
							'description' => __( 'Coupon Ids', 'learning-management-system' ),
							'type'        => 'array',
						),
					),
				),
			)
		);

		register_rest_route(
			$this->namespace,
			'/' . $this->rest_base . '/(?P<id>[\d]+)/restore',
			array(
				'args' => array(
					'id' => array(
						'description' => __( 'Unique identifier for the resource.', 'learning-management-system' ),
						'type'        => 'integer',
					),
				),
				array(
					'methods'             => \WP_REST_Server::CREATABLE,
					'callback'            => array( $this, 'restore_item' ),
					'permission_callback' => array( $this, 'delete_item_permissions_check' ),
					'args'                => array(
						'context' => $this->get_context_param(
							array(
								'default' => 'view',
							)
						),
					),
				),
			)
		);
	}

	/**
	 * Get object.
	 *
	 * @since 2.5.12
	 *
	 * @param int|\Masteriyo\Database\Model|\WP_Post $object Object ID or Model or WP_Post object.
	 *
	 * @return false|\Masteriyo\Addons\Coupons\Models\Coupon
	 */
	protected function get_object( $object ) {
		try {
			if ( is_int( $object ) ) {
				$id = $object;
			} else {
				$id = is_a( $object, \WP_Post::class ) ? $object->ID : $object->get_id();
			}

			$coupon = masteriyo_create_coupon_object();
			$coupon->set_id( $id );
			$coupon_repo = masteriyo_create_coupon_store();
			$coupon_repo->read( $coupon );
		} catch ( \Exception $e ) {
			return false;
		}

		return $coupon;
	}


	/**
	 * Prepares the object for the REST response.
	 *
	 * @since  2.5.12
	 *
	 * @param  Masteriyo\Database\Model $object  Model object.
	 * @param  WP_REST_Request $request Request object.
	 *
	 * @return WP_Error|WP_REST_Response Response object on success, or WP_Error object on failure.
	 */
	protected function prepare_object_for_response( $object, $request ) {
		$context = ! empty( $request['context'] ) ? $request['context'] : 'view';
		$data    = $this->get_coupon_data( $object, $context );

		$data     = $this->add_additional_fields_to_object( $data, $request );
		$data     = $this->filter_response_by_context( $data, $context );
		$response = rest_ensure_response( $data );
		$response->add_links( $this->prepare_links( $object, $request ) );

		/**
		 * Filter the data for a response.
		 *
		 * The dynamic portion of the hook name, $this->post_type,
		 * refers to object type being prepared for the response.
		 *
		 * @since 2.5.12
		 *
		 * @param WP_REST_Response $response The response object.
		 * @param Masteriyo\Database\Model $object   Object data.
		 * @param WP_REST_Request  $request  Request object.
		 */
		return apply_filters( "masteriyo_rest_prepare_{$this->object_type}_object", $response, $object, $request );
	}

	/**
	 * Get coupon data.
	 *
	 * @since 2.5.12
	 *
	 * @param \Masteriyo\Addons\Coupons\Models\Coupon $coupon coupon instance.
	 * @param string  $context Request context.
	 *                         Options: 'view' and 'edit'.
	 *
	 * @return array
	 */
	protected function get_coupon_data( $coupon, $context = 'view' ) {
		$author = masteriyo_get_user( $coupon->get_author_id( $context ) );

		$author = is_wp_error( $author ) ? null : array(
			'id'           => $author->get_id(),
			'display_name' => $author->get_display_name(),
			'avatar_url'   => $author->get_avatar_url(),
		);

		$data = array(
			'id'                     => $coupon->get_id(),
			'method'                 => $coupon->get_method( $context ),
			'applies_to'             => $coupon->get_applies_to( $context ),
			'status'                 => $coupon->get_dynamic_status(),
			'description'            => 'view' === $context ? $coupon->get_description() : $coupon->get_description( $context ),
			'author'                 => $author,
			'discount_type'          => $coupon->get_discount_type( $context ),
			'discount_amount'        => $coupon->get_discount_amount( $context ),
			'usage_limit_per_user'   => $coupon->get_usage_limit_per_user( $context ),
			'usage_limit_per_coupon' => $coupon->get_usage_limit_per_coupon( $context ),
			'usage_count'            => $coupon->get_usage_count( $context ),
			'created_at'             => masteriyo_rest_prepare_date_response( $coupon->get_created_at( $context ) ),
			'modified_at'            => masteriyo_rest_prepare_date_response( $coupon->get_modified_at( $context ) ),
			'start_at'               => masteriyo_rest_prepare_date_response( $coupon->get_start_at( $context ) ),
			'expire_at'              => masteriyo_rest_prepare_date_response( $coupon->get_expire_at( $context ) ),
			'expired'                => $coupon->is_expired(),
			'stackable'              => $coupon->get_stackable(),
		);

		if ( DeductionMethodType::AUTOMATIC !== $coupon->get_method( $context ) ) {
			$data['code'] = wp_specialchars_decode( $coupon->get_code( $context ) );
		}

		/**
		 * Filter coupon rest response data.
		 *
		 * @since 2.5.12
		 *
		 * @param array $data Coupon data.
		 * @param \Masteriyo\Addons\Coupons\Models\Coupon $coupon Coupon object.
		 * @param string $context What the value is for. Valid values are view and edit.
		 * @param \Masteriyo\RestApi\Controllers\Version1\CouponsController $controller REST Coupons controller object.
		 */
		return apply_filters( "masteriyo_rest_response_{$this->object_type}_data", $data, $coupon, $context, $this );
	}

	/**
	 * Get the coupons'schema, conforming to JSON Schema.
	 *
	 * @since 2.5.12
	 *
	 * @return array
	 */
	public function get_item_schema() {
		$schema = array(
			'$schema'    => 'http://json-schema.org/draft-04/schema#',
			'title'      => $this->object_type,
			'type'       => 'object',
			'properties' => array(
				'id'                     => array(
					'description' => __( 'Unique identifier for the resource.', 'learning-management-system' ),
					'type'        => 'integer',
					'context'     => array( 'view', 'edit' ),
					'readonly'    => true,
				),
				'code'                   => array(
					'description' => __( 'Coupon code', 'learning-management-system' ),
					'type'        => 'string',
					'required'    => false,
					'context'     => array( 'view', 'edit' ),
				),
				'method'                 => array(
					'description' => __( 'Deduction Method.', 'learning-management-system' ),
					'type'        => 'string',
					'default'     => DeductionMethodType::CODE,
					'enum'        => DeductionMethodType::all(),
					'context'     => array( 'view', 'edit' ),
				),
				'stackable'              => array(
					'description' => __( 'Coupon is stackable.', 'learning-management-system' ),
					'type'        => 'string',
					'default'     => 'no',
					'enum'        => array( 'yes', 'no' ),
					'context'     => array( 'view', 'edit' ),
				),
				'title'                  => array(
					'description' => __( 'Coupon title.', 'learning-management-system' ),
					'type'        => 'string',
					'context'     => array( 'view', 'edit' ),
				),
				'applies_to'             => array(
					'description' => __( 'Coupon applies to.', 'learning-management-system' ),
					'type'        => 'object',
					'properties'  => array(
						'type'   => array(
							'description' => __( 'Coupon applies to type.', 'learning-management-system' ),
							'type'        => 'string',
							'default'     => AppliesToType::ALL,
							'context'     => array( 'view', 'edit' ),
							'required'    => true,
						),
						'target' => array(
							'description' => __( 'Coupon applies to specific targets.', 'learning-management-system' ),
							'type'        => 'object',
							'properties'  => array(
								'id'   => array(
									'description' => __( 'List of specific courses to which the coupon applies.', 'learning-management-system' ),
									'type'        => 'integer',
									'context'     => array( 'view', 'edit' ),
								),
								'name' => array(
									'description' => __( 'List of specific bundles to which the coupon applies.', 'learning-management-system' ),
									'type'        => 'string',
									'context'     => array( 'view', 'edit' ),
								),
							),
							'required'    => false,
						),
					),
					'context'     => array( 'view', 'edit' ),
				),
				'status'                 => array(
					'description' => __( 'Coupon status.', 'learning-management-system' ),
					'type'        => 'string',
					'default'     => CouponStatus::ACTIVE,
					'enum'        => CouponStatus::all(),
					'context'     => array( 'view', 'edit' ),
				),
				'author_id'              => array(
					'description' => __( 'Coupon author ID.', 'learning-management-system' ),
					'type'        => 'integer',
					'context'     => array( 'view', 'edit' ),
				),
				'description'            => array(
					'description' => __( 'Coupon description', 'learning-management-system' ),
					'type'        => 'string',
					'context'     => array( 'view', 'edit' ),
				),
				'discount_type'          => array(
					'description' => __( 'Coupon discount type', 'learning-management-system' ),
					'type'        => 'string',
					'default'     => CouponDiscountType::FIXED_CART,
					'enum'        => CouponDiscountType::all(),
					'context'     => array( 'view', 'edit' ),
				),
				'discount_amount'        => array(
					'description' => __( 'Coupon discount amount', 'learning-management-system' ),
					'type'        => 'number',
					'context'     => array( 'view', 'edit' ),
				),
				'usage_limit_per_user'   => array(
					'description' => __( 'Coupon usage limit per user', 'learning-management-system' ),
					'type'        => 'integer',
					'context'     => array( 'view', 'edit' ),
				),
				'usage_limit_per_coupon' => array(
					'description' => __( 'Coupon usage limit per coupon', 'learning-management-system' ),
					'type'        => 'integer',
					'context'     => array( 'view', 'edit' ),
				),
				'start_at'               => array(
					'description' => __( 'The date the coupon will be active, as GMT in ISO8601 compliant date .', 'learning-management-system' ),
					'type'        => 'string',
					'context'     => array( 'view', 'edit' ),
				),
				'expire_at'              => array(
					'description' => __( 'The date the coupon will expire, as GMT in ISO8601 compliant date.', 'learning-management-system' ),
					'type'        => 'string',
					'context'     => array( 'view', 'edit' ),
				),
				'created_at'             => array(
					'description' => __( 'The date the coupon was created, in the UTC.', 'learning-management-system' ),
					'type'        => 'string',
					'context'     => array( 'view', 'edit' ),
				),
				'created_at_gmt'         => array(
					'description' => __( 'The date the coupon was created, as GMT.', 'learning-management-system' ),
					'type'        => 'string',
					'context'     => array( 'view', 'edit' ),
					'readonly'    => true,
				),
				'modified_at'            => array(
					'description' => __( "The date the coupon was last modified, in the site's timezone.", 'learning-management-system' ),
					'type'        => 'string',
					'context'     => array( 'view', 'edit' ),
				),
				'modified_at_gmt'        => array(
					'description' => __( 'The date the coupon was last modified, as GMT.', 'learning-management-system' ),
					'type'        => 'string',
					'context'     => array( 'view', 'edit' ),
					'readonly'    => true,
				),
				'meta_data'              => array(
					'description' => __( 'Meta data', 'learning-management-system' ),
					'type'        => 'array',
					'context'     => array( 'view', 'edit' ),
					'items'       => array(
						'type'       => 'object',
						'properties' => array(
							'id'    => array(
								'description' => __( 'Meta ID', 'learning-management-system' ),
								'type'        => 'integer',
								'context'     => array( 'view', 'edit' ),
								'readonly'    => true,
							),
							'key'   => array(
								'description' => __( 'Meta key', 'learning-management-system' ),
								'type'        => 'string',
								'context'     => array( 'view', 'edit' ),
							),
							'value' => array(
								'description' => __( 'Meta value', 'learning-management-system' ),
								'type'        => 'mixed',
								'context'     => array( 'view', 'edit' ),
							),
						),
					),
				),
			),
		);

			return $this->add_additional_fields_schema( $schema );
	}

	/**
	 * Prepare a single coupon for create or update.
	 *
	 * @since 2.5.12
	 *
	 * @param WP_REST_Request $request Request object.
	 * @param bool            $creating If is creating a new object.
	 *
	 * @return \WP_Error|Masteriyo\Database\Model
	 */
	protected function prepare_object_for_database( $request, $creating = false ) {
		$id     = isset( $request['id'] ) ? absint( $request['id'] ) : 0;
		$coupon = masteriyo_create_coupon_object();

		if ( 0 !== $id ) {
			$coupon->set_id( $id );
			$coupon_repo = masteriyo_create_coupon_store();
			$coupon_repo->read( $coupon );
		}

		// Validate and set the method (only for new coupons)
		if ( ! $id ) {
			$method = isset( $request['method'] ) ? sanitize_text_field( $request['method'] ) : '';

			if ( ! in_array( $method, DeductionMethodType::all(), true ) ) {
				return new \WP_Error(
					'masteriyo_coupon_invalid_method',
					__( 'Invalid coupon method.', 'learning-management-system' ),
					array( 'status' => 400 )
				);
			}

			$coupon->set_method( $method );
		} else {
			$method = $coupon->get_method();
		}

		// If method is "code", validate and set the coupon code
		if ( ! $id && DeductionMethodType::CODE === $method ) {
			if ( ! isset( $request['code'] ) || empty( $request['code'] ) ) {
				return new \WP_Error(
					'masteriyo_coupon_code_required',
					__( 'Coupon code is required.', 'learning-management-system' ),
					array( 'status' => 400 )
				);
			}
		}

		// Coupon code.
		if ( DeductionMethodType::CODE === $request['method'] && isset( $request['code'] ) ) {
			$coupon_code = sanitize_text_field( $request['code'] );

			if ( $coupon->get_code() !== $coupon_code && masteriyo_get_coupon_by_code( $coupon_code ) ) {
				return new \WP_Error(
					'masteriyo_coupon_already_exists',
					__( 'Coupon code already exists. Please use a different coupon code.', 'learning-management-system' ),
					array(
						'status' => 409,
					)
				);
			}

			$coupon->set_code( $coupon_code );
		}

		if ( ! $id && isset( $request['method'] ) && DeductionMethodType::AUTOMATIC === $request['method'] ) {
			$coupon->set_code( time() );
		}

		// Applies to.
		if ( isset( $request['applies_to'] ) ) {
			$coupon->set_applies_to( $request['applies_to'] );
		}

		// Coupon author ID.
		if ( isset( $request['author_id'] ) ) {
			$coupon->set_author_id( $request['author_id'] );
		}

		// Coupon content.
		if ( isset( $request['description'] ) ) {
			$coupon->set_description( sanitize_textarea_field( $request['description'] ) );
		}

		// Coupon status.
		if ( isset( $request['status'] ) ) {
			$coupon->set_status( $request['status'] );
		}

		// Coupon discount type.
		if ( isset( $request['discount_type'] ) ) {
			$coupon->set_discount_type( $request['discount_type'] );
		}

		// Coupon stackable.
		if ( isset( $request['stackable'] ) ) {
			$coupon->set_stackable( $request['stackable'] );
		}

		// Coupon discount amount.
		if ( isset( $request['discount_amount'] ) ) {
			$coupon->set_discount_amount( $request['discount_amount'] );
		}

		// Coupon usage limit per user.
		if ( isset( $request['usage_limit_per_user'] ) ) {
			$coupon->set_usage_limit_per_user( $request['usage_limit_per_user'] );
		}

		// Coupon usage limit per coupon.
		if ( isset( $request['usage_limit_per_coupon'] ) ) {
			$coupon->set_usage_limit_per_coupon( $request['usage_limit_per_coupon'] );
		}

		// Coupon usage create at.
		if ( isset( $request['created_at'] ) ) {
			$coupon->set_created_at( $request['created_at'] );
		}

		// Coupon usage create at.
		if ( isset( $request['modified_at'] ) ) {
			$coupon->set_modified_at( $request['modified_at'] );
		}

		// Coupon usage start at.
		if ( isset( $request['start_at'] ) ) {
			$coupon->set_start_at( $request['start_at'] );
		}

		// Coupon usage expire at.
		if ( isset( $request['expire_at'] ) ) {
			$coupon->set_expire_at( $request['expire_at'] );
		}

		// Allow set meta_data.
		if ( isset( $request['meta_data'] ) && is_array( $request['meta_data'] ) ) {
			foreach ( $request['meta_data'] as $meta ) {
				$coupon->update_meta_data( $meta['key'], $meta['value'], isset( $meta['id'] ) ? $meta['id'] : '' );
			}
		}

		/**
		 * Filters an object before it is inserted via the REST API.
		 *
		 * The dynamic portion of the hook name, `$this->post_type`,
		 * refers to the object type slug.
		 *
		 * @since 2.5.12
		 *
		 * @param Masteriyo\Database\Model $coupon Coupon object.
		 * @param WP_REST_Request $request  Request object.
		 * @param bool            $creating If is creating a new object.
		 */
		return apply_filters( "masteriyo_rest_pre_insert_{$this->post_type}_object", $coupon, $request, $creating );
	}

	/**
	 * Prepare objects query.
	 *
	 * @param WP_REST_Request $request Full details about the request.
	 *
	 * @since  2.5.12
	 * @return array
	 */
	protected function prepare_objects_query( $request ) {
		$args = parent::prepare_objects_query( $request );

		$args['post_status'] = $request['status'];

		if ( masteriyo_is_current_user_instructor() ) {
			$args['author'] = get_current_user_id();
		}

		return $args;
	}

	/**
	 * Process objects collection.
	 *
	 * @since 2.5.12
	 *
	 * @param array $objects Coupons data.
	 * @param array $query_args Query arguments.
	 * @param array $query_results Coupons query result data.
	 *
	 * @return array
	 */
	protected function process_objects_collection( $objects, $query_args, $query_results ) {
		return array(
			'data' => $objects,
			'meta' => array(
				'total'         => $query_results['total'],
				'pages'         => $query_results['pages'],
				'current_page'  => $query_args['paged'],
				'per_page'      => $query_args['posts_per_page'],
				'coupons_count' => $this->get_coupons_count(),
			),
		);
	}

	/**
	 * Get coupons count by status.
	 *
	 * @since 2.5.12
	 *
	 * @return Array
	 */
	protected function get_coupons_count() {
		$post_count = parent::get_posts_count();

		return masteriyo_array_only( $post_count, array_merge( array( CouponStatus::ANY ), CouponStatus::all() ) );
	}

	/**
	 * Get the query params for collections of attachments.
	 *
	 * @since 2.5.12
	 *
	 * @return array
	 */
	public function get_collection_params() {
		$params = parent::get_collection_params();

		$params['status'] = array(
			'default'           => CouponStatus::ANY,
			'description'       => __( 'Limit result set to coupons assigned a specific status.', 'learning-management-system' ),
			'type'              => 'string',
			'enum'              => array_merge( CouponStatus::all(), array( CouponStatus::ANY ) ),
			'sanitize_callback' => 'sanitize_key',
			'validate_callback' => 'rest_validate_request_arg',
		);

		return $params;
	}

	/**
	 * Restore coupon.
	 *
	 * @since 2.5.12
	 *
	 * @param \WP_REST_Request $request Full details about the request.
	 *
	 * @return \WP_Error|\WP_REST_Response
	 */
	public function restore_item( $request ) {
		$object = $this->get_object( (int) $request['id'] );

		if ( ! $object || 0 === $object->get_id() ) {
			return new \WP_Error( "masteriyo_rest_{$this->post_type}_invalid_id", __( 'Invalid ID.', 'learning-management-system' ), array( 'status' => 404 ) );
		}

		wp_untrash_post( $object->get_id() );

		$object   = $this->get_object( (int) $request['id'] );// Read object again.
		$data     = $this->prepare_object_for_response( $object, $request );
		$response = rest_ensure_response( $data );

		if ( $this->public ) {
			$response->link_header( 'alternate', $this->get_permalink( $object ), array( 'type' => 'text/html' ) );
		}

		return $response;
	}

	/**
	 * Prepare objects query for batch.
	 *
	 * @param WP_REST_Request $request Full details about the request.
	 *
	 * @since  2.6.5
	 * @return array
	 */
	protected function prepare_objects_query_for_batch( $request ) {
		$query_args = array(
			'post_status'    => CouponStatus::all(),
			'post_type'      => $this->post_type,
			'post__in'       => wp_parse_id_list( $request['ids'] ),
			'posts_per_page' => -1,
		);

		/**
		 * Filters objects query for batch operation.
		 *
		 * @since 2.6.5
		 *
		 * @param array $query_args Query arguments.
		 * @param WP_REST_Request $request
		 * @param \Masteriyo\RestApi\Controllers\Version1\PostsController $controller
		 */
		return apply_filters( 'masteriyo_rest_objects_query_for_batch', $query_args, $request, $this );
	}
}
