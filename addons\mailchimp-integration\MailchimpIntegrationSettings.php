<?php

/**
 * Mailchimp integration settings class.
 *
 * @package Masteriyo\Addons\MailchimpIntegration
 *
 * @since 2.18.0
 */

namespace Masteriyo\Addons\MailchimpIntegration;

defined( 'ABSPATH' ) || exit;


use Masteriyo\EmailMarketingAndCRM\IntegrationSettings;

/**
 * Mailchimp integration settings class.
 *
 * @since 2.18.0
 */
class MailchimpIntegrationSettings extends IntegrationSettings {

	/**
	 * The settings data.
	 *
	 * @since 2.18.0
	 *
	 * @var array
	 */
	protected static $data = array(
		'forced_email_subscription'  => false,
		'is_connected'               => false,
		'api_key'                    => '',
		'list'                       => '',
		'group'                      => '',
		'interest'                   => '',
		'subscriber_consent_message' => 'I would like to receive the newsletters.',
	);

	/**
	 * Get the option name for the settings.
	 *
	 * @since 2.18.0
	 *
	 * @return string
	 */
	protected static function get_option_name() {
		return 'masteriyo_mailchimp_integration_setting';
	}

	/**
	 * Get the Mailchimp API key.
	 *
	 * @since 2.18.0
	 *
	 * @return string The Mailchimp API key.
	 */
	public static function get_api_key() {
		return static::get( 'api_key' );
	}
}
