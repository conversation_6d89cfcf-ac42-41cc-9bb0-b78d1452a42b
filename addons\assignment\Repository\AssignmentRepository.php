<?php
/**
 * AssignmentRepository class.
 *
 * @since 2.3.5
 *
 * @package Masteriyo\Addons\Assignment
 */

namespace Masteriyo\Addons\Assignment\Repository;

defined( 'ABSPATH' ) || exit;


use Masteriyo\Database\Model;
use Masteriyo\Enums\PostStatus;
use Masteriyo\Addons\Assignment\Models\Assignment;
use Masteriyo\PostType\PostType;
use Masteriyo\Repository\AbstractRepository;
use Masteriyo\Repository\RepositoryInterface;

/**
 * AssignmentRepository class.
 *
 * @since 2.3.5
 */
class AssignmentRepository extends AbstractRepository implements RepositoryInterface {

	/**
	 * Data stored in meta keys, but not considered "meta".
	 *
	 * @since 2.3.5
	 * @var array
	 */
	protected $internal_meta_keys = array(
		'course_id'            => '_course_id',
		'total_points'         => '_total_points',
		'pass_points'          => '_pass_points',
		'due_date'             => '_due_date',
		'due_timestamp'        => '_due_timestamp',
		'max_file_upload_size' => '_max_file_upload_size',
		'download_materials'   => '_download_materials',
		'video_source'         => '_video_source',
		'video_source_url'     => '_video_source_url',
		'video_playback_time'  => '_video_playback_time',
	);

	/**
	 * Create a assignment in the database.
	 *
	 * @since 2.3.5
	 *
	 * @param \Masteriyo\Addons\Assignment\Models\Assignment $assignment Assignment object.
	 */
	public function create( Model &$assignment ) {
		if ( ! $assignment->get_created_at( 'edit' ) ) {
			$assignment->set_created_at( time() );
		}

		// Author of the assignment should be same as that of course, because assignments are children of courses.
		if ( $assignment->get_course_id() ) {
			$assignment->set_author_id( masteriyo_get_course_author_id( $assignment->get_course_id() ) );
		}

		// Set the author of the assignment to the current user id, if the assignment doesn't have a author.
		if ( empty( $assignment->get_author_id() ) ) {
			$assignment->set_author_id( get_current_user_id() );
		}

		if ( $assignment->get_due_date( 'edit' ) ) {
			$assignment->set_due_timestamp( $assignment->get_due_date( 'edit' )->getTimestamp() );
		}

		$id = wp_insert_post(
			/**
			 * Filters new assignment data before creating.
			 *
			 * @since 2.3.5
			 *
			 * @param array $data New assignment data.
			 * @param Masteriyo\Addons\Assignment\Models\Assignment $assignment Assignment object.
			 */
			apply_filters(
				'masteriyo_new_assignment_data',
				array(
					'post_type'      => PostType::ASSIGNMENT,
					'post_status'    => $assignment->get_status() ? $assignment->get_status() : PostStatus::PUBLISH,
					'post_author'    => $assignment->get_author_id( 'edit' ),
					'post_title'     => $assignment->get_name(),
					'post_content'   => $assignment->get_answer(),
					'post_parent'    => $assignment->get_parent_id(),
					'comment_status' => 'closed',
					'ping_status'    => 'closed',
					'menu_order'     => $assignment->get_menu_order(),
					'post_date'      => gmdate( 'Y-m-d H:i:s', $assignment->get_created_at( 'edit' )->getOffsetTimestamp() ),
					'post_date_gmt'  => gmdate( 'Y-m-d H:i:s', $assignment->get_created_at( 'edit' )->getTimestamp() ),
				),
				$assignment
			)
		);

		if ( $id && ! is_wp_error( $id ) ) {
			$assignment->set_id( $id );
			$this->update_post_meta( $assignment, true );
			// TODO Invalidate caches.

			$assignment->save_meta_data();
			$assignment->apply_changes();

			/**
			 * Fires after creating a assignment.
			 *
			 * @since 2.3.5
			 *
			 * @param integer $id The assignment ID.
			 * @param \Masteriyo\Addons\Assignment\Models\Assignment $object The assignment object.
			 */
			do_action( 'masteriyo_new_assignment', $id, $assignment );
		}

	}

	/**
	 * Read a assignment.
	 *
	 * @since 2.3.5
	 *
	 * @param \Masteriyo\Addons\Assignment\Models\Assignment $assignment Assignment object.
	 * @throws \Exception If invalid assignment.
	 */
	public function read( Model &$assignment ) {
		$assignment_post = get_post( $assignment->get_id() );

		if ( ! $assignment->get_id() || ! $assignment_post || PostType::ASSIGNMENT !== $assignment_post->post_type ) {
			throw new \Exception( __( 'Invalid assignment.', 'learning-management-system' ) );
		}

		$assignment->set_props(
			array(
				'name'        => $assignment_post->post_title,
				'created_at'  => $this->string_to_timestamp( $assignment_post->post_date_gmt ),
				'modified_at' => $this->string_to_timestamp( $assignment_post->post_modified_gmt ),
				'answer'      => $assignment_post->post_content,
				'parent_id'   => $assignment_post->post_parent,
				'menu_order'  => $assignment_post->menu_order,
				'status'      => $assignment_post->post_status,
				'author_id'   => $assignment_post->post_author,
			)
		);

		$this->read_assignment_data( $assignment );
		$this->read_extra_data( $assignment );
		$assignment->set_object_read( true );

		/**
		 * Fires after reading a assignment from database.
		 *
		 * @since 2.3.5
		 *
		 * @param integer $id The assignment ID.
		 * @param \Masteriyo\Addons\Assignment\Models\Assignment $object The assignment object.
		 */
		do_action( 'masteriyo_assignment_read', $assignment->get_id(), $assignment );
	}

	/**
	 * Update a assignment in the database.
	 *
	 * @since 2.3.5
	 *
	 * @param \Masteriyo\Addons\Assignment\Models\Assignment $assignment Assignment object.
	 *
	 * @return void
	 */
	public function update( Model &$assignment ) {
		$changes = $assignment->get_changes();

		$post_data_keys = array(
			'answer',
			'name',
			'status',
			'parent_id',
			'menu_order',
			'created_at',
			'modified_at',
		);

		// Only update the post when the post data changes.
		if ( array_intersect( $post_data_keys, array_keys( $changes ) ) ) {
			$post_data = array(
				'post_content'   => $assignment->get_answer( 'edit' ),
				'post_title'     => $assignment->get_name( 'edit' ),
				'post_parent'    => $assignment->get_parent_id( 'edit' ),
				'comment_status' => 'closed',
				'post_status'    => $assignment->get_status( 'edit' ) ? $assignment->get_status( 'edit' ) : PostStatus::PUBLISH,
				'menu_order'     => $assignment->get_menu_order( 'edit' ),
				'post_type'      => PostType::ASSIGNMENT,
			);

			/**
			 * When updating this object, to prevent infinite loops, use $wpdb
			 * to update data, since wp_update_post spawns more calls to the
			 * save_post action.
			 *
			 * This ensures hooks are fired by either WP itself (admin screen save),
			 * or an update purely from CRUD.
			 */
			if ( doing_action( 'save_post' ) ) {
				// TODO Abstract the $wpdb WordPress class.
				$GLOBALS['wpdb']->update( $GLOBALS['wpdb']->posts, $post_data, array( 'ID' => $assignment->get_id() ) );
				clean_post_cache( $assignment->get_id() );
			} else {
				wp_update_post( array_merge( array( 'ID' => $assignment->get_id() ), $post_data ) );
			}
			$assignment->read_meta_data( true ); // Refresh internal meta data, in case things were hooked into `save_post` or another WP hook.
		} else { // Only update post modified time to record this save event.
			$GLOBALS['wpdb']->update(
				$GLOBALS['wpdb']->posts,
				array(
					'post_modified'     => current_time( 'mysql' ),
					'post_modified_gmt' => current_time( 'mysql', true ),
				),
				array(
					'ID' => $assignment->get_id(),
				)
			);
			clean_post_cache( $assignment->get_id() );
		}

		if ( $assignment->get_due_date( 'edit' ) ) {
			$assignment->set_due_timestamp( $assignment->get_due_date( 'edit' )->getTimestamp() );
		}

		$this->update_post_meta( $assignment );

		$assignment->apply_changes();

		/**
		 * Fires after updating a assignment.
		 *
		 * @since 2.3.5
		 *
		 * @param integer $id The assignment ID.
		 * @param \Masteriyo\Addons\Assignment\Models\Assignment $object The assignment object.
		 */
		do_action( 'masteriyo_update_assignment', $assignment->get_id(), $assignment );
	}

	/**
	 * Delete a assignment from the database.
	 *
	 * @since 2.3.5
	 *
	 * @param \Masteriyo\Addons\Assignment\Models\Assignment $assignment Assignment object.
	 * @param array $args   Array of args to pass.alert-danger.
	 */
	public function delete( Model &$assignment, $args = array() ) {
		$id          = $assignment->get_id();
		$object_type = $assignment->get_object_type();

		$args = array_merge(
			array(
				'force_delete' => false,
			),
			$args
		);

		if ( ! $id ) {
			return;
		}

		if ( $args['force_delete'] ) {
			/**
			 * Fires before deleting a assignment.
			 *
			 * @since 2.3.5
			 *
			 * @param integer $id The assignment ID.
			 * @param \Masteriyo\Addons\Assignment\Models\Assignment $object The assignment object.
			 */
			do_action( 'masteriyo_before_delete_' . $object_type, $id, $assignment );

			wp_delete_post( $id, true );
			$assignment->set_id( 0 );

			/**
			 * Fires after deleting a assignment.
			 *
			 * @since 2.3.5
			 *
			 * @param integer $id The assignment ID.
			 * @param \Masteriyo\Addons\Assignment\Models\Assignment $object The assignment object.
			 */
			do_action( 'masteriyo_after_delete_' . $object_type, $id, $assignment );
		} else {
			/**
			 * Fires before moving a assignment to trash.
			 *
			 * @since 2.3.5
			 *
			 * @param integer $id The assignment ID.
			 * @param \Masteriyo\Addons\Assignment\Models\Assignment $object The assignment object.
			 */
			do_action( 'masteriyo_before_trash_' . $object_type, $id, $assignment );

			wp_trash_post( $id );
			$assignment->set_status( 'trash' );

			/**
			 * Fires after moving a assignment to trash.
			 *
			 * @since 2.3.5
			 *
			 * @param integer $id The assignment ID.
			 * @param \Masteriyo\Addons\Assignment\Models\Assignment $object The assignment object.
			 */
			do_action( 'masteriyo_after_trash_' . $object_type, $id, $assignment );
		}
	}

	/**
	 * Read assignment data. Can be overridden by child classes to load other props.
	 *
	 * @since 2.3.5
	 *
	 * @param Assignment $assignment Assignment object.
	 */
	protected function read_assignment_data( &$assignment ) {
		$id          = $assignment->get_id();
		$meta_values = $this->read_meta( $assignment );

		$set_props = array();

		$meta_values = array_reduce(
			$meta_values,
			function( $result, $meta_value ) {
				$result[ $meta_value->key ][] = $meta_value->value;
				return $result;
			},
			array()
		);

		foreach ( $this->internal_meta_keys as $prop => $meta_key ) {
			$meta_value         = isset( $meta_values[ $meta_key ][0] ) ? $meta_values[ $meta_key ][0] : null;
			$set_props[ $prop ] = maybe_unserialize( $meta_value ); // get_post_meta only unserializes single values.
		}

		$assignment->set_props( $set_props );
	}

	/**
	 * Read extra data associated with the assignment, like button text or assignment URL for external assignments.
	 *
	 * @since 2.3.5
	 *
	 * @param Assignment $assignment Assignment object.
	 */
	protected function read_extra_data( &$assignment ) {
		$meta_values = $this->read_meta( $assignment );

		foreach ( $assignment->get_extra_data_keys() as $key ) {
			$function = 'set_' . $key;

			if ( is_callable( array( $assignment, $function ) )
				&& isset( $meta_values[ '_' . $key ] ) ) {
				$assignment->{$function}( $meta_values[ '_' . $key ] );
			}
		}
	}

	/**
	 * Fetch assignments.
	 *
	 * @since 2.3.5
	 *
	 * @param array $query_vars Query vars.
	 * @return Assignment[]
	 */
	public function query( $query_vars ) {
		$args = $this->get_wp_query_args( $query_vars );

		if ( ! empty( $args['errors'] ) ) {
			$query = (object) array(
				'posts'         => array(),
				'found_posts'   => 0,
				'max_num_pages' => 0,
			);
		} else {
			$query = new \WP_Query( $args );
		}

		if ( isset( $query_vars['return'] ) && 'objects' === $query_vars['return'] && ! empty( $query->posts ) ) {
			// Prime caches before grabbing objects.
			update_post_caches( $query->posts, array( PostType::ASSIGNMENT ) );
		}

		$assignments = ( isset( $query_vars['return'] ) && 'ids' === $query_vars['return'] ) ? $query->posts : array_filter( array_map( 'masteriyo_get_assignment', $query->posts ) );

		if ( isset( $query_vars['paginate'] ) && $query_vars['paginate'] ) {
			return (object) array(
				'assignments'   => $assignments,
				'total'         => $query->found_posts,
				'max_num_pages' => $query->max_num_pages,
			);
		}

		return $assignments;
	}

	/**
	 * Get valid WP_Query args from a AssignmentQuery's query variables.
	 *
	 * @since 2.3.5
	 * @param array $query_vars Query vars from a AssignmentQuery.
	 * @return array
	 */
	protected function get_wp_query_args( $query_vars ) {
		// Map query vars to ones that get_wp_query_args or WP_Query recognize.
		$key_mapping = array(
			'status'    => 'post_status',
			'page'      => 'paged',
			'parent_id' => 'post_parent',
		);

		foreach ( $key_mapping as $query_key => $db_key ) {
			if ( isset( $query_vars[ $query_key ] ) ) {
				$query_vars[ $db_key ] = $query_vars[ $query_key ];
				unset( $query_vars[ $query_key ] );
			}
		}

		$query_vars['post_type'] = PostType::ASSIGNMENT;

		$wp_query_args = parent::get_wp_query_args( $query_vars );

		if ( ! isset( $wp_query_args['date_query'] ) ) {
			$wp_query_args['date_query'] = array();
		}
		if ( ! isset( $wp_query_args['meta_query'] ) ) {
			$wp_query_args['meta_query'] = array(); // phpcs:ignore WordPress.DB.SlowDBQuery.slow_db_query_meta_query
		}

		// Handle date queries.
		$date_queries = array(
			'created_at'  => 'post_date',
			'modified_at' => 'post_modified',
		);
		foreach ( $date_queries as $query_var_key => $db_key ) {
			if ( isset( $query_vars[ $query_var_key ] ) && '' !== $query_vars[ $query_var_key ] ) {

				// Remove any existing meta queries for the same keys to prevent conflicts.
				$existing_queries = wp_list_pluck( $wp_query_args['meta_query'], 'key', true );
				foreach ( $existing_queries as $query_index => $query_contents ) {
					unset( $wp_query_args['meta_query'][ $query_index ] );
				}

				$wp_query_args = $this->parse_date_for_wp_query( $query_vars[ $query_var_key ], $db_key, $wp_query_args );
			}
		}

		// Handle paginate.
		if ( ! isset( $query_vars['paginate'] ) || ! $query_vars['paginate'] ) {
			$wp_query_args['no_found_rows'] = true;
		}

		// Handle orderby.
		if ( isset( $query_vars['orderby'] ) && 'include' === $query_vars['orderby'] ) {
			$wp_query_args['orderby'] = 'post__in';
		}

		/**
		 * Filters WP Query args for assignment post type query.
		 *
		 * @since 2.3.5
		 *
		 * @param array $wp_query_args WP Query args.
		 * @param array $query_vars Query vars.
		 * @param \Masteriyo\Repository\AssignmentRepository $repository Assignment repository object.
		 */
		return apply_filters( 'masteriyo_assignment_wp_query_args', $wp_query_args, $query_vars, $this );
	}
}
