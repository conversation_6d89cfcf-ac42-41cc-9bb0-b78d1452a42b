<?php
/**
 * Masteriyo course duration block builder.
 *
 * @since 2.14.0
 */

namespace Masteriyo\Addons\Certificate\PDF\BlockBuilders;

defined( 'ABSPATH' ) || exit;


class MasteriyoCourseDuration extends BlockBuilder {

	/**
	 * Build and return the block HTML.
	 *
	 * @since 2.14.0
	 *
	 * @return string
	 */
	public function build() {
		$pdf             = $this->get_pdf();
		$block_data      = $this->get_block_data();
		$course          = masteriyo_get_course( $pdf->get_course_id() );
		$course_duration = __( 'Course Duration', 'learning-management-system' );

		if ( ! is_null( $course ) ) {
			$course_duration = masteriyo_minutes_to_time_length_string( $course->get_duration() );
		}

		/**
		 * Filters the course duration value to be displayed on the certificate.
		 *
		 * @since 2.14.0
		 *
		 * @param string $course_duration The course duration value.
		 * @return string The filtered course duration value.
		 */
		$course_duration = apply_filters( 'masteriyo_certificate_course_duration', $course_duration );

		$html  = $block_data['innerHTML'];
		$html  = str_replace( '{{masteriyo_course_duration}}', $course_duration, $html );
		$html .= '<style>' . masteriyo_array_get( $block_data, 'attrs.blockCSS', '' ) . '</style>';

		return $html;
	}
}
