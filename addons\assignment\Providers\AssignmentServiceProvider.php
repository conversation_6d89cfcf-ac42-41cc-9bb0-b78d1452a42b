<?php
/**
 * Assignment service provider.
 *
 * @since 2.3.5
 * @package \Masteriyo\Addons\Assignment
 */

namespace Masteriyo\Addons\Assignment\Providers;

defined( 'ABSPATH' ) || exit;

use Masteriyo\Addons\Assignment\AssignmentAddon;
use Masteriyo\Addons\Assignment\Models\Assignment;
use League\Container\ServiceProvider\AbstractServiceProvider;
use Masteriyo\Addons\Assignment\Query\AssignmentQuery;
use Masteriyo\Addons\Assignment\RestApi\AssignmentsController;
use Masteriyo\Addons\Assignment\Repository\AssignmentRepository;

/**
 * Assignment service provider.
 *
 * @since 2.3.5
 */
class AssignmentServiceProvider extends AbstractServiceProvider {
	/**
	 * The provided array is a way to let the container
	 * know that a service is provided by this service
	 * provider. Every service that is registered via
	 * this service provider must have an alias added
	 * to this array or it will be ignored
	 *
	 * @since 2.3.5
	 *
	 * @var array
	 */
	protected $provides = array(
		'addons.assignment',
		AssignmentAddon::class,

		'assignment',
		'assignment.store',
		'assignment.rest',
		'mto-assignment',
		'mto-assignment.store',
		'mto-assignment.rest',
		AssignmentsController::class,

		'query.assignment',
		AssignmentQuery::class,
	);

	/**
	 * This is where the magic happens, within the method you can
	 * access the container and register or retrieve anything
	 * that you need to, but remember, every alias registered
	 * within this method must be declared in the `$provides` array.
	 *
	 * @since 2.3.5
	 */
	public function register() {
		$this->getContainer()->add( 'addons.assignment', AssignmentAddon::class, true );

		$this->getContainer()->add( 'assignment.store', AssignmentRepository::class );

		$this->getContainer()->add( 'assignment.rest', AssignmentsController::class )
			->addArgument( 'permission' );

		$this->getContainer()->add( AssignmentsController::class )
			->addArgument( 'permission' );

		$this->getContainer()->add( 'assignment', Assignment::class )
			->addArgument( 'assignment.store' );

		// Register based on post type.
		$this->getContainer()->add( 'mto-assignment', Assignment::class )
			->addArgument( 'assignment.store' );

		$this->getContainer()->add( 'mto-assignment.store', AssignmentRepository::class );

		$this->getContainer()->add( 'mto-assignment.rest', AssignmentsController::class )
				->addArgument( 'permission' );

		$this->getContainer()->add( 'query.assignment', AssignmentQuery::class );

		$this->getContainer()->add( AssignmentQuery::class );
	}
}
