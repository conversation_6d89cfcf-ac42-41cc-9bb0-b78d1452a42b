.masteriyo-certificate-share-container {
	display: flex;
	align-items: center;
	gap: 10px;
	position: relative;
}

.masteriyo-certificate-share-button {
	box-sizing: border-box;
	color: var(--masteriyo-color-primary);
	background: var(--masteriyo-color-white);
	border: 1px solid var(--masteriyo-color-primary);
	padding: 6px 16px;
	border-radius: 2px;
	font-size: 14px;
	font-weight: 500;
	text-decoration: none;

	&:hover {
		box-sizing: border-box;
		color: var(--masteriyo-color-primary);
		background: var(--masteriyo-color-white);
		border: 1px solid var(--masteriyo-color-primary);
		padding: 6px 16px;
		border-radius: 2px;
		font-size: 14px;
		font-weight: 500;
		text-decoration: none;
	}
}

.masteriyo-certificate-share-modal {
	.masteriyo-overlay {
		align-items: center;
	}
}

.masteriyo-certificate-share-popup {
	border-radius: 8px;
	background: #fff;
	box-shadow: 0px 0px 25px 0px rgba(10, 10, 10, 0.1);
	display: flex;
	width: 60%;
	height: auto;
	padding: 28px 28px 30px 28px;
	flex-direction: column;
	gap: 32px;
}

.masteriyo-certificate-share {
	&__wrapper {
		position: relative;
	}

	&__heading {
		color: #222222;
		font-size: 24px;
		font-weight: 700;
		line-height: 32px;
		padding-bottom: 16px;
		border-bottom: 1px solid #e5e5e5;
		margin-bottom: 18px;
	}

	&__exit-popup {
		position: absolute;
		top: 0;
		right: 0;
		border-radius: 60px;
		background: #eef1f7;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 7px;
		cursor: pointer;

		svg {
			width: 14px;
			height: 14px;
		}
	}

	&__title {
		color: #111;
		font-size: 16px;
		font-weight: 300;
		line-height: 26px;
	}

	&__share-link {
		text-decoration: none;
	}

	&__share-link a {
		text-decoration: none;
	}

	&__share-button {
		margin-top: 20px;
		box-sizing: border-box;
		color: var(--masteriyo-color-primary);
		background: var(--masteriyo-color-white);
		border: 1px solid var(--masteriyo-color-primary);
		padding: 6px 16px;
		border-radius: 2px;
		font-size: 16px;
		font-weight: 500;
		text-decoration: none;
		width: 100%;

		&:hover {
			box-sizing: border-box;
			color: var(--masteriyo-color-primary);
			background: var(--masteriyo-color-white);
			border: 1px solid var(--masteriyo-color-primary);
			padding: 6px 16px;
			border-radius: 2px;
			font-size: 16px;
			font-weight: 500;
			text-decoration: none;
			width: 100%;
		}

		&:focus {
			box-sizing: border-box;
			color: var(--masteriyo-color-primary);
			background: var(--masteriyo-color-white);
			border: 1px solid var(--masteriyo-color-primary);
			padding: 6px 16px;
			border-radius: 2px;
			font-size: 16px;
			font-weight: 500;
			text-decoration: none;
			width: 100%;
		}
	}

	&__share-button svg {
		position: relative;
		top: 4px;
		right: 4px;
	}
}

.masteriyo-share-container {
	display: flex;
	align-items: center;
	justify-content: space-between;
}
