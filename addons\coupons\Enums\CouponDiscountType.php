<?php
/**
 * Coupon discount type enums.
 *
 * @since 2.5.12
 * @package Masteriyo\Addons\Coupons
 */

namespace Masteriyo\Addons\Coupons\Enums;

defined( 'ABSPATH' ) || exit;

/**
 * Coupon discount type enum class.
 *
 * @since 2.5.12
 */
class CouponDiscountType {
	/**
	 * Coupon fixed cart discount type.
	 *
	 * @since 2.5.12
	 * @var string
	 */
	const FIXED_CART = 'fixed-cart';

	/**
	 * Coupon percentage cart discount type.
	 *
	 * @since 2.5.12
	 * @var string
	 */
	const PERCENTAGE_CART = 'percentage-cart';

	/**
	 * Return all the Coupon discount types.
	 *
	 * @since 2.5.12
	 *
	 * @return array
	 */
	public static function all() {
		return array_unique(
			/**
			 * Filters Coupon discount type list.
			 *
			 * @since 2.5.12
			 *
			 * @param string[] $statuses Coupon discount type list.
			 */
			apply_filters(
				'masteriyo_coupon_discount_types',
				array(
					self::FIXED_CART,
					self::PERCENTAGE_CART,
				)
			)
		);
	}
}
