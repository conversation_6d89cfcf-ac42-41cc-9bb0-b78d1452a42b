<?php

defined( 'ABSPATH' ) || exit;

/**
 * Template functions.
 *
 * @since 2.12.0
 */

use Masteriyo\Addons\CourseBundle\Models\CourseBundle;
use Masteriyo\Enums\PostStatus;
use Masteriyo\Models\Course;
use Masteriyo\PostType\PostType;

if ( ! function_exists( 'masteriyo_setup_course_bundle_data' ) ) {
	/**
	 * Masteriyo setup course bundle data.
	 *
	 * @since 2.12.0
	 * @param int|\WP_Post $post Post.
	 * @return CourseBundle
	 */
	function masteriyo_setup_course_bundle_data( $post ) {
		unset( $GLOBALS['course_bundle'] );

		if ( is_int( $post ) ) {
			$post = get_post( $post );
		}

		if ( empty( $post->post_type ) || ! in_array( $post->post_type, array( PostType::COURSE_BUNDLE ), true ) ) {
			return;
		}

		$GLOBALS['course_bundle'] = masteriyo_get_course_bundle( $post );

		/**
		 * Filters course bundle data.
		 *
		 * @since 2.14.0
		 *
		 * @param CourseBundle $course_bundle Course bundle.
		 */
		return apply_filters( 'masteriyo_single_course_bundle', $GLOBALS['course_bundle'] );
	}
}

function_exists( 'add_action' ) && add_action( 'the_post', 'masteriyo_setup_course_bundle_data' );

if ( ! function_exists( 'masteriyo_single_course_bundle_featured_image' ) ) {
	/**
	 * Show course featured image.
	 *
	 * @param CourseBundle $course_bundle Course bundle.
	 * @since 2.12.0
	 */
	function masteriyo_single_course_bundle_featured_image( $course_bundle ) {
		masteriyo_get_template(
			'single-course-bundle/featured-image.php',
			array(
				'course_bundle' => $course_bundle,
			)
		);
	}
}

if ( ! function_exists( 'masteriyo_single_course_bundle_title' ) ) {
	/**
	 * Show course title.
	 *
	 * @param CourseBundle $course_bundle Course bundle.
	 * @since 2.12.0
	 */
	function masteriyo_single_course_bundle_title( $course_bundle ) {
		masteriyo_get_template(
			'single-course-bundle/title.php',
			array(
				'course_bundle' => $course_bundle,
			)
		);
	}
}

if ( ! function_exists( 'masteriyo_single_course_bundle_author' ) ) {
	/**
	 * Show course bundle author and rating.
	 *
	 * @param CourseBundle $course_bundle Course bundle.
	 * @since 2.12.0
	 */
	function masteriyo_single_course_bundle_author( $course_bundle ) {
		masteriyo_get_template(
			'single-course-bundle/author.php',
			array(
				'course_bundle' => $course_bundle,
				'author'        => masteriyo_get_user( $course_bundle->get_author_id() ),
			)
		);
	}
}


if ( ! function_exists( 'masteriyo_single_course_bundle_price_and_buy_button' ) ) {
	/**
	 * Show course price and enroll button.
	 *
	 * @param CourseBundle $course_bundle Course bundle.
	 * @since 2.12.0
	 */
	function masteriyo_single_course_bundle_price_and_buy_button( $course_bundle ) {

		//single course price
		masteriyo_get_template(
			'single-course-bundle/price-and-buy-button.php',
			array(
				'course_bundle' => $course_bundle,
			)
		);
	}
}


if ( ! function_exists( 'masteriyo_single_course_bundle_highlights' ) ) {
	/**
	 * Show course highlights in single course bundle page.
	 *
	 * @param CourseBundle $course_bundle Course bundle.
	 * @since 2.12.0
	 */
	function masteriyo_single_course_bundle_highlights( $course_bundle ) {
		masteriyo_get_template(
			'single-course-bundle/highlights.php',
			array(
				'course_bundle' => $course_bundle,
			)
		);
	}
}

if ( ! function_exists( 'masteriyo_template_single_course_bundle_main_content' ) ) {
	/**
	 * Show course main content in single course bundle page.
	 *
	 * @param CourseBundle $course_bundle Course bundle.
	 * @since 2.12.0
	 */
	function masteriyo_template_single_course_bundle_main_content( $course_bundle ) {
		masteriyo_get_template(
			'single-course-bundle/main-content.php',
			array(
				'course_bundle' => $course_bundle,
			)
		);
	}
}

if ( ! function_exists( 'masteriyo_single_course_bundle_tab_handles' ) ) {
	/**
	 * Show tab handles in single course bundle page.
	 *
	 * @param CourseBundle $course_bundle Course bundle.
	 * @since 2.12.0
	 */
	function masteriyo_single_course_bundle_tab_handles( $course_bundle ) {
		masteriyo_get_template(
			'single-course-bundle/tab-handles.php',
			array(
				'course_bundle' => $course_bundle,
			)
		);
	}
}

if ( ! function_exists( 'masteriyo_single_course_bundle_overview' ) ) {
	/**
	 * Show course overview in single course bundle page.
	 *
	 * @param CourseBundle $course_bundle Course bundle.
	 * @since 2.12.0
	 */
	function masteriyo_single_course_bundle_overview( $course_bundle ) {
		masteriyo_get_template(
			'single-course-bundle/overview.php',
			array(
				'course_bundle' => $course_bundle,
			)
		);
	}
}

if ( ! function_exists( 'masteriyo_single_course_bundle_courses' ) ) {
	/**
	 * Show course overview in single course page.
	 *
	 * @param CourseBundle $course_bundle Course bundle object.
	 * @since 2.12.0
	 */
	function masteriyo_single_course_bundle_courses( $course_bundle ) {
		masteriyo_get_template(
			'single-course-bundle/courses.php',
			array(
				'courses' => $course_bundle->get_bundled_courses( PostStatus::PUBLISH ),
			)
		);
	}
}

if ( ! function_exists( 'masteriyo_single_course_bundle_courses_list' ) ) {

	/**
	 * Show courses list in single course bundle page.
	 *
	 * @since 2.12.0
	 * @param Course[] $courses
	 * @return void
	 */
	function masteriyo_single_course_bundle_courses_list( $courses ) {
		masteriyo_get_template(
			'single-course-bundle/courses-list.php',
			array(
				'courses' => $courses,
			)
		);
	}
}



if ( ! function_exists( 'masteriyo_single_course_bundle_courses_list_items' ) ) {
	/**
	 * Show courses list items in single course bundle page.
	 *
	 * @since 2.12.0
	 * @param Course[] $courses
	 * @return void
	 */
	function masteriyo_single_course_bundle_courses_list_items( $courses ) {
		if ( ! $courses ) {
			return;
		}
		if ( $courses instanceof \Masteriyo\Models\Course ) {
			$courses = array( $courses );
		}

		foreach ( (array) $courses as $course ) {
			masteriyo_get_template(
				'single-course-bundle/courses-list-item.php',
				array(
					'course' => $course,
				)
			);
		}
	}
}

if ( ! function_exists( 'masteriyo_single_course_bundle_instructor' ) ) {
	/**
	 * Show course bundle instructors in single course bundle page.
	 *
	 * @since 2.12.0
	 * @param CourseBundle $course_bundle
	 * @return void
	 */
	function masteriyo_single_course_bundle_instructor( $course_bundle ) {
		masteriyo_get_template(
			'single-course-bundle/instructors.php',
			array(
				'instructors' => $course_bundle->get_instructors(),
			)
		);
	}
}

if ( ! function_exists( 'masteriyo_single_course_bundle_categories' ) ) {
	/**
	 * Show course bundle categories.
	 *
	 * @param CourseBundle $course_bundle
	 * @since 2.12.0
	 */
	function masteriyo_single_course_bundle_categories( $course_bundle ) {
		if ( empty( $course_bundle->get_categories() ) ) {
			return;
		}

		masteriyo_get_template(
			'single-course-bundle/categories.php',
			array(
				'course_bundle' => $course_bundle,
			)
		);
	}
}

if ( ! function_exists( 'masteriyo_course_bundle_buy_button' ) ) {
	/**
	 * Course bundle buy button.
	 *
	 * @param CourseBundle $course_bundle Course bundle object.
	 * @return void
	 */
	function masteriyo_course_bundle_buy_button( $course_bundle ) {
		$user_has_bought_bundle = false;
		if ( is_user_logged_in() ) {
			$bundles   = get_user_meta( get_current_user_id(), '_mas_bundles', true );
			$bundle_id = $course_bundle->get_id();

			if ( is_array( $bundles ) && in_array( $bundle_id, $bundles, true ) ) {
				$user_has_bought_bundle = masteriyo_is_course_bundle_order_completed( masteriyo_get_order_ids_by_course_bundle_id( $bundle_id ) );
			}
			/**
			 * Check if user has bought the bundle.
			 * @since 2.14.0
			 */
				$user_has_bought_bundle = apply_filters( 'masteriyo_user_has_bought_bundle', $user_has_bought_bundle, $course_bundle );
		}

		$class = array(
			'masteriyo-enroll-btn',
			'masteriyo-btn',
			'masteriyo-btn-primary',
			'masteriyo-single-course--btn',
			'masteriyo-enroll-btn',
			'mb-0',
			'masteriyo-enroll-btn',
		);

		/**
		 * Filters enroll button class.
		 *
		 * @since 2.15.0
		 *
		 * @param string[] $class An array of class names.
		 * @param \Masteriyo\Addons\CourseBundle\Models\CourseBundle $course_bundle.
		 * @param bool $user_has_bought_bundle.
		 */
		$class = apply_filters( 'masteriyo_enroll_bundle_button_class', $class, $course_bundle, $user_has_bought_bundle );
		masteriyo_get_template(
			'course-bundle-buy-button.php',
			array(
				'course_bundle'          => $course_bundle,
				'user_has_bought_bundle' => $user_has_bought_bundle,
				'class'                  => implode( ' ', $class ),
			)
		);
	}
}

if ( ! function_exists( 'masteriyo_course_bundle_search_form' ) ) {

	/**
	 * Course Bundle Search Form.
	 *
	 * @since 2.12.0
	 */
	function masteriyo_course_bundle_search_form() {
		$show_search_box = masteriyo_get_setting( 'course_archive.display.enable_search' );

		if ( true !== $show_search_box ) {
			return;
		}

		printf( '<div class="masteriyo-search-section">' );

		/**
		 * Fires before rendering search section content.
		 *
		 * @since 2.12.0
		 */
		do_action( 'masteriyo_before_course_bundle_search_section_content' );

		?>
		<div class="masteriyo-search">
			<?php masteriyo_get_course_bundle_search_form(); ?>
		</div>
		<?php

		/**
		 * Fires after rendering search section content.
		 *
		 * @since 2.12.0
		 */
		do_action( 'masteriyo_after_course_bundle_search_section_content' );

		printf( '</div>' );
	}
}

if ( ! function_exists( 'masteriyo_get_course_bundle_search_form' ) ) {

	/**
	 * Display course search form.
	 *
	 * Will first attempt to locate the course-searchform.php file in either the child or.
	 * the parent, then load it. If it doesn't exist, then the default search form.
	 * will be displayed.
	 *
	 * The default searchform uses html5.
	 *
	 * @since 2.12.0
	 * @param bool $echo (default: true).
	 * @return string
	 */
	function masteriyo_get_course_bundle_search_form( $echo = true ) {
		global $course_search_form_index;

		ob_start();

		if ( empty( $course_search_form_index ) ) {
			$course_search_form_index = 0;
		}

		/**
		 * Fires before rendering course search form.
		 *
		 * @since 2.12.0
		 */
		do_action( 'before_masteriyo_get_course_bundle_search_form' );

		masteriyo_get_template(
			'course-bundle/course-bundle-searchform.php',
			array(
				'index' => $course_search_form_index++,
			)
		);

		/**
		 * Filters course search form html.
		 *
		 * @since 2.12.0
		 *
		 * @param string $html The course search form html.
		 */
		$search_form = apply_filters( 'masteriyo_get_course_bundle_search_form', ob_get_clean() );

		if ( ! $echo ) {
			return $search_form;
		}

		echo $search_form; // phpcs:ignore
	}
}

if ( ! function_exists( 'masteriyo_course_bundle_loop_start' ) ) {

	/**
	 * Output the start of a course loop. By default this is a UL.
	 *
	 * @since 2.12.0
	 *
	 * @param bool $echo Should echo?
	 * @return string
	 */
	function masteriyo_course_bundle_loop_start( $echo = true, $template_name = 'course-bundle/loop/bundle-loop-start.php' ) {
		ob_start();

		masteriyo_set_loop_prop( 'loop', 0 );

		masteriyo_get_template( $template_name );

		/**
		 * Filter masteriyo courses loop start content.
		 *
		 * @since 2.12.0
		 */
		$loop_start = apply_filters( 'masteriyo_course_bundle_loop_start', ob_get_clean() );

		if ( $echo ) {
			echo wp_kses_post( $loop_start );
		} else {
			return $loop_start;
		}
	}
}

if ( ! function_exists( 'masteriyo_course_bundle_loop_end' ) ) {

	/**
	 * Output the end of a course loop. By default this is a UL.
	 *
	 * @since 2.12.0
	 *
	 * @param bool $echo Should echo?.
	 * @return string
	 */
	function masteriyo_course_bundle_loop_end( $echo = true ) {
		ob_start();

		masteriyo_get_template( 'course-bundle/loop/bundle-loop-end.php' );

		/**
		 * Filters course loop end html.
		 *
		 * @since 2.12.0
		 *
		 * @param string $html The course loop end html.
		 */
		$loop_end = apply_filters( 'masteriyo_course_bundle_loop_end', ob_get_clean() );

		if ( $echo ) {
			echo wp_kses_post( $loop_end );
		} else {
			return $loop_end;
		}
	}
}
