<?php
/**
 * Coupons service provider.
 *
 * @since 2.5.12
 */

namespace Masteriyo\Addons\Coupons\Providers;

defined( 'ABSPATH' ) || exit;

use Masteriyo\Addons\Coupons\Models\Coupon;
use Masteriyo\Addons\Coupons\Repository\CouponRepository;
use Masteriyo\Addons\Coupons\Controllers\CouponsController;
use League\Container\ServiceProvider\AbstractServiceProvider;
use Masteriyo\Addons\Coupons\Coupons;
use Masteriyo\Addons\Coupons\Models\OrderItemCoupon;
use Masteriyo\Addons\Coupons\Repository\OrderItemCouponRepository;

/**
 * Coupons service provider.
 *
 * @since 2.5.12
 */
class CouponsServiceProvider extends AbstractServiceProvider {
	/**
	 * The provided array is a way to let the container
	 * know that a service is provided by this service
	 * provider. Every service that is registered via
	 * this service provider must have an alias added
	 * to this array or it will be ignored
	 *
	 * @since 2.5.12
	 *
	 * @var array
	 */
	protected $provides = array(
		'coupon',
		'coupons',
		'coupon.store',
		'coupon.rest',
		'mto-coupon',
		'mto-coupon.store',
		'mto-coupon.rest',
		'order-item.coupon',
		'order-item.coupon.store',
	);

	/**
	 * This is where the magic happens, within the method you can
	 * access the container and register or retrieve anything
	 * that you need to, but remember, every alias registered
	 * within this method must be declared in the `$provides` array.
	 *
	 * @since 2.5.12
	 */
	public function register() {
		$this->getContainer()->add( 'coupons', Coupons::class, true );

		$this->getContainer()->add( 'coupon.store', CouponRepository::class );

		$this->getContainer()->add( 'coupon.rest', CouponsController::class )
			->addArgument( 'permission' );

		$this->getContainer()->add( 'coupon', Coupon::class )
			->addArgument( 'coupon.store' );

		// Register based on post type.
		$this->getContainer()->add( 'mto-coupon.store', CouponRepository::class );

		$this->getContainer()->add( 'mto-coupon.rest', CouponsController::class )
			->addArgument( 'permission' );

		$this->getContainer()->add( 'mto-coupon', Coupon::class )
			->addArgument( 'mto-coupon.store' );

		$this->getContainer()->add( 'order-item.coupon.store', OrderItemCouponRepository::class );
		$this->getContainer()->add( 'order-item.coupon', OrderItemCoupon::class )
			->addArgument( 'order-item.coupon.store' );
	}
}
