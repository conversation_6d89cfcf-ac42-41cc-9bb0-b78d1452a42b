<?php
/**
 * Handles Ajax requests for switching currency during checkout.
 *
 * This class provides methods to handle Ajax requests for allowing users to switch currency
 * during the checkout process.
 *
 * @since 2.11.0
 *
 * @package Masteriyo\Addons\MultipleCurrency\AjaxHandlers
 */

namespace Masteriyo\Addons\MultipleCurrency\AjaxHandlers;

defined( 'ABSPATH' ) || exit;


use Masteriyo\Abstracts\AjaxHandler;
use Masteriyo\Addons\Coupons\Coupons;
use Masteriyo\Notice;

/**
 * Class SwitchCurrencyAjaxHandler
 *
 * @package Masteriyo\Addons\MultipleCurrency\AjaxHandlers
 */
class SwitchCurrencyAjaxHandler extends AjaxHandler {

	/**
	 * The Ajax action for currency switch.
	 *
	 * @since 2.11.0
	 *
	 * @var string
	 */
	public $action = 'masteriyo_switch_currency';

	/**
	 * Registers the Ajax handler for switching currency.
	 *
	 * @since 2.11.0
	 */
	public function register() {
		add_action( "wp_ajax_{$this->action}", array( $this, 'switch_currency' ) );
		add_action( "wp_ajax_nopriv_{$this->action}", array( $this, 'switch_currency' ) );
	}

	/**
	 * Switches currency based on Ajax request.
	 *
	 * @since 2.11.0
	 */
	public function switch_currency() {
		try {
			if ( ! isset( $_POST['_wpnonce'] ) ) {
				throw new \Exception( __( 'Nonce is required.', 'learning-management-system' ) );
			}

			if ( ! wp_verify_nonce( sanitize_key( wp_unslash( $_POST['_wpnonce'] ) ), 'masteriyo_switch_currency' ) ) {
				throw new \Exception( __( 'Invalid nonce. Maybe you should reload the page.', 'learning-management-system' ) );
			}

			$currency_code = sanitize_text_field( $_POST['currency'] );

			if ( masteriyo_get_currency() !== $currency_code ) {
				$pricing_zone = masteriyo_get_price_zone_by_currency( $currency_code );

				if ( ! $pricing_zone ) {
					throw new \Exception( __( 'Invalid pricing zone.', 'learning-management-system' ) );
				}
			}

			masteriyo_create_session_object()->put( 'selected_currency', $currency_code );
			masteriyo_create_session_object()->save_data();

			$this->send_success_response();
		} catch ( \Exception $e ) {
			masteriyo_add_notice( $e->getMessage(), Notice::ERROR );
			$this->send_failure_response();
		}
	}

	/**
	 * If the AJAX operation failed, send failure response.
	 *
	 * @since 2.11.0
	 */
	protected function send_success_response() {
		// Bail early if not ajax.
		if ( ! masteriyo_is_ajax() ) {
			return;
		}

		// Only print notices if not reloading the checkout, otherwise they're lost in the page reload.
		$messages = masteriyo_display_all_notices( true );

		$response = array(
			'messages'  => isset( $messages ) ? $messages : '',
			'fragments' => masteriyo_get_checkout_fragments(),
		);

		wp_send_json_success( $response );
	}

	/**
	 * If the AJAX operation failed, send failure response.
	 *
	 * @since 2.11.0
	 */
	protected function send_failure_response() {
		// Bail early if not ajax.
		if ( ! masteriyo_is_ajax() ) {
			return;
		}

		// Only print notices if not reloading the checkout, otherwise they're lost in the page reload.
		$messages = masteriyo_display_all_notices( true );

		$response = array(
			'messages' => isset( $messages ) ? $messages : '',
		);

		wp_send_json_error( $response, 400 );
	}
}
