<?php
/**
 * Resource handler for Course Announcement data.
 *
 * @since 2.8.3
 */

namespace Masteriyo\Addons\CourseAnnouncement\Resources;

use Masteriyo\Helper\Utils;
use Masteriyo\Resources\CourseResource;
use Masteriyo\Resources\UserResource;

defined( 'ABSPATH' ) || exit;

/**
 * Resource handler for Course Announcement data.
 *
 * @since 2.8.3
 */
class CourseAnnouncementResource {


	/**
	 * Transform the resource into an array.
	 *
	 * @since 2.8.3
	 *
	 * @param \Masteriyo\Addons\CourseAnnouncement\Models\CourseAnnouncement $course_announcement Course announcement object.
	 * @param string $context What the value is for. Valid values are view and edit.
	 *
	 * @return array
	 */
	public static function to_array( $course_announcement, $context = 'view' ) {

		$author = masteriyo_get_user( $course_announcement->get_author_id( $context ) );

		if ( ! is_wp_error( $course_announcement->get_author_id() ) ) {
			$author = array(
				'id'           => $author->get_id(),
				'display_name' => $author->get_display_name(),
				'avatar_url'   => $author->profile_image_url(),
			);
		}

		$course = masteriyo_get_course( $course_announcement->get_course_id() );
		$author = masteriyo_get_user( $course_announcement->get_author_id( $context ) );

		$data = array(
			'title'         => $course_announcement->get_title(),
			'slug'          => $course_announcement->get_slug(),
			'description'   => $course_announcement->get_description(),
			'course_id'     => $course_announcement->get_course_id(),
			'author_id'     => $course_announcement->get_author_id(),
			'status'        => $course_announcement->get_status(),
			'menu_order'    => $course_announcement->get_menu_order(),
			'date_created'  => masteriyo_rest_prepare_date_response( $course_announcement->get_date_created() ),
			'date_modified' => masteriyo_rest_prepare_date_response( $course_announcement->get_date_modified() ),
			'course'        => CourseResource::to_array( $course ),
			'author'        => UserResource::to_array( $author ),
		);

		/**
		 * Filter course data array resource.
		 *
		 * @since 2.8.3
		 *
		 * @param array $data Course Announcement data.
		 * @param \Masteriyo\Addons\CourseAnnouncement\Models\CourseAnnouncement $course_announcement Course Announcement object.
		 * @param string $context What the value is for. Valid values are view and edit.
		 */
		return apply_filters( 'masteriyo_order_resource_array', $data, $course_announcement, $context );
	}

	/**
	 * Get taxonomy terms if a course.
	 *
	 * @since 2.8.3
	 *
	 * @param \Masteriyo\Addons\CourseAnnouncement\Models\CourseAnnouncement $course_announcement Course Announcement object.
	 * @param string $taxonomy Taxonomy slug.
	 *
	 * @return array
	 */
	protected static function get_taxonomy_terms( $course_announcement, $taxonomy = 'cat' ) {
		$terms = Utils::get_object_terms( $course_announcement->get_id(), 'course_announcement_' . $taxonomy );

		$terms = array_map(
			function ( $term ) {
				return array(
					'id'   => $term->term_id,
					'name' => $term->name,
					'slug' => $term->slug,
				);
			},
			$terms
		);

		$terms = 'difficulty' === $taxonomy ? array_shift( $terms ) : $terms;

		return $terms;
	}
}
