<?php

/**
 * GRade service provider.
 *
 * @since 2.5.20
 * @package \Masteriyo\Addons\Gradebook
 */

namespace Masteriyo\Addons\Gradebook\Providers;

defined( 'ABSPATH' ) || exit;

use Masteriyo\Addons\Gradebook\Models\Grade;
use Masteriyo\Addons\Gradebook\Controllers\GradesController;
use League\Container\ServiceProvider\AbstractServiceProvider;
use Masteriyo\Addons\Gradebook\Models\GradeResult;
use Masteriyo\Addons\Gradebook\Repository\GradeRepository;
use Masteriyo\Addons\Gradebook\Repository\GradeResultRepository;

/**
 * Gradebook service provider.
 *
 * @since 2.5.20
 */
class GradeServiceProvider extends AbstractServiceProvider {
	/**
	 * The provided array is a way to let the container
	 * know that a service is provided by this service
	 * provider. Every service that is registered via
	 * this service provider must have an alias added
	 * to this array or it will be ignored
	 *
	 * @since 2.5.20
	 *
	 * @var array
	 */
	protected $provides = array(
		'grade',
		'grade.store',
		'grade.rest',
		'mto-grade',
		'mto-grade.store',
		'mto-grade.rest',

		'grade-result',
		'grade-result.store',
		'grade-result.rest',
	);

	/**
	 * This is where the magic happens, within the method you can
	 * access the container and register or retrieve anything
	 * that you need to, but remember, every alias registered
	 * within this method must be declared in the `$provides` array.
	 *
	 * @since 2.5.20
	 */
	public function register() {
		$this->getContainer()->add( 'grade.store', GradeRepository::class );

		$this->getContainer()->add( 'grade.rest', GradesController::class )
			->addArgument( 'permission' );

		$this->getContainer()->add( GradesController::class )
			->addArgument( 'permission' );

		$this->getContainer()->add( 'grade', Grade::class )
			->addArgument( 'grade.store' );

		// Register based on post type.
		$this->getContainer()->add( 'mto-grade', Grade::class )
			->addArgument( 'grade.store' );

		$this->getContainer()->add( 'mto-grade.store', GradeRepository::class );

		$this->getContainer()->add( 'mto-grade.rest', GradesController::class )
				->addArgument( 'permission' );

		$this->getContainer()->add( 'grade-result.store', GradeResultRepository::class );

		$this->getContainer()->add( 'grade-result.rest', GradeResultsController::class )
			->addArgument( 'permission' );

		$this->getContainer()->add( 'grade-result', GradeResult::class )
			->addArgument( 'grade-result.store' );
	}
}
