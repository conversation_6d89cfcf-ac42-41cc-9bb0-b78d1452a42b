<?php

defined( 'ABSPATH' ) || exit;

/**
 * Template hooks and their corresponding functions.
 *
 * @since 2.2.7
 * @package Masteriyo\Addons\CourseFaq
 */

if ( ! function_exists( 'add_action' ) || ! function_exists( 'add_filter' ) ) {
	return;
}

add_action( 'masteriyo_single_course_main_content', 'masteriyo_single_course_faq', 50 );
add_action( 'masteriyo_layout_1_single_course_tabbar_content', 'masteriyo_single_course_faq', 50 );
add_action( 'masteriyo_single_course_main_content_tab', 'masteriyo_single_course_faq_tab', 10 );
add_action( 'masteriyo_layout_1_single_course_main_content_tabbar', 'masteriyo_single_course_faq_tab', 10 );


/**
 * Single course FAQ tab content.
 *
 * @since 2.2.7
 *
 * @param \Masteriyo\Models\Course $course Course object.
 */
function masteriyo_single_course_faq_tab( $course ) {
	if ( masteriyo_is_course_faq_enable( $course ) ) {
		$layout = masteriyo_get_setting( 'single_course.display.template.layout' ) ?? 'default';

		if ( 'layout1' === $layout ) {
			$html  = '<li class="masteriyo-single-body__main--tabbar-item" onClick="masteriyoSelectSingleCoursePageTabById(event);" data-tab-id="masteriyoSingleCourseFAQsTab">';
			$html .= esc_html__( 'FAQs', 'learning-management-system' );
			$html .= '</li>';

			echo $html; //phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
		} else {
			echo "<div class=\"masteriyo-tab\" onClick=\"masteriyo_select_single_course_page_tab(event, '.tab-content.course-faqs');\">";
			echo esc_html__( 'FAQs', 'learning-management-system' );
			echo '</div>';
		}
	}
}

if ( ! function_exists( 'masteriyo_single_course_faq' ) ) {
	/**
	 * Show course faq in single course page.
	 *
	 * @since 2.2.7
	 *
	 * @param \Masteriyo\Models\Course $course Course object.
	 */
	function masteriyo_single_course_faq( $course ) {
		if ( masteriyo_is_course_faq_enable( $course ) ) {
			$course_faqs = masteriyo_get_course_faqs(
				array(
					'course_id' => $course->get_id(),
					'orderby'   => 'comment_karma',
					'order'     => 'asc',
					'limit'     => '',
				)
			);
			require_once dirname( __FILE__ ) . '/templates/course-faq.php';
		}
	}
}
