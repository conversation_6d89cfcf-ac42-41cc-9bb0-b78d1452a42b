<?php
/**
 * Assignment Reply to instructor email class.
 *
 * @package Masteriyo\Emails
 *
 * @since 2.12.0
 */

namespace Masteriyo\Addons\Assignment\Emails;

defined( 'ABSPATH' ) || exit;

use Masteriyo\Abstracts\Email;

class NewAssignmentSubmissionEmailToInstructor extends Email {

	/**
	 * Email method ID.
	 *
	 * @since 2.12.0
	 *
	 * @var string
	 */
	protected $id = 'assignment-submission/to/instructor';

	/**
	 * HTML template path.
	 *
	 * @since 2.12.0
	 *
	 * @var string
	 */
	protected $html_template = 'emails/instructor/assignment-submission.php';

	/**
	 * Send this email.
	 *
	 * @since 2.12.0
	 *
	 * @param \Masteriyo\Addons\Assignment\Models\AssignmentReply $assignment_reply assignment reply object.
	 */
	public function trigger( $assignment_reply ) {
		$instructor_email = get_bloginfo( 'instructor_email' );

		// Bail early if order doesn't exist.
		if ( empty( $instructor_email ) ) {
			return;
		}

		$assignment_reply = masteriyo_get_assignment_reply( $assignment_reply );
		$assignment       = masteriyo_get_assignment( $assignment_reply->get_parent_id() );
		$course           = masteriyo_get_course( $assignment->get_course_id() );
		$user             = masteriyo_get_user( $assignment_reply->get_user_id() );
		$assignment       = masteriyo_get_assignment( $assignment_reply->get_parent_id() );

		$instructors            = array( $course->get_author_id() );
		$additional_instructors = $course->get_meta( '_additional_authors', false );

		if ( $additional_instructors ) {
			$instructors = array_merge( $instructors, $additional_instructors );
		}

		$instructors = array_filter(
			$instructors,
			function( $user_id ) {
				return get_option( 'masteriyo_assignment_reply_instructor_email_setting' )['enable'] ? ! masteriyo_is_user_admin( $user_id ) : true;
			}
		);

		$instructor_emails = array_map(
			function( $user_id ) {
				return get_the_author_meta( 'user_email', $user_id );
			},
			$instructors
		);

		$to_addresses_setting = get_option( 'masteriyo_assignment_reply_instructor_email_setting' );
		$to_addresses_setting = $to_addresses_setting['to_address'];
		$to_address           = array();

		if ( ! empty( $to_addresses_setting ) ) {
			$to_addresses_setting = str_replace( '{instructor_email}', implode( ', ', $instructor_emails ), $to_addresses_setting );
			$to_address           = explode( ',', $to_addresses_setting );
		}

		$this->set_recipients( ! empty( $to_address ) ? $to_address : $instructor_emails );
		$this->set( 'course_progress', $assignment_reply );
		$this->set( 'course', $course );
		$this->set( 'student', $user );
		$this->set( 'assignment', $assignment );
		$this->set( 'assignment_reply', $assignment_reply );

		$this->send(
			$this->get_recipients(),
			$this->get_subject(),
			$this->get_content(),
			$this->get_headers(),
			$this->get_attachments()
		);
	}

	/**
	 * Return true if it is enabled.
	 *
	 * @since 2.12.0
	 *
	 * @return bool
	 */
	public function is_enabled() {

		$instructor_settings = get_option( 'masteriyo_assignment_reply_instructor_email_setting' );

		return masteriyo_string_to_bool( $instructor_settings['enable'] );
	}

	/**
	 * Return subject.
	 *
	 * @since 2.12.0
	 *
	 * @return string
	 */
	public function get_subject() {
		/**
		 * Filter course start email subject to instructor.
		 *
		 * @since 2.12.0
		 *
		 * @param string $subject.
		 */
		$now     = get_option( 'masteriyo_assignment_reply_instructor_email_setting' );
		$subject = apply_filters( $this->get_full_id(), $now['subject'] );

		return $this->format_string( $subject );
	}

	/**
	 * Return heading.
	 *
	 * @since 2.12.0
	 *
	 * @return string
	 */
	public function get_heading() {
		/**
		 * Filter course start email heading to instructor.
		 *
		 * @since 2.12.0
		 *
		 * @param string $heading.
		 */
		$heading_data = get_option( 'masteriyo_assignment_reply_instructor_email_setting' );
		$heading      = apply_filters( $this->get_full_id() . '_heading', $heading_data['heading'] ? $heading_data['heading'] : 'New Assignment Submission' );

		return $this->format_string( $heading );
	}

	/**
	 * Return additional content.
	 *
	 * @since 2.12.0
	 *
	 * @return string
	 */
	public function get_additional_content() {

		/**
		 * Filter course start email additional content to instructor.
		 *
		 * @since 2.12.0
		 *
		 * @param string $additional_content.
		 */
		$additional_content_data = get_option( 'masteriyo_assignment_reply_instructor_email_setting' );
		$additional_content      = apply_filters( $this->get_full_id() . '_additional_content', isset( $additional_content_data['additional_content'] ) ? $additional_content_data['additional_content'] : '' );

		return $this->format_string( $additional_content );
	}

	/**
	 * Get email content.
	 *
	 * @since 2.12.0
	 *
	 * @return string
	 */
	public function get_content() {
		$content_data = get_option( 'masteriyo_assignment_reply_instructor_email_setting' );
		$content      = $content_data['content'];
		$content      = $this->format_string( $content );
		$this->set( 'content', $content );
		return parent::get_content();
	}

	/**
	 * Get placeholders.
	 *
	 * @since 2.12.0
	 *
	 * @return array
	 */
	public function get_placeholders() {
		$placeholders = parent::get_placeholders();

		/** @var \Masteriyo\Models\User|null $student */
		$student = $this->get( 'student' );

		/** @var \Masteriyo\Models\Course|null $course */
		$course = $this->get( 'course' );

		/** @var \Masteriyo\Models\Assignment|null $assignment */
		$assignment = $this->get( 'assignment' );

		/** @var \Masteriyo\Models\AssignmentReply|null $assignment_reply */
		$assignment_reply = $this->get( 'assignment_reply' );

		if ( $student ) {
			$placeholders = $placeholders + array(
				'{student_display_name}' => $student->get_display_name(),
				'{student_first_name}'   => $student->get_first_name(),
				'{student_last_name}'    => $student->get_last_name(),
				'{student_username}'     => $student->get_username(),
				'{student_nicename}'     => $student->get_nicename(),
				'{student_nickname}'     => $student->get_nickname(),
				'{student_name}'         => sprintf( '%s %s', $student->get_first_name(), $student->get_last_name() ) ?? $student->get_username(),
			);
		}

		if ( $course ) {
			$placeholders = $placeholders + array(
				'{course_name}'              => $course->get_name(),
				'{course_url}'               => $course->get_permalink(),
				'{course_short_description}' => $course->get_short_description(),
			);

			$instructor = masteriyo_get_user( absint( $course->get_author_id() ) );

			if ( $instructor ) {
				$placeholders = $placeholders + array(
					'{instructor_display_name}' => $instructor->get_display_name(),
					'{instructor_first_name}'   => $instructor->get_first_name(),
					'{instructor_last_name}'    => $instructor->get_last_name(),
					'{instructor_username}'     => $instructor->get_username(),
					'{instructor_nicename}'     => $instructor->get_nicename(),
					'{instructor_nickname}'     => $instructor->get_nickname(),
					'{instructor_name}'         => sprintf( '%s %s', $instructor->get_first_name(), $instructor->get_last_name() ) ?? $instructor->get_username(),
				);
			}
		}

		if ( $assignment ) {
			$placeholders = $placeholders + array(
				'{assignment_name}' => $assignment->get_title(),
			);
		}

		if ( $assignment_reply ) {
			$placeholders = $placeholders + array(
				'{assignment_submission_review_link}' => wp_kses_post(
					'<a href="' . admin_url( 'admin.php?page=masteriyo#/assignments/' ) . $assignment_reply->get_id() . '" style="text-decoration: none;">Review Assignment Submission</a>'
				),
			);
		}

		return $placeholders;
	}

	/**
	 * Get the reply_to_name.
	 *
	 * @since 2.12.0
	 *
	 * @return string
	 */
	public function get_reply_to_name() {
		/**
		 * Filter student registration email reply_to_name to instructor.
		 *
		 * @since 2.12.0
		 *
		 * @param string $reply_to_name.
		 */
		$reply_to_name_data = get_option( 'masteriyo_assignment_reply_instructor_email_setting' );
		$reply_to_name      = apply_filters( $this->get_full_id() . 'reply_to_name', $reply_to_name_data['reply_to_name'] );

		return ! empty( trim( $reply_to_name ) ) ? wp_specialchars_decode( esc_html( $reply_to_name ), ENT_QUOTES ) : parent::get_reply_to_name();
	}

	/**
	 * Get the reply_to_address.
	 *
	 * @since 2.12.0
	 *
	 * @return string
	 */
	public function get_reply_to_address( $reply_to_address = '' ) {
		/**
		 * Filter student registration email reply_to_address to instructor.
		 *
		 * @since 2.12.0
		 *
		 * @param string $reply_to_address.
		 */
		$reply_to_address_data = get_option( 'masteriyo_assignment_reply_instructor_email_setting' );
		$reply_to_address      = apply_filters( $this->get_full_id() . 'reply_to_address', $reply_to_address_data['reply_to_address'] );

		return ! empty( $reply_to_address ) ? sanitize_email( $reply_to_address ) : parent::get_reply_to_address();
	}

	/**
	 * Get the from_name.
	 *
	 * @since 2.12.0
	 *
	 * @return string
	 */
	public function get_from_name() {
		/**
		 * Filter student registration email from_name to instructor.
		 *
		 * @since 2.12.0
		 *
		 * @param string $from_name.
		 */
		$from_name_data = get_option( 'masteriyo_assignment_reply_instructor_email_setting' );
		$from_name      = apply_filters( $this->get_full_id() . '_from_name', $from_name_data['from_name'] );

		return ! empty( trim( $from_name ) ) ? wp_specialchars_decode( esc_html( $from_name ), ENT_QUOTES ) : parent::get_from_name();
	}

	/**
	 * Get the from_address.
	 *
	 * @since 2.12.0
	 *
	 * @return string
	 */
	public function get_from_address( $from_address = '' ) {
		/**
		 * Filter student registration email from_address to instructor.
		 *
		 * @since 2.12.0
		 *
		 * @param string $from_address.
		 */
		$from_address_data = get_option( 'masteriyo_assignment_reply_instructor_email_setting' );
		$from_address      = apply_filters( $this->get_full_id() . '_from_address', $from_address_data['from_address'] );

		return ! empty( trim( $from_address ) ) ? sanitize_email( $from_address ) : parent::get_from_address();
	}
}
