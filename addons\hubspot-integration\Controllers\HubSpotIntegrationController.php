<?php
/**
 * HubSpot Integration REST Controller.
 *
 * @since 2.14.4
 *
 * @subpackage Masteriyo\Addons\HubSpotIntegration
 */

namespace Masteriyo\Addons\HubSpotIntegration\Controllers;

defined( 'ABSPATH' ) || exit;

use Masteriyo\Addons\HubSpotIntegration\HubSpotIntegrationSettings;
use Masteriyo\Helper\Permission;
use Masteriyo\Addons\HubSpotIntegration\API\API;
use Masteriyo\RestApi\Controllers\Version1\CrudController;

/**
 * HubSpotIntegrationController class.
 *
 * @since 2.14.4
 */
class HubSpotIntegrationController extends CrudController {
	/**
	 * Endpoint namespace.
	 *
	 * @since 2.14.4
	 *
	 * @var string
	 */
	protected $namespace = 'masteriyo/pro/v1';

	/**
	 * Route base.
	 *
	 * @since 2.14.4
	 *
	 * @var string
	 */
	protected $rest_base = 'hubspot-integration';

	/** Object type.
	 *
	 * @since 2.14.4
	 *
	 * @var string
	 */
	protected $object_type = 'hubspot-integration';

	/**
	 * Permission class.
	 *
	 * @since 2.14.4
	 *
	 * @var \Masteriyo\Helper\Permission;
	 */
	protected $permission = null;

	/**
	 * API client instance.
	 *
	 * @since 2.14.4
	 *
	 * @var \Masteriyo\Addons\HubSpotIntegration\API\API
	 */
	private $api_client;

	/**
	 * Constructor.
	 *
	 * @since 2.14.4
	 *
	 * @param \Masteriyo\Helper\Permission $permission
	 */
	public function __construct( ?Permission $permission = null ) {
		$this->permission = $permission;
		$this->api_client = new API( HubSpotIntegrationSettings::get_access_token() );
	}

	/**
	 * Register routes.
	 *
	 * @since 2.14.4
	 *
	 * @return void
	 */
	public function register_routes() {
		register_rest_route(
			$this->namespace,
			'/' . $this->rest_base . '/lists',
			array(
				array(
					'methods'             => \WP_REST_Server::READABLE,
					'callback'            => array( $this, 'get_lists' ),
					'permission_callback' => array( $this, 'get_items_permissions_check' ),
				),
			)
		);

		register_rest_route(
			$this->namespace,
			'/' . $this->rest_base . '/connect',
			array(
				array(
					'methods'             => \WP_REST_Server::CREATABLE,
					'callback'            => array( $this, 'connect' ),
					'permission_callback' => array( $this, 'get_items_permissions_check' ),
				),
			)
		);

		register_rest_route(
			$this->namespace,
			'/' . $this->rest_base . '/disconnect',
			array(
				array(
					'methods'             => \WP_REST_Server::DELETABLE,
					'callback'            => array( $this, 'disconnect' ),
					'permission_callback' => array( $this, 'get_items_permissions_check' ),
				),
			)
		);
	}

	/**
	 * Check if a given request has access to read items.
	 *
	 * @since 2.14.4
	 *
	 * @param  WP_REST_Request $request Full details about the request.
	 *
	 * @return WP_Error|boolean
	 */
	public function get_items_permissions_check( $request ) {
		if ( is_null( $this->permission ) ) {
			return new \WP_Error(
				'masteriyo_null_permission',
				__( 'The permission object is missing. Unable to verify access rights.', 'learning-management-system' ),
				array( 'status' => 403 )
			);
		}

		return current_user_can( 'manage_options' ) || current_user_can( 'manage_masteriyo_settings' );
	}

	/**
	 * Get HubSpot lists.
	 *
	 * This function fetches the HubSpot lists from the API and caches them in the database.
	 *
	 * @since 2.14.4
	 *
	 * @param \WP_REST_Request $request The REST request object.
	 *
	 * @return \WP_REST_Response The response object containing the HubSpot lists.
	 */
	public function get_lists( $request ) {
		$force_fetch = masteriyo_string_to_bool( $request->get_param( 'force' ) );

		if ( $force_fetch ) {
			delete_option( 'masteriyo_hubspot_lists' );
		}

		$lists = get_option( 'masteriyo_hubspot_lists', array() );

		if ( empty( $lists ) ) {
			$lists = $this->api_client->get_lists();

			if ( is_wp_error( $lists ) ) {
				return $lists;
			}

			if ( isset( $lists['lists'] ) ) {
				$lists = $lists['lists'];
				update_option( 'masteriyo_hubspot_lists', $lists );
			} else {
				return new \WP_Error(
					'masteriyo_invalid_api_response',
					__( 'Failed to retrieve HubSpot lists. Please check the API key or try again later.', 'learning-management-system' ),
					array( 'status' => 500 )
				);
			}
		}

		return rest_ensure_response( $lists );
	}

	/**
	 * Connect to HubSpot with validating the access token.
	 *
	 * This function validates the provided access token and stores it in the settings if valid.
	 *
	 * @since 2.14.4
	 *
	 * @param \WP_REST_Request $request The REST request object.
	 *                                 Required parameter: access_token
	 *
	 * @return \WP_REST_Response The response object containing the success status and message.
	 *                           Error response: masteriyo_invalid_access_token
	 *
	 * @throws \WP_Error If the access token is invalid.
	 */
	public function connect( $request ) {
		$access_token = sanitize_text_field( $request['access_token'] ?? '' );
		$verify_again = masteriyo_string_to_bool( $request['verify_again'] ?? false );

		if ( $verify_again ) {
			$access_token = HubSpotIntegrationSettings::get_access_token();
		}

		if ( empty( $access_token ) ) {
			return new \WP_Error(
				'masteriyo_invalid_access_token',
				__( 'Access token is required to establish a connection.', 'learning-management-system' ),
				array( 'status' => 401 )
			);
		}

		$is_valid = $this->api_client->validate_access_token( $access_token );

		if ( ! $is_valid ) {
			return new \WP_Error(
				'masteriyo_invalid_access_token',
				__( 'The provided access token is invalid. Please verify the token and try again.', 'learning-management-system' ),
				array( 'status' => 401 )
			);
		}

		HubSpotIntegrationSettings::set( 'access_token', $access_token );
		HubSpotIntegrationSettings::set( 'is_connected', true );

		$message = $verify_again
		? __( 'Access token re-verified successfully.', 'learning-management-system' )
		: __( 'Connected to HubSpot successfully.', 'learning-management-system' );

		return rest_ensure_response(
			array(
				'success' => true,
				'message' => $message,
			)
		);
	}

	/**
	 * Disconnects from HubSpot integration.
	 *
	 * This function clears the access token and sets the is_connected flag to false.
	 *
	 * @since 2.14.4
	 *
	 * @param \WP_REST_Request $request The REST request object.
	 *
	 * @return \WP_REST_Response The response object containing the success status and message.
	 */
	public function disconnect( $request ) {
		HubSpotIntegrationSettings::set( 'access_token', '' );
		HubSpotIntegrationSettings::set( 'is_connected', false );
		HubSpotIntegrationSettings::settings_remove();

		return rest_ensure_response(
			array(
				'success' => true,
				'message' => __( 'Successfully disconnected from HubSpot.', 'learning-management-system' ),
			)
		);
	}
}
