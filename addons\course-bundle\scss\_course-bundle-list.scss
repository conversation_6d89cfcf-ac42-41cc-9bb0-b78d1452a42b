.post-type-archive-mto-bundle {
	.masteriyo-container {
		@media screen and (max-width: 600px) {
			padding-left: 0;
			padding-right: 0;
		}
	}
}

.masteriyo-bundles {
	display: grid !important;
	gap: 24px;
	margin: 0;

	&.columns {
		&-4 {
			grid-template-columns: repeat(4, 1fr);

			@media screen and (max-width: 1199px) {
				grid-template-columns: repeat(3, 1fr);
			}
		}

		&-3 {
			grid-template-columns: repeat(3, 1fr);
		}

		&-2 {
			grid-template-columns: repeat(2, 1fr);
		}

		&-1 {
			grid-template-columns: repeat(1, 1fr);
		}

		&-4,
		&-3 {
			@media screen and (max-width: 991px) {
				grid-template-columns: repeat(2, 1fr);
			}
		}

		&-4,
		&-3,
		&-2 {
			@media screen and (max-width: 600px) {
				grid-template-columns: repeat(1, 1fr);
			}
		}
	}

	@media screen and (max-width: 600px) {
		margin: 0;
	}

	.masteriyo-col {
		width: 100%; // important removed
		max-width: 100%; // important removed
		padding: 0 !important;

		@media screen and (max-width: 600px) {
			padding: 0;
		}

		.masteriyo-bundle {
			padding: 10px 10px 16px 10px;
			border-radius: 6px;
			background: #ffffff;
			border: 1px solid #eaeaea;
			flex-direction: column;
			row-gap: 12px;
			margin-left: 0;
			margin-right: 0;

			&--top {
				border-radius: 4px;
			}

			&__title {
				margin: 0;
				padding: 0;
				font-size: 18px;
				font-weight: 500;
				line-height: 28px;

				a {
					color: var(--masteriyo-color-primary);
				}
			}

			&--bottom {
				flex: 1;
			}

			&--top {
				display: flex;
				padding: 16px 18px;
				flex-direction: column;
				align-items: flex-start;
				gap: 14px;
				align-self: stretch;
				background: #4b53bc14;
			}

			&__price span {
				color: var(--masteriyo-color-primary);
			}

			&__info {
				display: flex;
				justify-content: space-between;
				align-items: center;
				width: 100%;
			}

			&__courses {
				height: 235px;
				overflow-y: auto;
				padding: 8px 16px 8px 8px;

				&-item {
					margin-bottom: 16px;
					padding-bottom: 0;
					border: none;

					&:last-child {
						margin-bottom: 0;
					}

					img {
						width: 90px;
						height: 65px;
						object-fit: cover;
					}

					&-info {
						h3 {
							font-size: 14px;
							font-weight: 500;
							line-height: 22px;
							color: #222222;
						}
					}

					&__price {
						color: #4e4e4e;
						font-size: 14px;
						font-weight: 500;
						line-height: 24px;
					}
				}

				&::-webkit-scrollbar {
					width: 4px;
					background-color: #d9d9d9;
				}
				&::-webkit-scrollbar-thumb {
					background-color: #5b5b5b;
					border-radius: 8px;
				}
				&::-webkit-scrollbar-corner {
					border-radius: 8px;
				}

				&-count {
					font-size: 14px;
					font-weight: 400;
					line-height: 16px;
					color: #4e4e4e;
				}
			}

			&__price {
				display: flex;
				align-items: center;
				gap: 6px;

				del {
					font-size: 12px;
					font-weight: 400;
					line-height: 22px;
					color: #909090;
				}

				span {
					color: var(--masteriyo-color-primary);
					font-size: 16px;
					font-weight: 500;
					line-height: 26px;
				}
			}

			&__cta {
				border-top: 1px solid #eaeaea;
				display: flex;
				justify-content: flex-end;
				margin-top: 10px;
				padding-top: 16px;

				.masteriyo-btn {
					padding: 8px 16px;
					border-radius: 3px;
					color: #ffffff;
					font-size: 14px;
					line-height: 22px;
					font-weight: 400;
				}
			}
		}
	}
}
