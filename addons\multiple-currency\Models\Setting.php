<?php
/**
 * Multiple Currency Setting Class.
 *
 * @package Masteriyo\Addons\MultipleCurrency\Models
 *
 * @since 1.11.0 [free]
 */

namespace Masteriyo\Addons\MultipleCurrency\Models;

defined( 'ABSPATH' ) || exit;

/**
 * Multiple Currency Setting class.
 */
class Setting {
	/**
	 * Global option name.
	 *
	 * @since 1.11.0 [free]
	 */
	const OPTION_NAME = 'masteriyo_multiple_currency_settings';

	/**
	 * Data.
	 *
	 * @since 1.11.0 [free]
	 *
	 * @var array
	 */
	protected static $data = array(
		'test_mode' => array(
			'enabled' => false,
			'country' => '',
		),
		'maxmind'   => array(
			'enabled'     => false,
			'license_key' => '',
		),
	);

	/**
	 * Read the settings.
	 *
	 * @since 1.11.0 [free]
	 */
	protected static function read() {
		$settings   = get_option( self::OPTION_NAME, self::$data );
		self::$data = masteriyo_parse_args( $settings, self::$data );

		return self::$data;
	}

	/**
	 * Return all the settings.
	 *
	 * @since 1.11.0 [free]
	 *
	 * @return mixed
	 */
	public static function all() {
		return self::read();
	}

	/**
	 * Return global white field value.
	 *
	 * @since 1.11.0 [free]
	 *
	 * @param string $key
	 *
	 * @return string|array
	 */
	public static function get( $key ) {
		self::read();

		return masteriyo_array_get( self::$data, $key, null );
	}

	/**
	 * Set global multiple currency field.
	 *
	 * @since 1.11.0 [free]
	 *
	 * @param string $key Setting key.
	 * @param mixed $value Setting value.
	 */
	public static function set( $key, $value ) {
		masteriyo_array_set( self::$data, $key, $value );
		self::save();
	}

	/**
	 * Set multiple settings.
	 *
	 * @since 1.11.0 [free]
	 *
	 * @param array $args
	 */
	public static function set_props( $args ) {
		self::$data = masteriyo_parse_args( $args, self::$data );
		self::save();
	}

	/**
	 * Save the settings.
	 *
	 * @since 1.11.0 [free]
	 */
	public static function save() {
		update_option( self::OPTION_NAME, self::$data );
	}
}
