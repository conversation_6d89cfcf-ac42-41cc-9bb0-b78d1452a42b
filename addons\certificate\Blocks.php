<?php
/**
 * Blocks class.
 *
 * @since 2.3.7
 */

namespace Masteriyo\Addons\Certificate;

defined( 'ABSPATH' ) || exit;

class Blocks {
	/**
	 * Init.
	 *
	 * @since 2.3.7
	 */
	public function init() {
		$this->init_hooks();
	}

	/**
	 * Constructor.
	 *
	 * @since 2.3.7
	 */
	private function init_hooks() {
		add_action( 'init', array( $this, 'register_blocks' ) );
	}

	/**
	 * Register all the blocks.
	 *
	 * @since 2.3.7
	 */
	public function register_blocks() {
		register_block_type(
			'masteriyo/certificate',
			array(
				'attributes'    => array(
					'blockCSS'           => array(
						'type' => 'string',
					),
					'backgroundImageURL' => array(
						'type' => 'string',
					),
					'backgroundImageID'  => array(
						'type' => 'number',
					),
					'containerWidth'     => array(
						'type' => 'number',
					),
					'paddingTop'         => array(
						'type'    => 'object',
						'default' => array(
							'value' => 100,
							'unit'  => 'px',
						),
					),
					'pageSize'           => array(
						'type' => 'string',
					),
					'pageOrientation'    => array(
						'type' => 'string',
					),
				),
				'style'         => 'masteriyo-public',
				'editor_script' => 'masteriyo-certificate-blocks',
				'editor_style'  => 'masteriyo-public',
			)
		);

		register_block_type(
			'masteriyo/course-title',
			array(
				'attributes'    => array(
					'clientId'  => array(
						'type' => 'string',
					),
					'blockCSS'  => array(
						'type' => 'string',
					),
					'alignment' => array(
						'type' => 'object',
					),
					'fontSize'  => array(
						'type' => 'object',
					),
					'textColor' => array(
						'type' => 'string',
					),
				),
				'style'         => 'masteriyo-public',
				'editor_script' => 'masteriyo-certificate-blocks',
				'editor_style'  => 'masteriyo-public',
			)
		);

		register_block_type(
			'masteriyo/student-name',
			array(
				'attributes'    => array(
					'clientId'   => array(
						'type' => 'string',
					),
					'blockCSS'   => array(
						'type' => 'string',
					),
					'alignment'  => array(
						'type' => 'object',
					),
					'fontSize'   => array(
						'type' => 'object',
					),
					'textColor'  => array(
						'type' => 'string',
					),
					'nameFormat' => array(
						'type' => 'string',
					),
				),
				'style'         => 'masteriyo-public',
				'editor_script' => 'masteriyo-certificate-blocks',
				'editor_style'  => 'masteriyo-public',
			)
		);

		register_block_type(
			'masteriyo/course-completion-date',
			array(
				'attributes'    => array(
					'clientId'   => array(
						'type' => 'string',
					),
					'blockCSS'   => array(
						'type' => 'string',
					),
					'alignment'  => array(
						'type' => 'object',
					),
					'fontSize'   => array(
						'type' => 'object',
					),
					'textColor'  => array(
						'type' => 'string',
					),
					'dateFormat' => array(
						'type'    => 'string',
						'default' => 'F j, Y',
					),
				),
				'style'         => 'masteriyo-public',
				'editor_script' => 'masteriyo-certificate-blocks',
				'editor_style'  => 'masteriyo-public',
			)
		);
		register_block_type(
			'masteriyo/qr-code',
			array(
				'attributes'    => array(
					'clientId'  => array(
						'type' => 'string',
					),
					'blockCSS'  => array(
						'type' => 'string',
					),
					'alignment' => array(
						'type' => 'object',
					),
				),
				'style'         => 'masteriyo-public',
				'editor_script' => 'masteriyo-certificate-blocks',
				'editor_style'  => 'masteriyo-public',
			)
		);
		register_block_type(
			'masteriyo/certificate-verification-code',
			array(
				'attributes'    => array(
					'clientId'  => array(
						'type' => 'string',
					),
					'blockCSS'  => array(
						'type' => 'string',
					),
					'alignment' => array(
						'type' => 'object',
					),
					'fontSize'  => array(
						'type' => 'object',
					),
					'textColor' => array(
						'type' => 'string',
					),
				),
				'style'         => 'masteriyo-public',
				'editor_script' => 'masteriyo-certificate-blocks',
				'editor_style'  => 'masteriyo-public',
			)
		);

		/**
		 * Registers a new Gutenberg block for displaying the course start date.
		 *
		 * The block allows the user to customize the date format, alignment, font size, and text color.
		 *
		 * @since 2.14.0
		 */
		register_block_type(
			'masteriyo/course-start-date',
			array(
				'attributes'    => array(
					'clientId'   => array(
						'type' => 'string',
					),
					'blockCSS'   => array(
						'type' => 'string',
					),
					'alignment'  => array(
						'type' => 'object',
					),
					'fontSize'   => array(
						'type' => 'object',
					),
					'textColor'  => array(
						'type' => 'string',
					),
					'dateFormat' => array(
						'type'    => 'string',
						'default' => 'F j, Y',
					),
				),
				'style'         => 'masteriyo-public',
				'editor_script' => 'masteriyo-certificate-blocks',
				'editor_style'  => 'masteriyo-public',
			)
		);

		/**
		 * Registers a new Gutenberg block for displaying the course grade result.
		 *
		 * The block allows the user to customize the alignment, font size, and text color.
		 *
		 * @since 2.14.0
		 */
		register_block_type(
			'masteriyo/course-grade-result',
			array(
				'attributes'    => array(
					'clientId'   => array(
						'type' => 'string',
					),
					'blockCSS'   => array(
						'type' => 'string',
					),
					'alignment'  => array(
						'type' => 'object',
					),
					'fontSize'   => array(
						'type' => 'object',
					),
					'textColor'  => array(
						'type' => 'string',
					),
					'nameFormat' => array(
						'type' => 'string',
					),
				),
				'style'         => 'masteriyo-public',
				'editor_script' => 'masteriyo-certificate-blocks',
				'editor_style'  => 'masteriyo-public',
			)
		);

		/**
		 * Registers a new Gutenberg block for displaying the instructor's name.
		 *
		 * The block allows the user to customize the name format, alignment, font size, and text color.
		 *
		 * @since 2.14.0
		 */
		register_block_type(
			'masteriyo/instructor-name',
			array(
				'attributes'    => array(
					'clientId'   => array(
						'type' => 'string',
					),
					'blockCSS'   => array(
						'type' => 'string',
					),
					'alignment'  => array(
						'type' => 'object',
					),
					'fontSize'   => array(
						'type' => 'object',
					),
					'textColor'  => array(
						'type' => 'string',
					),
					'nameFormat' => array(
						'type' => 'string',
					),
				),
				'style'         => 'masteriyo-public',
				'editor_script' => 'masteriyo-certificate-blocks',
				'editor_style'  => 'masteriyo-public',
			)
		);

		/**
		 * Registers a new Gutenberg block for displaying the co-instructors' names.
		 *
		 * The block allows the user to customize the name format, alignment, font size, and text color.
		 *
		 * @since 2.14.0
		 */
		register_block_type(
			'masteriyo/co-instructors-name',
			array(
				'attributes'    => array(
					'clientId'   => array(
						'type' => 'string',
					),
					'blockCSS'   => array(
						'type' => 'string',
					),
					'alignment'  => array(
						'type' => 'object',
					),
					'fontSize'   => array(
						'type' => 'object',
					),
					'textColor'  => array(
						'type' => 'string',
					),
					'nameFormat' => array(
						'type' => 'string',
					),
				),
				'style'         => 'masteriyo-public',
				'editor_script' => 'masteriyo-certificate-blocks',
				'editor_style'  => 'masteriyo-public',
			)
		);

		/**
		 * Registers a new Gutenberg block for displaying the course duration.
		 *
		 * The block allows the user to customize the alignment, font size, and text color.
		 *
		 * @since 2.14.0
		 */
		register_block_type(
			'masteriyo/course-duration',
			array(
				'attributes'    => array(
					'clientId'  => array(
						'type' => 'string',
					),
					'blockCSS'  => array(
						'type' => 'string',
					),
					'alignment' => array(
						'type' => 'object',
					),
					'fontSize'  => array(
						'type' => 'object',
					),
					'textColor' => array(
						'type' => 'string',
					),
				),
				'style'         => 'masteriyo-public',
				'editor_script' => 'masteriyo-certificate-blocks',
				'editor_style'  => 'masteriyo-public',
			)
		);

		/**
		 * Registers a new Gutenberg block for displaying the current date.
		 *
		 * The block allows the user to customize the date format, alignment, font size, and text color.
		 *
		 * @since 2.14.0
		 */
		register_block_type(
			'masteriyo/current-date',
			array(
				'attributes'    => array(
					'clientId'   => array(
						'type' => 'string',
					),
					'blockCSS'   => array(
						'type' => 'string',
					),
					'alignment'  => array(
						'type' => 'object',
					),
					'fontSize'   => array(
						'type' => 'object',
					),
					'textColor'  => array(
						'type' => 'string',
					),
					'dateFormat' => array(
						'type'    => 'string',
						'default' => 'F j, Y',
					),
				),
				'style'         => 'masteriyo-public',
				'editor_script' => 'masteriyo-certificate-blocks',
				'editor_style'  => 'masteriyo-public',
			)
		);

		/**
		 * Registers a new Gutenberg block for displaying the current time.
		 *
		 * The block allows the user to customize the time format, alignment, font size, and text color.
		 *
		 * @since 2.14.0
		 */
		register_block_type(
			'masteriyo/current-time',
			array(
				'attributes'    => array(
					'clientId'   => array(
						'type' => 'string',
					),
					'blockCSS'   => array(
						'type' => 'string',
					),
					'alignment'  => array(
						'type' => 'object',
					),
					'fontSize'   => array(
						'type' => 'object',
					),
					'textColor'  => array(
						'type' => 'string',
					),
					'timeFormat' => array(
						'type'    => 'string',
						'default' => 'g:i a',
					),
				),
				'style'         => 'masteriyo-public',
				'editor_script' => 'masteriyo-certificate-blocks',
				'editor_style'  => 'masteriyo-public',
			)
		);

		/**
		 * Registers a new Gutenberg block for displaying the full timestamp.
		 *
		 * The block allows the user to customize the timestamp format, alignment, font size, and text color.
		 *
		 * @since 2.14.0
		 */
		register_block_type(
			'masteriyo/current-timestamp',
			array(
				'attributes'    => array(
					'clientId'        => array(
						'type' => 'string',
					),
					'blockCSS'        => array(
						'type' => 'string',
					),
					'alignment'       => array(
						'type' => 'object',
					),
					'fontSize'        => array(
						'type' => 'object',
					),
					'textColor'       => array(
						'type' => 'string',
					),
					'timestampFormat' => array(
						'type'    => 'string',
						'default' => 'F j, Y g:i a',
					),
				),
				'style'         => 'masteriyo-public',
				'editor_script' => 'masteriyo-certificate-blocks',
				'editor_style'  => 'masteriyo-public',
			)
		);
	}
}
