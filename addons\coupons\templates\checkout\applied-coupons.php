<?php

/**
 * Applied coupons listing in order summary section.
 *
 * @since 2.5.12
 * @version 1.0.0
 */

use Masteriyo\Addons\Coupons\Enums\DeductionMethodType;

defined( 'ABSPATH' ) || exit;
?>

<?php foreach ( $coupon_discounts as $coupon_code => $discount_amount ) : ?>
	<li class="masteriyo-coupon-row">
		<div class="coupon-discount-label">
			<?php $coupon = masteriyo_get_coupon_by_code( $coupon_code ); ?>
			<?php if ( $coupon && DeductionMethodType::AUTOMATIC === $coupon->get_method() ) : ?>
				<p class="coupon-code" title="<?php echo esc_attr( $coupon->get_description() ); ?>">
					<?php esc_html_e( 'Coupon Auto Applied', 'learning-management-system' ); ?>
				</p>
			<?php else : ?>
				<strong><?php esc_html_e( 'Coupon Code', 'learning-management-system' ); ?>:&nbsp;</strong>
				<p class="coupon-code">
					<?php echo esc_html( $coupon_code ); ?>
				</p>
				<span>
					<a href="#" class="masteriyo-remove-coupon"
						title="<?php esc_attr_e( 'Remove Coupon', 'learning-management-system' ); ?>"
						data-coupon-code="<?php echo esc_attr( $coupon_code ); ?>">
						<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 25 25">
							<path stroke="#ff4c4c" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m18.506 6.547-12 12m0-12 12 12" />
						</svg>
					</a>
				</span>
			<?php endif; ?>
		</div>
		<div class="coupon-discount-amount">
			<span>-
				<?php
				echo wp_kses_post(
					masteriyo_price(
						$discount_amount,
						array(
							'show_price_free_text' => false,
							'currency'             => $currency,
						)
					)
				);
				?>
			</span>
		</div>
	</li>
<?php endforeach; ?>

<?php
wp_nonce_field( 'masteriyo_remove_applied_coupon', 'masteriyo-remove-applied-coupon-nonce' );
