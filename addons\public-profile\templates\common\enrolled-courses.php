<?php

/**
 * The Template for displaying enrolled courses tab content in the public profile page.
 *
 * @since 2.6.8
 */

use Masteriyo\Constants;

defined( 'ABSPATH' ) || exit; // Exit if accessed directly.

/**
 * Fires before rendering enrolled courses section in public profile page.
 *
 * @since 2.6.8
 */
do_action( 'masteriyo_before_public_profile_enrolled_courses' );

?>

<?php if ( count( $enrolled_courses ) ) : ?>

	<div class="masteriyo-enrolled-courses--content">
		<?php do_action( 'masteriyo_public_profile_enrolled_courses_content', $enrolled_courses, $user_id ); ?>
	</div>

	<?php do_action( 'masteriyo_after_public_profile_enrolled_courses_content', $total_pages, $current_page ); ?>

<?php else : ?>
	<?php masteriyo_display_template_notice( 'No enrolled courses.' ); ?>
<?php endif; ?>
<?php
/**
 * Fires after rendering enrolled courses section in public profile page.
 *
 * @since 2.6.8
 */
do_action( 'masteriyo_after_public_profile_enrolled_courses' );
