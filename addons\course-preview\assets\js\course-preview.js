/**
 * Masteriyo course preview JavaScript.
 *
 * @since 2.6.7
 */
(function ($) {
	/**
	 * Handles the course preview functionality.
	 *
	 * @since 2.6.7
	 */
	var masteriyoCoursePreview = {
		/**
		 * Initializes the course preview functionality.
		 *
		 * @since 2.6.7
		 */
		init: function () {
			this.bindUIActions();
		},

		/**
		 * Binds event handlers to elements.
		 *
		 * @since 2.6.7
		 */
		bindUIActions: function () {
			this.setupPreviewLinkClick();
			this.setupModalCloseClick();
			this.setupModalOutsideClick();
			this.setupPlaylistItemClick(
				'.masteriyo-course-preview-playlist-item',
				'#masteriyo-course-preview-video-player-container',
			);
		},

		/**
		 * Sets up the click event for preview links.
		 *
		 * @since 2.6.7
		 */
		setupPreviewLinkClick: function () {
			var $playlistItems = $('.masteriyo-course-preview-playlist-item');
			var $videoContainer = $(
				'#masteriyo-course-preview-video-player-container',
			);

			$(document.body).on(
				'click',
				'.masteriyo-course-preview-link',
				function () {
					var previewId = $(this).data('preview-id');

					var playlistItem = $playlistItems.filter(
						'[data-lesson-id="' + previewId + '"]',
					);

					$playlistItems.removeClass('active');
					playlistItem.addClass('active');
					masteriyoCoursePreview.updateVideoSource(
						playlistItem,
						$videoContainer,
					);
					$('#masteriyo-course-preview-video-playlist-modal').show();
				},
			);
		},

		/**
		 * Sets up the click event for modal close button.
		 *
		 * @since 2.6.7
		 */
		setupModalCloseClick: function () {
			$(document.body).on(
				'click',
				'.masteriyo-course-preview-modal-content .close',
				function () {
					masteriyoCoursePreview.closeModal();
				},
			);
		},

		/**
		 * Sets up the click event for outside the modal.
		 *
		 * @since 2.6.7
		 */
		setupModalOutsideClick: function () {
			var $modal = $('#masteriyo-course-preview-video-playlist-modal');

			$modal.on('click', function (event) {
				if (event.target === $modal[0]) {
					masteriyoCoursePreview.closeModal();
				}
			});
		},

		/**
		 * Closes the modal and resets the video player.
		 *
		 * @since 2.6.7
		 */
		closeModal: function () {
			$('#masteriyo-course-preview-video-playlist-modal').hide();
			$('#masteriyo-course-preview-video-player-container')
				.find('iframe')
				.attr('src', '');
			$('#masteriyo-course-preview-video-player-container')
				.find('video')
				.attr('src', '');
		},

		/**
		 * Sets up the click event for playlist items.
		 *
		 * @since 2.6.7
		 *
		 * @param {string} itemSelector - The selector for playlist items.
		 * @param {string} playerContainerSelector - The selector for the video player.
		 */
		setupPlaylistItemClick: function (itemSelector, playerContainerSelector) {
			$(document.body).on('click', itemSelector, function () {
				var $playlistItems = $(itemSelector);
				var $playerContainer = $(playerContainerSelector);
				var playlistItem = $(this);

				$playlistItems.removeClass('active');
				playlistItem.addClass('active');

				masteriyoCoursePreview.updateVideoSource(
					playlistItem,
					$playerContainer,
				);
			});
		},

		/**
		 * Updates the video source based on the playlist item and player container.
		 *
		 * @since 2.6.7
		 *
		 * @param {jQuery} playlistItem - The playlist item element.
		 * @param {jQuery} playerContainer - The video player container element.
		 */
		updateVideoSource: function (playlistItem, playerContainer) {
			var videoSrc = playlistItem.data('video-src');
			var useIframe = JSON.parse(playlistItem.data('iframe'));
			var $video = playerContainer.find('video');
			var $iframe = playerContainer.find('iframe');

			var lessonId = playlistItem.data('lesson-id');

			var $previewLink = $(
				'.masteriyo-course-preview-link[data-preview-id=' + lessonId + ']',
			);
			var lessonTitle = $previewLink.data('lesson-title');
			var categories = $previewLink.data('categories');

			$('.masteriyo-course-preview__header-title-sub-title').html(categories);
			$('.masteriyo-course-preview__header-title-main-title').html(lessonTitle);

			$video.attr('src', useIframe ? '' : videoSrc).toggle(!useIframe);
			$iframe.attr('src', useIframe ? videoSrc : '').toggle(useIframe);

			// For autoplay YouTube or Vimeo video in the iframe.
			if (
				useIframe &&
				'external' !== playlistItem.data('source') &&
				'embed-video' !== playlistItem.data('source')
			) {
				$iframe.one('load', function () {
					var videoSrc = playlistItem.data('video-src');
					var autoplayUrl = videoSrc + '?' + $.param({ autoplay: 1 });
					$iframe.attr('src', autoplayUrl);
				});
			} else if ('embed-video' === playlistItem.data('source')) {
				var videoSrc = playlistItem.data();

				$iframe.one('load', function () {
					var url = new URL(videoSrc);
					if (!url.searchParams.has('autoplay')) {
						url.searchParams.set('autoplay', 1);
					}
					url.searchParams.set('loop', 1);
					var autoplayUrl = url.toString();

					$iframe.attr('src', autoplayUrl);
				});
			}
		},
	};

	masteriyoCoursePreview.init();
})(jQuery);
