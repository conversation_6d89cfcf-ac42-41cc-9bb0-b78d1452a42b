<?php
/**
 * OrderItemCourseBundle class.
 *
 * @since 2.12.0
 */

namespace Masteriyo\Addons\CourseBundle\Models;

defined( 'ABSPATH' ) || exit;


use Masteriyo\Addons\CourseBundle\Repository\OrderItemCourseBundleRepository;
use Masteriyo\Models\Order\OrderItem;
use Masteriyo\PostType\PostType;

/**
 * Order item course bundle.
 */
class OrderItemCourseBundle extends OrderItem {
	protected $extra_data = array(
		'course_bundle_id' => 0,
		'quantity'         => 1,
		'subtotal'         => 0,
		'total'            => 0,
	);

	/**
	 * Get the order item if ID
	 *
	 * @since 2.12.0
	 *
	 * @param OrderItemCourseBundleRepository $repository Order Repository.
	 */
	public function __construct( OrderItemCourseBundleRepository $repository ) {
		parent::__construct();
		$this->repository = $repository;
	}

	/**
	 * Get formatted subtotal for rest.
	 *
	 * @since 2.12.0
	 *
	 * @param  string $context What the value is for. Valid values are view and edit.
	 *
	 * @return string
	 */
	public function get_rest_formatted_subtotal( $context = 'view' ) {
		$subtotal = masteriyo_price( $this->get_subtotal( $context ), array( 'currency' => $this->get_order()->get_currency() ) );

		/**
		 * Filters the rest formatted subtotal for course.
		 *
		 * @since 2.12.0
		 *
		 * @param integer $subtotal The total.
		 * @param Masteriyo\Models\Order\OrderItemCourse $order_item_course The order object.
		 */
		return apply_filters( 'masteriyo_order_item_course_formatted_subtotal', $subtotal, $this );
	}


	/**
	 * Get formatted total for rest.
	 *
	 * @since 2.12.0
	 *
	 * @param  string $context What the value is for. Valid values are view and edit.
	 *
	 * @return string
	 */
	public function get_rest_formatted_total( $context = 'view' ) {
		$args  = array(
			'currency' => $this->get_order()->get_currency(),
			'html'     => false,
		);
		$total = masteriyo_price( $this->get_total( $context ), $args );

		/**
		 * Filters the rest formatted total for order item course.
		 *
		 * @since 2.12.0
		 *
		 * @param integer $total The total.
		 * @param Masteriyo\Models\Order\OrderItemCourse $order_item_course The order item course object.
		 */
		return apply_filters( 'masteriyo_order_item_course_formatted_total', $total, $this );
	}

	/**
	 * Get the course bundle ID.
	 *
	 * @since  2.12.0
	 *
	 * @param  string $context What the value is for. Valid values are view and edit.
	 *
	 * @return string
	 */
	public function get_course_bundle_id( $context = 'view' ) {
		return $this->get_prop( 'course_bundle_id', $context );
	}

	/**
	 * Get the course bundle type.
	 *
	 * @since  2.12.0
	 *
	 * @param  string $context What the value is for. Valid values are view and edit.
	 *
	 * @return string
	 */
	public function get_type( $context = 'view' ) {
		return 'course-bundle';
	}

	/**
	 * Get the courses quantity.
	 *
	 * @since  2.12.0
	 *
	 * @param  string $context What the value is for. Valid values are view and edit.
	 *
	 * @return string
	 */
	public function get_quantity( $context = 'view' ) {
		return $this->get_prop( 'quantity', $context );
	}

	/**
	 * Get the sub total amount.
	 *
	 * @since  2.12.0
	 *r
	 * @param  string $context What the value is for. Valid values are view and edit.
	 *
	 * @return string
	 */
	public function get_subtotal( $context = 'view' ) {
		return $this->get_prop( 'subtotal', $context );
	}

	/**
	 * Get the total amount.
	 *
	 * @since  2.12.0
	 *r
	 * @param  string $context What the value is for. Valid values are view and edit.
	 *
	 * @return string
	 */
	public function get_total( $context = 'view' ) {
		return $this->get_prop( 'total', $context );
	}

	/**
	 * Set course bundle id.
	 *
	 * @since 2.12.0
	 *
	 * @param string $course_bundle_id Course bundle ID.
	 */
	public function set_course_bundle_id( $course_bundle_id ) {
		if ( $course_bundle_id > 0 && PostType::COURSE_BUNDLE !== get_post_type( absint( $course_bundle_id ) ) ) {
			$this->error( 'order_item_course_invalid_course_bundle_id', __( 'Invalid course bundle ID', 'learning-management-system' ) );
		}
		$this->set_prop( 'course_bundle_id', absint( $course_bundle_id ) );
	}

	/**
	 * Get the associated course bundle.
	 *
	 * @since 2.12.0
	 *
	 * @return CourseBundle|null
	 */
	public function get_course_bundle() {
		$course_bundle = masteriyo_get_course_bundle( $this->get_course_bundle_id() );

		/**
		 * Filters course bundle object of an order item.
		 *
		 * @since 2.12.0
		 *
		 * @param CourseBundle|null $course The course object of an order item.
		 * @param Masteriyo\Models\Order\OrderItemCourse $order_item_course Order item course object.
		 */
		return apply_filters( 'masteriyo_order_item_course_bundle', $course_bundle, $this );
	}

	/**
	 * Set properties based on passed in course bundle object.
	 *
	 * @since 2.12.0
	 *
	 * @param CourseBundle $course_bundle Course object.
	 */
	public function set_course_bundle( $course_bundle ) {

		if ( ! is_a( $course_bundle, 'Masteriyo\Addons\CourseBundle\Models\CourseBundle' ) ) {
			$this->error( 'order_item_course_invalid_course_bundle', __( 'Invalid course bundle', 'learning-management-system' ) );
		}

		$this->set_course_bundle_id( $course_bundle->get_id() );
		$this->set_name( $course_bundle->get_name() );
	}

	/**
	 * Set the course quantity.
	 *
	 * @since 2.12.0
	 *
	 * @param string $quantity course ID.
	 */
	public function set_quantity( $quantity ) {
		$this->set_prop( 'quantity', masteriyo_stock_amount( $quantity ) );
	}

	/**
	 * Line subtotal (before discounts).
	 *
	 * @since 2.12.0
	 *
	 * @param string $sub_total Subtotal.
	 */
	public function set_subtotal( $sub_total ) {
		$sub_total = masteriyo_format_decimal( $sub_total );

		if ( ! is_numeric( $sub_total ) ) {
			$sub_total = 0;
		}

		$this->set_prop( 'subtotal', $sub_total );
	}

	/**
	 * Setline total amount (after discounts).
	 *
	 * @since 2.12.0
	 *
	 * @param double $total Total amount.
	 */
	public function set_total( $total ) {
		$total = masteriyo_format_decimal( $total );

		if ( ! is_numeric( $total ) ) {
			$total = 0;
		}

		$this->set_prop( 'total', $total );

		// Subtotal cannot be less than total.
		if ( '' === $this->get_subtotal() || $this->get_subtotal() < $this->get_total() ) {
			$this->set_subtotal( $total );
		}
	}
}
