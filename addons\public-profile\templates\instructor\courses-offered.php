<?php

/**
 * The Template for displaying instructor's courses offered tab content in the public profile page.
 *
 * @since 2.6.8
 */

use Masteriyo\Constants;

defined( 'ABSPATH' ) || exit; // Exit if accessed directly.

/**
 * Fires before rendering courses offered section in public profile page.
 *
 * @since 2.6.8
 */
do_action( 'masteriyo_before_public_profile_courses_offered' );

?>

<?php
if ( empty( $offered_courses_list ) ) {
	masteriyo_display_template_notice( 'No courses offered.' );
} else {
	echo $offered_courses_list; //phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
}

	/**
	 * Fires an action after rendering the courses offered section in the public profile page.
	 *
	 * This action can be used to add additional content or perform other actions after the courses offered section has been rendered.
	 *
	 * @since 2.6.8
	 * @param int $total_pages The total number of pages for the courses offered.
	 * @param int $current_page The current page number for the courses offered.
	 */
	do_action( 'masteriyo_after_public_profile_courses_offered_content', $total_pages, $current_page );

/**
 * Fires after rendering courses offered section in public profile page.
 *
 * @since 2.6.8
 */
do_action( 'masteriyo_after_public_profile_courses_offered' );
