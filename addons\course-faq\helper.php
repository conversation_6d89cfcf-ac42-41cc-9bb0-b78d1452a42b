<?php

defined( 'ABSPATH' ) || exit;

/**
 * Course FAQ addon helper functions.
 *
 * @since 2.2.7
 * @package Masteriyo\Addons\CourseFaq
 */

/**
 * Return true if the course FAQ is enable.
 *
 * @since 2.2.7
 *
 * @param int|Course|WP_Post $course Course id or Course Model or Post.
 * @return boolean
*/
function masteriyo_is_course_faq_enable( $course ) {
	$course = masteriyo_get_course( $course );
	$enable = false;

	if ( $course ) {
		$enable = masteriyo_string_to_bool( $course->get_meta( '_course_faq_enable' ) );
	}

	/**
	 * Filters course FAQ enabled or not.
	 *
	 * @since 2.2.7
	 *
	 * @param boolean $enable Course FAQ enable or not.
	 * @param \Masteriyo\Models\Course $course Course object.
	 */
	return apply_filters( 'masteriyo_is_course_faq_enable', $enable, $course );
}

/**
 * Get course Faq.
 *
 * @since 2.2.7
 *
 * @param int|Masteriyo\Addons\CourseFaq\Models\CourseFaq|WP_Post $faq Faq id or Faq Model or Post.
 *
 * @return Masteriyo\Addons\CourseFaq\Models\CourseFaq|null
 */
function masteriyo_get_faq( $course_faq ) {
	$course_faq_obj   = masteriyo( 'course-faq' );
	$course_faq_store = masteriyo( 'course-faq.store' );

	if ( is_a( $course_faq, 'Masteriyo\Addons\CourseFaq\Models\CourseFaq' ) ) {
		$id = $course_faq->get_id();
	} elseif ( is_a( $course_faq, 'WP_Comment' ) ) {
		$id = $course_faq->comment_ID;
	} else {
		$id = $course_faq;
	}

	try {
		$id = absint( $id );
		$course_faq_obj->set_id( $id );
		$course_faq_store->read( $course_faq_obj );
	} catch ( \Exception $e ) {
		return null;
	}

	/**
	 * Filters faq object.
	 *
	 * @since 2.2.7
	 *
	 * @param Masteriyo\Addons\CourseFaq\Models\CourseFaq $course_faq_obj faq object.
	 * @param int|Masteriyo\Addons\CourseFaq\Models\CourseFaq|WP_Post $course_faq faq id or faq Model or Post.
	 */
	return apply_filters( 'masteriyo_get_faq', $course_faq_obj, $course_faq );
}

/**
 * Get Faqs
 *
 * @since 2.2.7
 *
 * @param array $args Query arguments.
 *
 * @return Masteriyo\Addons\CourseFaq\Models\CourseFaq[]
 */
function masteriyo_get_course_faqs( $args = array() ) {
	$course_faqs = masteriyo( 'query.course-faqs' )->set_args( $args )->get_course_faqs();

	/**
	 * Filters queried course faq objects.
	 *
	 * @since 2.2.7
	 *
	 * @param Masteriyo\Addons\CourseFaq\Models\CourseFaq[] $course_faqs Queried course faqs.
	 * @param array $args Query args.
	 */
	return apply_filters( 'masteriyo_get_course_faqs', $course_faqs, $args );
}
