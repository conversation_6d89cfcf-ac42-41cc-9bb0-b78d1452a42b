<?php

/**
 * Helper.
 *
 * @since 2.7.3
 *
 * @package Masteriyo\Addons\RCPIntegration
 */

namespace Masteriyo\Addons\RCPIntegration;

defined( 'ABSPATH' ) || exit;


/**
 * Helper.
 *
 * @since 2.7.3
 */
class Helper {
	/**
	 * Is Restrict Content Pro active.
	 *
	 * @since 2.7.3
	 *
	 * @return bool
	 */
	public static function is_rcp_active() {
		return in_array( 'restrict-content-pro/restrict-content-pro.php', get_option( 'active_plugins', array() ), true );
	}

	/**
	 * Checks if a user has access to a specific item based on RCP membership.
	 *
	 * @since 2.7.3
	 *
	 * @param int $user_id User ID to check access for.
	 * @param int $item_id Item ID to check access against.
	 *
	 * @return bool True if user has access, false otherwise.
	 */
	public static function user_has_access( $user_id, $item_id ) {
		return function_exists( 'rcp_user_can_access' ) && \rcp_user_can_access( $user_id, $item_id );
	}
}
