<?php

defined( 'ABSPATH' ) || exit;

/**
 * Addon Name: Paid Memberships Pro Integration
 * Addon URI: https://masteriyo.com/wordpress-lms/
 * Addon Type: integration
 * Description: Enhancing your ability to monetize and manage course access within the Masteriyo learning environment.
 * Author: Masteriyo
 * Author URI: https://masteriyo.com
 * Requires: Paid Membership Pro
 * Plan: Starter,Pro,Elite,Growth,Scale
 */

use Masteriyo\Pro\Addons;
use Masteriyo\Addons\PMPROIntegration\Helper;
use Masteriyo\Addons\PMPROIntegration\PMPROIntegrationAddon;

define( 'MASTERIYO_PMPRO_INTEGRATION_ADDON_FILE', __FILE__ );
define( 'MASTERIYO_PMPRO_INTEGRATION_ADDON_BASENAME', plugin_basename( __FILE__ ) );
define( 'MASTERIYO_PMPRO_INTEGRATION_ADDON_DIR', __DIR__ );
define( 'MASTERIYO_PMPRO_INTEGRATION_ASSETS', __DIR__ . '/assets' );
define( 'MASTERIYO_PMPRO_INTEGRATION_TEMPLATES', __DIR__ . '/templates' );
define( 'MASTERIYO_PMPRO_INTEGRATION_ADDON_SLUG', 'pmpro-integration' );

if ( ( new Addons() )->is_active( MASTERIYO_PMPRO_INTEGRATION_ADDON_SLUG ) && ! Helper::is_pmpro_active() ) {
	add_action(
		'masteriyo_admin_notices',
		function() {
			printf(
				'<div class="notice notice-warning is-dismissible"><p><strong>%s </strong>%s</p><button type="button" class="notice-dismiss"><span class="screen-reader-text">%s</span></button></div>',
				esc_html( 'Masteriyo PRO:' ),
				esc_html__( 'Paid Memberships Pro Integration addon requires Paid Memberships Pro to be installed and activated.', 'learning-management-system' ),
				esc_html__( 'Dismiss this notice.', 'learning-management-system' )
			);
		}
	);
}

if ( ! Helper::is_pmpro_active() ) {
	add_filter(
		'masteriyo_pro_addon_pmpro-integration_activation_requirements',
		function ( $result, $request, $controller ) {
			$result = __( 'Paid Memberships Pro is to be installed and activated for this addon to work properly', 'learning-management-system' );
			return $result;
		},
		10,
		3
	);

	add_filter(
		'masteriyo_pro_addon_data',
		function( $data, $slug ) {
			if ( 'pmpro-integration' === $slug ) {
				$data['requirement_fulfilled'] = masteriyo_bool_to_string( Helper::is_pmpro_active() );
			}

			return $data;
		},
		10,
		2
	);
}

// Bail early if the addon is not active.
if ( ! ( ( new Addons() )->is_active( MASTERIYO_PMPRO_INTEGRATION_ADDON_SLUG ) && Helper::is_pmpro_active() ) ) {
	return;
}

// Initialize Paid Membership Pro integration addon.
PMPROIntegrationAddon::instance()->init();
