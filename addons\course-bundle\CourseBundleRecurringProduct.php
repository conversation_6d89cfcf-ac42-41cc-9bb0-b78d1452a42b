<?php
/**
 * Course Bundle recurring product type for WooCommerce.
 *
 * @since 2.14.0
 * @package Masteriyo\Addons\WcIntegration
 */

namespace Masteriyo\Addons\CourseBundle;

defined( 'ABSPATH' ) || exit;


use Masteriyo\Addons\WcIntegration\CourseRecurringProduct;

class CourseBundleRecurringProduct extends CourseRecurringProduct {

	/**
	 * Get internal type.
	 *
	 * @since 2.14.0
	 *
	 * @return string
	 */
	public function get_type() {
		return 'mto_course_bundle_recurring';
	}

	/**
	 * Check if a product is sold individually (no quantities).
	 *
	 * @since 2.14.0
	 *
	 * @return bool
	 */
	public function is_sold_individually() {
		return apply_filters( 'woocommerce_is_sold_individually', true, $this );
	}

	/**
	 * Return if product manage stock.
	 *
	 * @since  2.7.0
	 * @param  string $context What the value is for. Valid values are view and edit.
	 * @return boolean
	 */
	public function get_manage_stock( $context = 'view' ) {
		return false;
	}

	/**
	 * Get virtual.
	 *
	 * @since  2.7.0
	 * @param  string $context What the value is for. Valid values are view and edit.
	 * @return bool
	 */
	public function get_virtual( $context = 'view' ) {
		return true;
	}

	/**
	 * Get downloadable.
	 *
	 * @since  2.7.0
	 * @param  string $context What the value is for. Valid values are view and edit.
	 * @return bool
	 */
	public function get_downloadable( $context = 'view' ) {
		return true;
	}
}
