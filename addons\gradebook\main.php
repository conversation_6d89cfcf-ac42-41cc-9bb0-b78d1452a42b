<?php

defined( 'ABSPATH' ) || exit;

/**
 * Addon Name: Gradebook
 * Addon URI: https://masteriyo.com/wordpress-lms/
 * Description: Show you how students performs on each course quizzes and assignments.
 * Author: Masteriyo
 * Author URI: https://masteriyo.com
 * Addon Type: enhancement
 * Plan: Starter,Pro,Elite,Growth,Scale
 */

use Masteriyo\Pro\Addons;

define( 'MASTERIYO_GRADEBOOK_ADDON_FILE', __FILE__ );
define( 'MASTERIYO_GRADEBOOK_ADDON_BASENAME', plugin_basename( __FILE__ ) );
define( 'MASTERIYO_GRADEBOOK_ADDON_DIR', __DIR__ );
define( 'MASTERIYO_GRADEBOOK_ADDON_SLUG', 'gradebook' );


add_filter(
	'masteriyo_pro_addon_gradebook_activation_requirements',
	function ( $result, $request, $controller ) {
		$addons = masteriyo( 'addons' );

		if ( ! $addons->is_allowed( 'gradebook' ) ) {
			$result = __( 'This addon is not allowed in your plan.', 'learning-management-system' );
		}

		return $result;
	},
	10,
	3
);

add_filter(
	'masteriyo_pro_addon_data',
	function( $data, $slug ) {
		if ( 'gradebook' !== $slug ) {
			return $data;
		}

		$fulfilled = true;
		$addons    = masteriyo( 'addons' );

		if ( ! $addons->is_allowed( 'gradebook' ) ) {
			$fulfilled = false;
		}

		$data['requirement_fulfilled'] = masteriyo_bool_to_string( $fulfilled );

		return $data;
	},
	10,
	2
);

// Bail early if the addon is not active.
if ( ! ( new Addons() )->is_active( MASTERIYO_GRADEBOOK_ADDON_SLUG ) ) {
	return;
}

require_once __DIR__ . '/helper/gradebook.php';

/**
 * Include service providers for gradebook.
*/
add_filter(
	'masteriyo_service_providers',
	function( $providers ) {
		return array_merge( $providers, require_once __DIR__ . '/config/providers.php' );
	}
);

/**
 * Initialize Masteriyo gradebook.
*/
add_action(
	'masteriyo_before_init',
	function() {
		masteriyo( 'addons.gradebook' )->init();
	}
);
