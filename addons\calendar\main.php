<?php

defined( 'ABSPATH' ) || exit;

/**
 * Addon Name: Event Calendar
 * Addon URI: https://masteriyo.com/wordpress-lms/
 * Description: Event Calendar addon by Masteriyo shows monthly schedule of assignments, quizzes, lessons, and Zoom meetings.
 * Author: Masteriyo
 * Author URI: https://masteriyo.com
 * Addon Type: Feature
 * Plan: Starter,Pro,Elite,Growth,Scale
 */

use Masteriyo\Pro\Addons;
use Masteriyo\Addons\Calendar\CalendarAddon;

define( 'MASTERIYO_CALENDAR_ADDON_FILE', __FILE__ );
define( 'MASTERIYO_CALENDAR_ADDON_BASENAME', plugin_basename( __FILE__ ) );
define( 'MASTERIYO_CALENDAR_ADDON_DIR', __DIR__ );
define( 'MASTERIYO_CALENDAR_ADDON_SLUG', 'calendar' );
define( 'MASTERIYO_CALENDAR_ADDON_TEMPLATES', __DIR__ . '/templates' );
define( 'MASTERIYO_CALENDAR_ADDON_ASSETS_URL', plugins_url( 'assets', MASTERIYO_CALENDAR_ADDON_FILE ) );

// Bail early if the addon is not active.
if ( ! ( new Addons() )->is_active( MASTERIYO_CALENDAR_ADDON_SLUG ) ) {
	return;
}

// Include the Calendar helper file that possibly contains necessary configurations, functions, and setups for the Calendar integration.
require_once __DIR__ . '/helper/calendar.php';

// Initialize the Calendar addon.
CalendarAddon::instance()->init();
