<?php
/**
 * Masteriyo Razorpay setup.
 *
 * @package Masteriyo\Addons\Razorpay
 *
 * @since 2.7.1
 */
namespace Masteriyo\Addons\Razorpay;

use Masteriyo\Addons\Razorpay\AjaxHandlers\PaymentSignatureVerification;

defined( 'ABSPATH' ) || exit;
/**
 * Main Masteriyo Razorpay class.
 *
 * @class Masteriyo\Addons\Razorpay
 */
class RazorpayAddon {
	/**
	 * Instance
	 *
	 * @since 2.7.1
	 *
	 * @var \Masteriyo\Addons\Razorpay\RazorpayAddon
	 */
	protected static $instance = null;

	/**
	 * Constructor.
	 *
	 * @since 2.7.1
	 */
	private function __construct() {
	}

	/**
	 * Return the instance.
	 *
	 * @since 2.7.1
	 *
	 * @return \Masteriyo\Addons\Razorpay\RazorpayAddon
	 */
	public static function instance() {
		if ( null === self::$instance ) {
			self::$instance = new self();
		}

		return self::$instance;
	}

	/**
	 * Initialize module.
	 *
	 * @since 2.7.1
	 */
	public function init() {
		$this->init_hooks();
	}

	/**
	 * Initialize hooks.
	 *
	 * @since 2.7.1
	 */
	public function init_hooks() {
		add_filter( 'masteriyo_ajax_handlers', array( $this, 'register_ajax_handlers' ) );

		add_filter( 'masteriyo_enqueue_scripts', array( $this, 'load_scripts' ) );

		add_filter( 'masteriyo_rest_response_setting_data', array( $this, 'append_setting_in_response' ), 10, 4 );
		add_action( 'masteriyo_new_setting', array( $this, 'save_razorpay_settings' ), 10, 1 );

		add_filter( 'masteriyo_payment_gateways', array( $this, 'add_payment_gateway' ), 11, 1 );

	}

	/**
	 * Register ajax handlers.
	 *
	 * @since 2.7.1
	 *
	 * @param array $handlers
	 *
	 * @return array
	 */
	public function register_ajax_handlers( $handlers ) {
		$handlers[] = PaymentSignatureVerification::class;

		return $handlers;
	}

	/**
	 * Load scripts.
	 *
	 * @since 2.0.0
	 *
	 * @param array $scripts Scripts which are to be loaded.
	 *
	 * @return array
	 */
	public function load_scripts( $scripts ) {
		$suffix = defined( 'SCRIPT_DEBUG' ) && SCRIPT_DEBUG ? '' : '.min';

		return array_merge(
			$scripts,
			array(
				'razorpay-official' => array(
					'src'      => plugin_dir_url( MASTERIYO_RAZORPAY_ADDON_FILE ) . 'assets/js/frontend/checkout' . $suffix . '.js',
					'context'  => 'public',
					'callback' => function() {
						return Setting::get( 'enable' ) && masteriyo_is_checkout_page();
					},
				),
			)
		);
	}

	/**
	 * Append setting to response.
	 *
	 * @since 2.7.1
	 *
	 * @param array $data Setting data.
	 * @param \Masteriyo\Models\Setting $setting Setting object.
	 * @param string $context What the value is for. Valid values are view and edit.
	 * @param \Masteriyo\RestApi\Controllers\Version1\SettingsController $controller REST settings controller object.
	 *
	 * @return array
	 */
	public function append_setting_in_response( $data, $setting, $context, $controller ) {
		$data['payments']['razorpay'] = Setting::all();

		return $data;
	}

	/**
	 * Save global Razorpay settings.
	 *
	 * @since 2.7.1
	 *
	 * @param \Masteriyo\Models\Setting $setting Setting object.
	 */
	public function save_razorpay_settings( $setting ) {
		$request = masteriyo_current_http_request();

		if ( ! masteriyo_is_rest_api_request() ) {
			return;
		}

		if ( ! isset( $request['payments']['razorpay'] ) ) {
			return;
		}

		$settings = masteriyo_array_only( $request['payments']['razorpay'], array_keys( Setting::all() ) );
		$settings = masteriyo_parse_args( $settings, Setting::all() );

		// Sanitization.
		$settings['enable']               = masteriyo_string_to_bool( $settings['enable'] );
		$settings['title']                = sanitize_text_field( $settings['title'] );
		$settings['sandbox']              = masteriyo_string_to_bool( $settings['sandbox'] );
		$settings['description']          = sanitize_textarea_field( $settings['description'] );
		$settings['test_publishable_key'] = sanitize_text_field( $settings['test_publishable_key'] );
		$settings['test_secret_key']      = sanitize_text_field( $settings['test_secret_key'] );
		$settings['live_publishable_key'] = sanitize_text_field( $settings['live_publishable_key'] );
		$settings['live_secret_key']      = sanitize_text_field( $settings['live_secret_key'] );

		Setting::set_props( $settings );

		Setting::save();
	}

	/**
	 * Add Razorpay payment gateway to available payment gateways.
	 *
	 * @since 2.7.1
	 *
	 * @param Masteriyo\Abstracts\PaymentGateway[]
	 *
	 * @return Masteriyo\Abstracts\PaymentGateway[]
	 */
	public function add_payment_gateway( $gateways ) {
		$gateways[] = Razorpay::class;

		return $gateways;
	}
}
