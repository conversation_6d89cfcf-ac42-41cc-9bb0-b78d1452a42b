<?php
/**
 * Masteriyo bundle list elementor widget class.
 *
 * @package Masteriyo\Addons\CourseBundle\Widgets
 *
 * @since 2.14.0
 */

namespace Masteriyo\Addons\CourseBundle\Widgets;

use Elementor\Controls_Manager;
use Elementor\Group_Control_Border;
use Elementor\Group_Control_Box_Shadow;
use Elementor\Group_Control_Typography;
use Elementor\Group_Control_Text_Shadow;
use Masteriyo\Addons\ElementorIntegration\WidgetBase;
use Masteriyo\Enums\PostStatus;
use Masteriyo\PostType\PostType;

defined( 'ABSPATH' ) || exit;

/**
 * Masteriyo bundle list elementor widget class.
 *
 * @package Masteriyo\Addons\ElementorIntegration\Widgets
 *
 * @since 2.14.0
 */
class BundleListWidget extends WidgetBase {
	/**
	 * Get widget name.
	 *
	 * @since 2.14.0
	 *
	 * @return string
	 */
	public function get_name() {
		return 'masteriyo-bundle-list';
	}

	/**
	 * Get widget title.
	 *
	 * @since 2.14.0
	 *
	 * @return string
	 */
	public function get_title() {
		return __( 'Course Bundle List', 'learning-management-system' );
	}

	/**
	 * Get icon class for the widget.
	 *
	 * @since 2.14.0
	 *
	 * @return string
	 */
	public function get_icon() {
		return 'masteriyo-bundle-list-widget-icon';
	}

	/**
	 * Register controls for configuring widget content.
	 *
	 * @since 2.14.0
	 */
	protected function register_content_controls() {
		$this->register_general_content_controls_section();
		$this->register_filter_controls_section();
		$this->register_sorting_controls_section();
	}

	/**
	 * Register general content controls section.
	 *
	 * @since 2.14.0
	 */
	protected function register_general_content_controls_section() {
		$this->start_controls_section(
			'general',
			array(
				'label' => __( 'General', 'learning-management-system' ),
				'tab'   => Controls_Manager::TAB_CONTENT,
			)
		);

		$this->add_control(
			'per_page',
			array(
				'label'   => __( 'No. of Bundles', 'learning-management-system' ),
				'type'    => Controls_Manager::NUMBER,
				'min'     => 1,
				'max'     => 100,
				'step'    => 1,
				'default' => 12,
			)
		);

		$this->add_control(
			'columns_per_row',
			array(
				'label'   => __( 'Columns', 'learning-management-system' ),
				'type'    => Controls_Manager::NUMBER,
				'min'     => 1,
				'max'     => 4,
				'step'    => 1,
				'default' => 3,
			)
		);

		$this->add_control(
			'divider_1',
			array(
				'type' => Controls_Manager::DIVIDER,
			)
		);

		$this->end_controls_section();
	}

	/**
	 * Register filter controls section.
	 *
	 * @since 2.14.0
	 */
	protected function register_filter_controls_section() {
		$instructors = $this->get_instructors_options();

		$this->start_controls_section(
			'filter',
			array(
				'label' => __( 'Filter', 'learning-management-system' ),
				'tab'   => Controls_Manager::TAB_CONTENT,
			)
		);

		$this->start_controls_tabs( 'filter_params' );

		// Include Tab
		$this->start_controls_tab(
			'parameter_inclusion_tab',
			array(
				'label' => __( 'Include', 'learning-management-system' ),
			)
		);

		$this->add_control(
			'include_instructors',
			array(
				'label'    => __( 'Authors', 'learning-management-system' ),
				'type'     => Controls_Manager::SELECT2,
				'multiple' => true,
				'options'  => $instructors,
				'default'  => array(),
			)
		);

		$this->end_controls_tab();

		// Exclude Tab
		$this->start_controls_tab(
			'parameter_exclusion_tab',
			array(
				'label' => __( 'Exclude', 'learning-management-system' ),
			)
		);

		$this->add_control(
			'exclude_instructors',
			array(
				'label'    => __( 'Authors', 'learning-management-system' ),
				'type'     => Controls_Manager::SELECT2,
				'multiple' => true,
				'options'  => $instructors,
				'default'  => array(),
			)
		);

		$this->end_controls_tab();
		$this->end_controls_tabs();
		$this->end_controls_section();
	}

	/**
	 * Register sorting controls section.
	 *
	 * @since 2.14.0
	 */
	protected function register_sorting_controls_section() {
		$this->start_controls_section(
			'sorting',
			array(
				'label' => __( 'Sorting', 'learning-management-system' ),
				'tab'   => Controls_Manager::TAB_CONTENT,
			)
		);

		$this->add_control(
			'order_by',
			array(
				'label'   => __( 'Order By', 'learning-management-system' ),
				'type'    => Controls_Manager::SELECT,
				'options' => array(
					'date'  => __( 'Date', 'learning-management-system' ),
					'title' => __( 'Title', 'learning-management-system' ),
					'price' => __( 'Price', 'learning-management-system' ),
				),
				'default' => 'date',
			)
		);

		$this->add_control(
			'sorting_order',
			array(
				'label'   => __( 'Order', 'learning-management-system' ),
				'type'    => Controls_Manager::SELECT,
				'options' => array(
					'ASC'  => __( 'Ascending', 'learning-management-system' ),
					'DESC' => __( 'Descending', 'learning-management-system' ),
				),
				'default' => 'DESC',
			)
		);

		$this->end_controls_section();
	}

	/**
	 * Register controls for customizing widget styles.
	 *
	 * @since 2.14.0
	 */
	protected function register_style_controls() {
		$this->register_layout_style_section();
		$this->register_card_styles_section();
		$this->register_title_styles_section();
		$this->register_footer_styles_section();
		$this->register_price_styles_section();
		$this->register_enroll_button_styles_section();
	}

	/**
	 * Register layout style controls section.
	 *
	 * @since 2.14.0
	 */
	protected function register_layout_style_section() {
		$this->start_controls_section(
			'layout',
			array(
				'label' => __( 'Layout', 'learning-management-system' ),
				'tab'   => Controls_Manager::TAB_STYLE,
			)
		);

		$this->add_responsive_control(
			'columns_gap',
			array(
				'label'     => __( 'Columns Gap (px)', 'learning-management-system' ),
				'type'      => Controls_Manager::NUMBER,
				'min'       => 0,
				'step'      => 1,
				'selectors' => array(
					'{{WRAPPER}} .masteriyo-col' => 'padding-left: calc( {{VALUE}}px / 2 ) !important; padding-right: calc( {{VALUE}}px / 2 ) !important;',
					'{{WRAPPER}} .masteriyo-courses-wrapper' => 'margin-left: calc( -{{VALUE}}px / 2 ) !important; margin-right: calc( -{{VALUE}}px / 2 ) !important;',
				),
			)
		);

		$this->add_responsive_control(
			'rows_gap',
			array(
				'label'     => __( 'Rows Gap (px)', 'learning-management-system' ),
				'type'      => Controls_Manager::NUMBER,
				'min'       => 0,
				'step'      => 1,
				'selectors' => array(
					'{{WRAPPER}} .masteriyo-col' => 'padding-top: calc( {{VALUE}}px / 2 ) !important; padding-bottom: calc( {{VALUE}}px / 2 ) !important;',
					'{{WRAPPER}} .masteriyo-courses-wrapper' => 'margin-top: calc( -{{VALUE}}px / 2 ) !important; margin-bottom: calc( -{{VALUE}}px / 2 ) !important;',
				),
			)
		);

		$this->end_controls_section();
	}

	/**
	 * Register card style controls section.
	 *
	 * @since 2.14.0
	 */
	protected function register_card_styles_section() {
		$this->start_controls_section(
			'card_style',
			array(
				'label' => __( 'Card', 'learning-management-system' ),
				'tab'   => Controls_Manager::TAB_STYLE,
			)
		);

		$this->start_controls_tabs( 'card_style_tabs' );

		// Normal state styles.
		$this->start_controls_tab(
			'card_normal_state_style_tab',
			array(
				'label' => __( 'Normal', 'learning-management-system' ),
			)
		);

		$this->add_control(
			'card_background_color',
			array(
				'label'     => __( 'Background Color', 'learning-management-system' ),
				'type'      => Controls_Manager::COLOR,
				'selectors' => array(
					'{{WRAPPER}} .masteriyo-bundle' => 'background-color: {{VALUE}};',
				),
			)
		);

		$this->add_control(
			'card_border_styles_popover_toggle',
			array(
				'type'         => Controls_Manager::POPOVER_TOGGLE,
				'label'        => esc_html__( 'Border', 'learning-management-system' ),
				'label_off'    => esc_html__( 'Default', 'learning-management-system' ),
				'label_on'     => esc_html__( 'Custom', 'learning-management-system' ),
				'return_value' => 'yes',
			)
		);

		$this->start_popover();

		$this->add_group_control(
			Group_Control_Border::get_type(),
			array(
				'name'     => 'card_border_styles',
				'label'    => __( 'Border', 'learning-management-system' ),
				'selector' => '{{WRAPPER}} .masteriyo-bundle',
			)
		);

		$this->add_control(
			'card_border_radius',
			array(
				'label'      => __( 'Border Radius', 'learning-management-system' ),
				'type'       => Controls_Manager::DIMENSIONS,
				'size_units' => array( 'px', '%' ),
				'selectors'  => array(
					'{{WRAPPER}} .masteriyo-bundle' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
				),
			)
		);

		$this->end_popover();

		$this->add_group_control(
			Group_Control_Box_Shadow::get_type(),
			array(
				'name'     => 'card_box_shadow',
				'label'    => __( 'Box Shadow', 'learning-management-system' ),
				'selector' => '{{WRAPPER}} .masteriyo-bundle',
			)
		);

		$this->end_controls_tab();

		// Hover state styles.
		$this->start_controls_tab(
			'card_hover_state_style_tab',
			array(
				'label' => __( 'Hover', 'learning-management-system' ),
			)
		);

		$this->add_control(
			'card_hover_background_color',
			array(
				'label'     => __( 'Background Color', 'learning-management-system' ),
				'type'      => Controls_Manager::COLOR,
				'selectors' => array(
					'{{WRAPPER}} .masteriyo-bundle:hover' => 'background-color: {{VALUE}};',
				),
			)
		);

		$this->add_control(
			'card_hover_border_styles_popover_toggle',
			array(
				'type'         => Controls_Manager::POPOVER_TOGGLE,
				'label'        => esc_html__( 'Border', 'learning-management-system' ),
				'label_off'    => esc_html__( 'Default', 'learning-management-system' ),
				'label_on'     => esc_html__( 'Custom', 'learning-management-system' ),
				'return_value' => 'yes',
			)
		);

		$this->start_popover();

		$this->add_group_control(
			Group_Control_Border::get_type(),
			array(
				'name'     => 'card_hover_border_styles',
				'label'    => __( 'Border', 'learning-management-system' ),
				'selector' => '{{WRAPPER}} .masteriyo-bundle:hover',
			)
		);

		$this->add_control(
			'card_hover_border_radius',
			array(
				'label'      => __( 'Border Radius', 'learning-management-system' ),
				'type'       => Controls_Manager::DIMENSIONS,
				'size_units' => array( 'px', '%' ),
				'selectors'  => array(
					'{{WRAPPER}} .masteriyo-bundle:hover' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
				),
			)
		);

		$this->end_popover();

		$this->add_group_control(
			Group_Control_Box_Shadow::get_type(),
			array(
				'name'     => 'card_hover_box_shadow',
				'label'    => __( 'Box Shadow', 'learning-management-system' ),
				'selector' => '{{WRAPPER}} .masteriyo-bundle:hover',
			)
		);

		$this->add_control(
			'card_hover_animation',
			array(
				'label' => __( 'Hover Animation', 'learning-management-system' ),
				'type'  => Controls_Manager::HOVER_ANIMATION,
			)
		);

		$this->end_controls_tab();
		$this->end_controls_tabs();
		$this->end_controls_section();
	}

	/**
	 * Register course title style controls section.
	 *
	 * @since 2.14.0
	 */
	protected function register_title_styles_section() {
		$this->start_controls_section(
			'course_title_section',
			array(
				'label' => __( 'Title', 'learning-management-system' ),
				'tab'   => Controls_Manager::TAB_STYLE,
			)
		);

		$this->add_responsive_control(
			'course_title_text_align',
			array(
				'label'     => esc_html__( 'Alignment', 'learning-management-system' ),
				'type'      => Controls_Manager::CHOOSE,
				'options'   => array(
					'left'   => array(
						'title' => esc_html__( 'Left', 'learning-management-system' ),
						'icon'  => 'eicon-text-align-left',
					),
					'center' => array(
						'title' => esc_html__( 'Center', 'learning-management-system' ),
						'icon'  => 'eicon-text-align-center',
					),
					'right'  => array(
						'title' => esc_html__( 'Right', 'learning-management-system' ),
						'icon'  => 'eicon-text-align-right',
					),
				),
				'toggle'    => true,
				'selectors' => array(
					'{{WRAPPER}} .masteriyo-bundle__title' => 'text-align: {{VALUE}};',
				),
			)
		);

		$this->add_group_control(
			Group_Control_Typography::get_type(),
			array(
				'name'     => 'course_title_typography',
				'selector' => '{{WRAPPER}} .masteriyo-bundle__title',
			)
		);

		$this->add_control(
			'course_title_text_color',
			array(
				'label'     => __( 'Text Color', 'learning-management-system' ),
				'type'      => Controls_Manager::COLOR,
				'selectors' => array(
					'{{WRAPPER}} .masteriyo-bundle__title a' => 'color: {{VALUE}} !important;',
				),
			)
		);

		$this->add_control(
			'course_title_background_color',
			array(
				'label'     => __( 'Background Color', 'learning-management-system' ),
				'type'      => Controls_Manager::COLOR,
				'selectors' => array(
					'{{WRAPPER}} .masteriyo-bundle__title' => 'background-color: {{VALUE}} !important;',
				),
			)
		);

		$this->add_control(
			'popover-toggle_course_title_border',
			array(
				'type'         => Controls_Manager::POPOVER_TOGGLE,
				'label'        => esc_html__( 'Border', 'learning-management-system' ),
				'label_off'    => esc_html__( 'Default', 'learning-management-system' ),
				'label_on'     => esc_html__( 'Custom', 'learning-management-system' ),
				'return_value' => 'yes',
			)
		);

		$this->start_popover();

		$this->add_group_control(
			Group_Control_Border::get_type(),
			array(
				'name'     => 'course_title_border',
				'label'    => __( 'Border', 'learning-management-system' ),
				'selector' => '{{WRAPPER}} .masteriyo-bundle__title',
			)
		);

		$this->add_control(
			'course_title_border_radius',
			array(
				'label'      => __( 'Border Radius', 'learning-management-system' ),
				'type'       => Controls_Manager::DIMENSIONS,
				'size_units' => array( 'px', '%' ),
				'selectors'  => array(
					'{{WRAPPER}} .masteriyo-bundle__title' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
				),
			)
		);

		$this->end_popover();

		$this->add_control(
			'course_title_padding',
			array(
				'label'      => __( 'Padding', 'learning-management-system' ),
				'type'       => Controls_Manager::DIMENSIONS,
				'size_units' => array( 'px', '%' ),
				'selectors'  => array(
					'{{WRAPPER}} .masteriyo-bundle__title' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
				),
			)
		);

		$this->add_control(
			'course_title_margin',
			array(
				'label'      => __( 'Margin', 'learning-management-system' ),
				'type'       => Controls_Manager::DIMENSIONS,
				'size_units' => array( 'px', '%' ),
				'selectors'  => array(
					'{{WRAPPER}} .masteriyo-bundle__title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
				),
			)
		);
		$this->end_controls_section();
	}

	/**
	 * Register price style controls section.
	 *
	 * @since 2.14.0
	 */
	protected function register_price_styles_section() {
		$this->start_controls_section(
			'price_styles',
			array(
				'label' => __( 'Price', 'learning-management-system' ),
				'tab'   => Controls_Manager::TAB_STYLE,
			)
		);

		$this->add_group_control(
			Group_Control_Typography::get_type(),
			array(
				'name'     => 'price_typography',
				'selector' => '{{WRAPPER}} .masteriyo-bundle__price span',
			)
		);

		$this->add_control(
			'price_text_color',
			array(
				'label'     => __( 'Text Color', 'learning-management-system' ),
				'type'      => Controls_Manager::COLOR,
				'selectors' => array(
					'{{WRAPPER}} .masteriyo-bundle__price span' => 'color: {{VALUE}} !important;',
				),
			)
		);

		$this->add_control(
			'price_background_color',
			array(
				'label'     => __( 'Background Color', 'learning-management-system' ),
				'type'      => Controls_Manager::COLOR,
				'selectors' => array(
					'{{WRAPPER}} .masteriyo-bundle__price' => 'background-color: {{VALUE}} !important;',
				),
			)
		);

		$this->add_control(
			'price_border_styles_popover',
			array(
				'type'         => Controls_Manager::POPOVER_TOGGLE,
				'label'        => esc_html__( 'Border', 'learning-management-system' ),
				'label_off'    => esc_html__( 'Default', 'learning-management-system' ),
				'label_on'     => esc_html__( 'Custom', 'learning-management-system' ),
				'return_value' => 'yes',
			)
		);

		$this->start_popover();

		$this->add_group_control(
			Group_Control_Border::get_type(),
			array(
				'name'     => 'price_border_styles',
				'label'    => __( 'Border', 'learning-management-system' ),
				'selector' => '{{WRAPPER}} .masteriyo-bundle__price',
			)
		);

		$this->add_control(
			'price_border_radius',
			array(
				'label'      => __( 'Border Radius', 'learning-management-system' ),
				'type'       => Controls_Manager::DIMENSIONS,
				'size_units' => array( 'px', '%' ),
				'selectors'  => array(
					'{{WRAPPER}} .masteriyo-bundle__price' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
				),
			)
		);

		$this->end_popover();

		$this->add_control(
			'price_padding',
			array(
				'label'      => __( 'Padding', 'learning-management-system' ),
				'type'       => Controls_Manager::DIMENSIONS,
				'size_units' => array( 'px', '%' ),
				'selectors'  => array(
					'{{WRAPPER}} .masteriyo-bundle__price' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
				),
			)
		);

		$this->add_control(
			'price_margin',
			array(
				'label'      => __( 'Margin', 'learning-management-system' ),
				'type'       => Controls_Manager::DIMENSIONS,
				'size_units' => array( 'px', '%' ),
				'selectors'  => array(
					'{{WRAPPER}} .masteriyo-bundle__price' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
				),
			)
		);

		$this->end_controls_section();
	}

	/**
	 * Register enroll button style controls section.
	 *
	 * @since 2.14.0
	 */
	protected function register_enroll_button_styles_section() {
		$this->start_controls_section(
			'enroll_button_styles',
			array(
				'label' => __( 'Enroll Button', 'learning-management-system' ),
				'tab'   => Controls_Manager::TAB_STYLE,
			)
		);

		$this->add_group_control(
			Group_Control_Typography::get_type(),
			array(
				'name'     => 'enroll_button_typography',
				'selector' => '{{WRAPPER}} .masteriyo-enroll-btn',
			)
		);

		$this->add_control(
			'enroll_button_padding',
			array(
				'label'      => __( 'Padding', 'learning-management-system' ),
				'type'       => Controls_Manager::DIMENSIONS,
				'size_units' => array( 'px', '%' ),
				'selectors'  => array(
					'{{WRAPPER}} .masteriyo-enroll-btn' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
				),
			)
		);

		$this->add_control(
			'enroll_button_margin',
			array(
				'label'      => __( 'Margin', 'learning-management-system' ),
				'type'       => Controls_Manager::DIMENSIONS,
				'size_units' => array( 'px', '%' ),
				'selectors'  => array(
					'{{WRAPPER}} .masteriyo-enroll-btn' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
				),
			)
		);

		$this->add_control(
			'enroll_button_styles_tabs_divider',
			array(
				'type' => Controls_Manager::DIVIDER,
			)
		);

		$this->start_controls_tabs( 'enroll_button_states' );

		// Normal state styles.
		$this->start_controls_tab(
			'enroll_button_normal_state_style_tab',
			array(
				'label' => __( 'Normal', 'learning-management-system' ),
			)
		);

		$this->add_control(
			'enroll_button_text_color',
			array(
				'label'     => __( 'Text Color', 'learning-management-system' ),
				'type'      => Controls_Manager::COLOR,
				'selectors' => array(
					'{{WRAPPER}} .masteriyo-enroll-btn' => 'color: {{VALUE}};',
				),
			)
		);

		$this->add_control(
			'enroll_button_background_color',
			array(
				'label'     => __( 'Background Color', 'learning-management-system' ),
				'type'      => Controls_Manager::COLOR,
				'selectors' => array(
					'{{WRAPPER}} .masteriyo-enroll-btn' => 'background-color: {{VALUE}};',
				),
			)
		);

		$this->add_control(
			'enroll_button_border_styles_popover',
			array(
				'type'         => Controls_Manager::POPOVER_TOGGLE,
				'label'        => esc_html__( 'Border', 'learning-management-system' ),
				'label_off'    => esc_html__( 'Default', 'learning-management-system' ),
				'label_on'     => esc_html__( 'Custom', 'learning-management-system' ),
				'return_value' => 'yes',
			)
		);

		$this->start_popover();

		$this->add_group_control(
			Group_Control_Border::get_type(),
			array(
				'name'     => 'enroll_button_border_styles',
				'label'    => __( 'Border', 'learning-management-system' ),
				'selector' => '{{WRAPPER}} .masteriyo-enroll-btn',
			)
		);

		$this->add_control(
			'enroll_button_border_radius',
			array(
				'label'      => __( 'Border Radius', 'learning-management-system' ),
				'type'       => Controls_Manager::DIMENSIONS,
				'size_units' => array( 'px', '%' ),
				'selectors'  => array(
					'{{WRAPPER}} .masteriyo-enroll-btn' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
				),
			)
		);

		$this->end_popover();

		$this->add_group_control(
			Group_Control_Text_Shadow::get_type(),
			array(
				'name'     => 'enroll_button_text_shadow',
				'label'    => __( 'Text Shadow', 'learning-management-system' ),
				'selector' => '{{WRAPPER}} .masteriyo-enroll-btn',
			)
		);

		$this->add_group_control(
			Group_Control_Box_Shadow::get_type(),
			array(
				'name'     => 'enroll_button_box_shadow',
				'label'    => __( 'Box Shadow', 'learning-management-system' ),
				'selector' => '{{WRAPPER}} .masteriyo-enroll-btn',
			)
		);

		$this->end_controls_tab();

		// Hover state styles.
		$this->start_controls_tab(
			'enroll_button_hover_state_style_tab',
			array(
				'label' => __( 'Hover', 'learning-management-system' ),
			)
		);

		$this->add_control(
			'enroll_button_hover_text_color',
			array(
				'label'     => __( 'Text Color', 'learning-management-system' ),
				'type'      => Controls_Manager::COLOR,
				'selectors' => array(
					'{{WRAPPER}} .masteriyo-enroll-btn:hover' => 'color: {{VALUE}};',
				),
			)
		);

		$this->add_control(
			'enroll_button_hover_background_color',
			array(
				'label'     => __( 'Background Color', 'learning-management-system' ),
				'type'      => Controls_Manager::COLOR,
				'selectors' => array(
					'{{WRAPPER}} .masteriyo-enroll-btn:hover' => 'background-color: {{VALUE}};',
				),
			)
		);

		$this->add_control(
			'enroll_button_hover_border_styles_popover',
			array(
				'type'         => Controls_Manager::POPOVER_TOGGLE,
				'label'        => esc_html__( 'Border', 'learning-management-system' ),
				'label_off'    => esc_html__( 'Default', 'learning-management-system' ),
				'label_on'     => esc_html__( 'Custom', 'learning-management-system' ),
				'return_value' => 'yes',
			)
		);

		$this->start_popover();

		$this->add_group_control(
			Group_Control_Border::get_type(),
			array(
				'name'     => 'enroll_button_hover_border_styles',
				'label'    => __( 'Border', 'learning-management-system' ),
				'selector' => '{{WRAPPER}} .masteriyo-enroll-btn:hover',
			)
		);

		$this->add_control(
			'enroll_button_hover_border_radius',
			array(
				'label'      => __( 'Border Radius', 'learning-management-system' ),
				'type'       => Controls_Manager::DIMENSIONS,
				'size_units' => array( 'px', '%' ),
				'selectors'  => array(
					'{{WRAPPER}} .masteriyo-enroll-btn:hover' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
				),
			)
		);

		$this->end_popover();

		$this->add_group_control(
			Group_Control_Text_Shadow::get_type(),
			array(
				'name'     => 'enroll_button_hover_text_shadow',
				'label'    => __( 'Text Shadow', 'learning-management-system' ),
				'selector' => '{{WRAPPER}} .masteriyo-enroll-btn:hover',
			)
		);

		$this->add_group_control(
			Group_Control_Box_Shadow::get_type(),
			array(
				'name'     => 'enroll_button_hover_box_shadow',
				'label'    => __( 'Box Shadow', 'learning-management-system' ),
				'selector' => '{{WRAPPER}} .masteriyo-enroll-btn:hover',
			)
		);

		$this->end_controls_tab();

		$this->end_controls_tabs();

		$this->end_controls_section();
	}

	/**
	 * Register footer style controls section.
	 *
	 * @since 2.14.0
	 */
	protected function register_footer_styles_section() {
		$this->start_controls_section(
			'footer_styles',
			array(
				'label' => __( 'Footer', 'learning-management-system' ),
				'tab'   => Controls_Manager::TAB_STYLE,
			)
		);

		$this->add_control(
			'footer_background_color',
			array(
				'label'     => __( 'Background Color', 'learning-management-system' ),
				'type'      => Controls_Manager::COLOR,
				'selectors' => array(
					'{{WRAPPER}} .masteriyo-bundle__cta' => 'background-color: {{VALUE}};',
				),
			)
		);

		$this->add_control(
			'footer_border_styles_popover_toggle',
			array(
				'type'         => Controls_Manager::POPOVER_TOGGLE,
				'label'        => esc_html__( 'Border', 'learning-management-system' ),
				'label_off'    => esc_html__( 'Default', 'learning-management-system' ),
				'label_on'     => esc_html__( 'Custom', 'learning-management-system' ),
				'return_value' => 'yes',
			)
		);

		$this->start_popover();

		$this->add_group_control(
			Group_Control_Border::get_type(),
			array(
				'name'     => 'footer_border_styles',
				'label'    => __( 'Border', 'learning-management-system' ),
				'selector' => '{{WRAPPER}} .masteriyo-bundle__cta',
			)
		);

		$this->add_control(
			'footer_border_radius',
			array(
				'label'      => __( 'Border Radius', 'learning-management-system' ),
				'type'       => Controls_Manager::DIMENSIONS,
				'size_units' => array( 'px', '%' ),
				'selectors'  => array(
					'{{WRAPPER}} .masteriyo-bundle__cta' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
				),
			)
		);

		$this->end_popover();

		$this->add_responsive_control(
			'footer_padding',
			array(
				'label'      => __( 'Padding', 'learning-management-system' ),
				'type'       => Controls_Manager::DIMENSIONS,
				'size_units' => array( 'px', 'em' ),
				'selectors'  => array(
					'{{WRAPPER}} .masteriyo-bundle__cta' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
				),
			)
		);

		$this->add_responsive_control(
			'footer_margin',
			array(
				'label'      => __( 'Margin', 'learning-management-system' ),
				'type'       => Controls_Manager::DIMENSIONS,
				'size_units' => array( 'px', 'em' ),
				'selectors'  => array(
					'{{WRAPPER}} .masteriyo-bundle__cta' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
				),
			)
		);

		$this->end_controls_section();
	}

	/**
	 * Render HTML for frontend.
	 *
	 * @since 2.6.7
	 */
	protected function render() {
		$settings = $this->get_settings();

		$limit     = max( absint( $settings['per_page'] ), 1 );
		$columns   = max( absint( $settings['columns_per_row'] ), 1 );
		$paged     = ( get_query_var( 'paged' ) ) ? get_query_var( 'paged' ) : 1;
		$tax_query = array(
			'relation' => 'AND',
		);

		$args = array(
			'post_type'      => PostType::COURSE_BUNDLE,
			'status'         => array( PostStatus::PUBLISH ),
			'posts_per_page' => $limit,
			'paged'          => $paged,
			'order'          => 'DESC',
			'orderby'        => 'date',
			'tax_query'      => $tax_query,
		);

		if ( ! empty( $settings['include_instructors'] ) ) {
			$args['author__in'] = $settings['include_instructors'];
		}

		if ( ! empty( $settings['exclude_instructors'] ) ) {
			$args['author__not_in'] = $settings['exclude_instructors'];
		}

		$order = strtoupper( $settings['sorting_order'] );

		switch ( $settings['order_by'] ) {
			case 'date':
				$args['orderby'] = 'date';
				$args['order']   = ( 'ASC' === $order ) ? 'ASC' : 'DESC';
				break;

			case 'price':
				$args['orderby']  = 'meta_value_num';
				$args['meta_key'] = '_price';
				$args['order']    = ( 'DESC' === $order ) ? 'DESC' : 'ASC';
				break;

			case 'title':
				$args['orderby'] = 'title';
				$args['order']   = ( 'DESC' === $order ) ? 'DESC' : 'ASC';
				break;

			case 'rating':
				$args['orderby']  = 'meta_value_num';
				$args['meta_key'] = '_average_rating';
				$args['order']    = ( 'ASC' === $order ) ? 'ASC' : 'DESC';
				break;

			default:
				$args['orderby'] = 'date';
				$args['order']   = ( 'ASC' === $order ) ? 'ASC' : 'DESC';
				break;
		}

		$bundles_query                      = new \WP_Query( $args );
		$GLOBALS['mto_course_bundle_query'] = $bundles_query;
		$bundles                            = array_filter( array_map( 'masteriyo_get_course_bundle', $bundles_query->posts ) );

		printf( '<div class="masteriyo-bundle-list-display-section">' );
		masteriyo_set_loop_prop( 'columns', $columns );

		if ( count( $bundles ) > 0 ) {
			$original_bundle = isset( $GLOBALS['course_bundle'] ) ? $GLOBALS['course_bundle'] : null;

			masteriyo_course_bundle_loop_start();

			foreach ( $bundles as $bundle ) {
				$GLOBALS['course_bundle'] = $bundle;
				$card_class               = empty( $settings['card_hover_animation'] ) ? '' : sprintf( 'elementor-animation-%s', $settings['card_hover_animation'] );

				masteriyo_get_template(
					'course-bundle/content-course-bundle.php',
					array(
						'card_class' => $card_class,
					)
				);
			}

			$GLOBALS['course_bundle'] = $original_bundle;

			masteriyo_course_bundle_loop_end();
			masteriyo_reset_loop();
		}
		echo '</div>';
	}
}
