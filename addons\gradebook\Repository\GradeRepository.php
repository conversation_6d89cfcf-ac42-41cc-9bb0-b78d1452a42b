<?php
/**
 * GradeRepository class.
 *
 * @since 2.5.20
 *
 * @package Masteriyo\Addons\Gradebook\Repository
 */

namespace Masteriyo\Addons\Gradebook\Repository;

defined( 'ABSPATH' ) || exit;


use Masteriyo\Models\Grade;
use Masteriyo\Database\Model;
use Masteriyo\Enums\PostStatus;
use Masteriyo\PostType\PostType;
use Masteriyo\Repository\AbstractRepository;
use Masteriyo\Repository\RepositoryInterface;
use \Colors\RandomColor;

/**
 * GradeRepository class.
 */
class GradeRepository extends AbstractRepository implements RepositoryInterface {

	/**
	 * Data stored in meta keys, but not considered "meta".
	 *
	 * @since 2.5.20
	 * @var array
	 */
	protected $internal_meta_keys = array(
		'min'    => '_min',
		'max'    => '_max',
		'points' => '_points',
	);

	/**
	 * Create a grade in the database.
	 *
	 * @since 2.5.20
	 *
	 * @param \Masteriyo\Addons\Gradebook\Models\Grade $grade Grade object.
	 */
	public function create( Model &$grade ) {
		if ( ! $grade->get_created_at( 'edit' ) ) {
			$grade->set_created_at( time() );
		}

		// Set the author of the grade to the current user id, if the grade doesn't have a author.
		if ( empty( $grade->get_author_id() ) ) {
			$grade->set_author_id( get_current_user_id() );
		}

		if ( empty( $grade->get_color( 'edit' ) ) ) {
			$grade->set_color( RandomColor::one() );
		}

		$id = wp_insert_post(
			/**
			 * Filters new grade data before creating.
			 *
			 * @since 2.5.20
			 *
			 * @param array $data New grade data.
			 * @param Masteriyo\Models\Grade $grade Grade object.
			 */
			apply_filters(
				'masteriyo_new_grade_data',
				array(
					'post_type'      => PostType::GRADE,
					'post_status'    => PostStatus::PUBLISH,
					'post_author'    => $grade->get_author_id( 'edit' ),
					'post_title'     => $grade->get_name(),
					'post_content'   => $grade->get_color( 'edit' ),
					'comment_status' => 'closed',
					'ping_status'    => 'closed',
					'menu_order'     => $grade->get_menu_order(),
					'post_date'      => gmdate( 'Y-m-d H:i:s', $grade->get_created_at( 'edit' )->getOffsetTimestamp() ),
					'post_date_gmt'  => gmdate( 'Y-m-d H:i:s', $grade->get_created_at( 'edit' )->getTimestamp() ),
				),
				$grade
			)
		);

		if ( $id && ! is_wp_error( $id ) ) {
			$grade->set_id( $id );
			$this->update_post_meta( $grade, true );
			// TODO Invalidate caches.

			$grade->save_meta_data();
			$grade->apply_changes();

			/**
			 * Fires after creating a grade.
			 *
			 * @since 2.5.20
			 *
			 * @param integer $id The grade ID.
			 * @param \Masteriyo\Addons\Gradebook\Models\Grade $object The grade object.
			 */
			do_action( 'masteriyo_new_grade', $id, $grade );
		}

	}

	/**
	 * Read a grade.
	 *
	 * @since 2.5.20
	 *
	 * @param \Masteriyo\Addons\Gradebook\Models\Grade $grade Grade object.
	 * @throws \Exception If invalid grade.
	 */
	public function read( Model &$grade ) {
		$grade_post = get_post( $grade->get_id() );

		if ( ! $grade->get_id() || ! $grade_post || PostType::GRADE !== $grade_post->post_type ) {
			throw new \Exception( __( 'Invalid grade.', 'learning-management-system' ) );
		}

		$grade->set_props(
			array(
				'name'        => $grade_post->post_title,
				'created_at'  => $this->string_to_timestamp( $grade_post->post_date_gmt ),
				'modified_at' => $this->string_to_timestamp( $grade_post->post_modified_gmt ),
				'author_id'   => $grade_post->post_author,
				'menu_order'  => $grade_post->menu_order,
				'color'       => $grade_post->post_content,
			)
		);

		$this->read_grade_data( $grade );
		$this->read_extra_data( $grade );
		$grade->set_object_read( true );

		/**
		 * Fires after reading a grade from database.
		 *
		 * @since 2.5.20
		 *
		 * @param integer $id The grade ID.
		 * @param \Masteriyo\Addons\Gradebook\Models\Grade $object The grade object.
		 */
		do_action( 'masteriyo_grade_read', $grade->get_id(), $grade );
	}

	/**
	 * Update a grade in the database.
	 *
	 * @since 2.5.20
	 *
	 * @param \Masteriyo\Addons\Gradebook\Models\Grade $grade Grade object.
	 *
	 * @return void
	 */
	public function update( Model &$grade ) {
		$changes = $grade->get_changes();

		$post_data_keys = array(
			'name',
			'author_id',
			'menu_order',
			'created_at',
			'modified_at',
			'color',
		);

		// Only update the post when the post data changes.
		if ( array_intersect( $post_data_keys, array_keys( $changes ) ) ) {
			$post_data = array(
				'post_title'     => $grade->get_name( 'edit' ),
				'post_author'    => $grade->get_author_id( 'edit' ),
				'comment_status' => 'closed',
				'post_status'    => PostStatus::PUBLISH,
				'menu_order'     => $grade->get_menu_order( 'edit' ),
				'post_content'   => $grade->get_color( 'edit' ),
				'post_type'      => PostType::GRADE,
			);

			/**
			 * When updating this object, to prevent infinite loops, use $wpdb
			 * to update data, since wp_update_post spawns more calls to the
			 * save_post action.
			 *
			 * This ensures hooks are fired by either WP itself (admin screen save),
			 * or an update purely from CRUD.
			 */
			if ( doing_action( 'save_post' ) ) {
				// TODO Abstract the $wpdb WordPress class.
				$GLOBALS['wpdb']->update( $GLOBALS['wpdb']->posts, $post_data, array( 'ID' => $grade->get_id() ) );
				clean_post_cache( $grade->get_id() );
			} else {
				wp_update_post( array_merge( array( 'ID' => $grade->get_id() ), $post_data ) );
			}
			$grade->read_meta_data( true ); // Refresh internal meta data, in case things were hooked into `save_post` or another WP hook.
		} else { // Only update post modified time to record this save event.
			$GLOBALS['wpdb']->update(
				$GLOBALS['wpdb']->posts,
				array(
					'post_modified'     => current_time( 'mysql' ),
					'post_modified_gmt' => current_time( 'mysql', true ),
				),
				array(
					'ID' => $grade->get_id(),
				)
			);
			clean_post_cache( $grade->get_id() );
		}

		$this->update_post_meta( $grade );

		$grade->apply_changes();

		/**
		 * Fires after updating a grade.
		 *
		 * @since 2.5.20
		 *
		 * @param integer $id The grade ID.
		 * @param \Masteriyo\Addons\Gradebook\Models\Grade $object The grade object.
		 */
		do_action( 'masteriyo_update_grade', $grade->get_id(), $grade );
	}

	/**
	 * Delete a grade from the database.
	 *
	 * @since 2.5.20
	 *
	 * @param \Masteriyo\Addons\Gradebook\Models\Grade $grade Grade object.
	 * @param array $args   Array of args to pass.alert-danger.
	 */
	public function delete( Model &$grade, $args = array() ) {
		$id          = $grade->get_id();
		$object_type = $grade->get_object_type();

		$args = array_merge(
			array(
				'force_delete' => false,
			),
			$args
		);

		if ( ! $id ) {
			return;
		}

		if ( $args['force_delete'] ) {
			/**
			 * Fires before deleting a grade.
			 *
			 * @since 1.4.1
			 *
			 * @param integer $id The grade ID.
			 * @param \Masteriyo\Addons\Gradebook\Models\Grade $object The grade object.
			 */
			do_action( 'masteriyo_before_delete_' . $object_type, $id, $grade );

			wp_delete_post( $id, true );
			$grade->set_id( 0 );

			/**
			 * Fires after deleting a grade.
			 *
			 * @since 1.4.1
			 *
			 * @param integer $id The grade ID.
			 * @param \Masteriyo\Addons\Gradebook\Models\Grade $object The grade object.
			 */
			do_action( 'masteriyo_after_delete_' . $object_type, $id, $grade );
		} else {
			/**
			 * Fires before moving a grade to trash.
			 *
			 * @since 1.4.1
			 *
			 * @param integer $id The grade ID.
			 * @param \Masteriyo\Addons\Gradebook\Models\Grade $object The grade object.
			 */
			do_action( 'masteriyo_before_trash_' . $object_type, $id, $grade );

			wp_trash_post( $id );
			$grade->set_status( 'trash' );

			/**
			 * Fires after moving a grade to trash.
			 *
			 * @since 1.5.2
			 *
			 * @param integer $id The grade ID.
			 * @param \Masteriyo\Addons\Gradebook\Models\Grade $object The grade object.
			 */
			do_action( 'masteriyo_after_trash_' . $object_type, $id, $grade );
		}
	}

	/**
	 * Read grade data. Can be overridden by child classes to load other props.
	 *
	 * @since 2.5.20
	 *
	 * @param \Masteriyo\Addons\Gradebook\Models\Grade $grade Grade object.
	 */
	protected function read_grade_data( &$grade ) {
		$id          = $grade->get_id();
		$meta_values = $this->read_meta( $grade );

		$set_props = array();

		$meta_values = array_reduce(
			$meta_values,
			function( $result, $meta_value ) {
				$result[ $meta_value->key ][] = $meta_value->value;
				return $result;
			},
			array()
		);

		foreach ( $this->internal_meta_keys as $prop => $meta_key ) {
			$meta_value         = isset( $meta_values[ $meta_key ][0] ) ? $meta_values[ $meta_key ][0] : null;
			$set_props[ $prop ] = maybe_unserialize( $meta_value ); // get_post_meta only unserializes single values.
		}

		$grade->set_props( $set_props );
	}

	/**
	 * Read extra data associated with the grade, like button text or grade URL for external grades.
	 *
	 * @since 2.5.20
	 *
	 * @param \Masteriyo\Addons\Gradebook\Models\Grade $grade Grade object.
	 */
	protected function read_extra_data( &$grade ) {
		$meta_values = $this->read_meta( $grade );

		foreach ( $grade->get_extra_data_keys() as $key ) {
			$function = 'set_' . $key;

			if ( is_callable( array( $grade, $function ) )
				&& isset( $meta_values[ '_' . $key ] ) ) {
				$grade->{$function}( $meta_values[ '_' . $key ] );
			}
		}
	}

	/**
	 * Fetch grades.
	 *
	 * @since 2.5.20
	 *
	 * @param array $query_vars Query vars.
	 * @return \Masteriyo\Addons\Gradebook\Models\Grade[]
	 */
	public function query( $query_vars ) {
		$args = $this->get_wp_query_args( $query_vars );

		if ( ! empty( $args['errors'] ) ) {
			$query = (object) array(
				'posts'         => array(),
				'found_posts'   => 0,
				'max_num_pages' => 0,
			);
		} else {
			$query = new \WP_Query( $args );
		}

		if ( isset( $query_vars['return'] ) && 'objects' === $query_vars['return'] && ! empty( $query->posts ) ) {
			// Prime caches before grabbing objects.
			update_post_caches( $query->posts, array( PostType::GRADE ) );
		}

		$grades = ( isset( $query_vars['return'] ) && 'ids' === $query_vars['return'] ) ? $query->posts : array_filter( array_map( 'masteriyo_get_grade', $query->posts ) );

		if ( isset( $query_vars['paginate'] ) && $query_vars['paginate'] ) {
			return (object) array(
				'grades'        => $grades,
				'total'         => $query->found_posts,
				'max_num_pages' => $query->max_num_pages,
			);
		}

		return $grades;
	}

	/**
	 * Get valid WP_Query args from a GradeQuery's query variables.
	 *
	 * @since 2.5.20
	 * @param array $query_vars Query vars from a GradeQuery.
	 * @return array
	 */
	protected function get_wp_query_args( $query_vars ) {
		// Map query vars to ones that get_wp_query_args or WP_Query recognize.
		$key_mapping = array(
			'status'    => 'post_status',
			'page'      => 'paged',
			'parent_id' => 'post_parent',
		);

		foreach ( $key_mapping as $query_key => $db_key ) {
			if ( isset( $query_vars[ $query_key ] ) ) {
				$query_vars[ $db_key ] = $query_vars[ $query_key ];
				unset( $query_vars[ $query_key ] );
			}
		}

		$query_vars['post_type'] = PostType::GRADE;

		$wp_query_args = parent::get_wp_query_args( $query_vars );

		if ( ! isset( $wp_query_args['date_query'] ) ) {
			$wp_query_args['date_query'] = array();
		}
		if ( ! isset( $wp_query_args['meta_query'] ) ) {
			$wp_query_args['meta_query'] = array(); // phpcs:ignore WordPress.DB.SlowDBQuery.slow_db_query_meta_query
		}

		// Handle date queries.
		$date_queries = array(
			'created_at'  => 'post_date',
			'modified_at' => 'post_modified',
		);
		foreach ( $date_queries as $query_var_key => $db_key ) {
			if ( isset( $query_vars[ $query_var_key ] ) && '' !== $query_vars[ $query_var_key ] ) {

				// Remove any existing meta queries for the same keys to prevent conflicts.
				$existing_queries = wp_list_pluck( $wp_query_args['meta_query'], 'key', true );
				foreach ( $existing_queries as $query_index => $query_contents ) {
					unset( $wp_query_args['meta_query'][ $query_index ] );
				}

				$wp_query_args = $this->parse_date_for_wp_query( $query_vars[ $query_var_key ], $db_key, $wp_query_args );
			}
		}

		// Handle paginate.
		if ( ! isset( $query_vars['paginate'] ) || ! $query_vars['paginate'] ) {
			$wp_query_args['no_found_rows'] = true;
		}

		// Handle orderby.
		if ( isset( $query_vars['orderby'] ) && 'include' === $query_vars['orderby'] ) {
			$wp_query_args['orderby'] = 'post__in';
		}

		/**
		 * Filters WP Query args for grade post type query.
		 *
		 * @since 2.5.20
		 *
		 * @param array $wp_query_args WP Query args.
		 * @param array $query_vars Query vars.
		 * @param \Masteriyo\Repository\GradeRepository $repository Grade repository object.
		 */
		return apply_filters( 'masteriyo_grade_wp_query_args', $wp_query_args, $query_vars, $this );
	}
}
