<?php
/**
 * Order type.
 *
 * @since 2.6.10
 * @package Masteriyo\Enums
 */

namespace Masteriyo\Enums;

defined( 'ABSPATH' ) || exit;

/**
 * Order type enum class.
 *
 * @since 2.6.10
 */
class OrderType {
	/**
	 * Order parent type.
	 *
	 * @since 2.6.10
	 * @var string
	 */
	const PARENT = 'parent';

	/**
	 * Order renewal type
	 *
	 * @since 2.6.10
	 * @var string
	 */
	const RENEWAL = 'renewal';

	/**
	 * Order resubscribe type
	 *
	 * @since 2.6.10
	 * @var string
	 */
	const RESUBSCRIBE = 'resubscribe';

	/**
	 * Order switch type
	 *
	 * @since 2.6.10
	 * @var string
	 */
	const SWITCH = 'switch';

	/**
	 * Return all Order types.
	 *
	 * @since 2.6.10
	 *
	 * @return array
	 */
	public static function all() {
		return array_unique(
			/**
			 * Filters Order  types.
			 *
			 * @since 2.6.10
			 *
			 * @param string[] $types Order types.
			 */
			apply_filters(
				'masteriyo_pro_subscription_order_types',
				array(
					self::PARENT,
					self::RENEWAL,
					self::RESUBSCRIBE,
					self::SWITCH,
				)
			)
		);
	}

	/**
	 * Return label Order types.
	 *
	 * @since 2.6.10
	 *
	 * @param string $type Order type.
	 *
	 * @return array
	 */
	public static function label( $type ) {
		/**
		 * Filters subscription order types labels.
		 *
		 * @since 2.6.10
		 */
		$labels = apply_filters(
			'masteriyo_pro_subscription_order_types_labels',
			array(
				self::PARENT      => _x( 'Parent', 'Subscription order type', 'learning-management-system' ),
				self::RENEWAL     => _x( 'Renewal', 'Subscription order type', 'learning-management-system' ),
				self::RESUBSCRIBE => _x( 'Resubscribe', 'Subscription order type', 'learning-management-system' ),
				self::SWITCH      => _x( 'Switch', 'Subscription order type', 'learning-management-system' ),
			)
		);

		return $labels[ $type ] ?? '';
	}
}
