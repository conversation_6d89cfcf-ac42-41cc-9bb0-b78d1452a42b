<?php

/**
 * The Template for displaying student's card section of overview tab in the public profile page.
 *
 * @since 2.6.8
 */

use Masteriyo\Addons\PublicProfile\Svg;

defined( 'ABSPATH' ) || exit; // Exit if accessed directly.

/**
 * Fires before rendering overview cards section in public profile page.
 *
 * @since 2.6.8
 */
do_action( 'masteriyo_before_public_profile_overview_cards' );

?>
<div class="masteriyo-col-right--cards">
	<div class="masteriyo-col-right--cards-single masteriyo-enrolled-courses">
		<div class="masteriyo-icon">
			<?php Svg::get( 'enrolled-courses-card', true ); ?>
		</div>

		<div class="masteriyo-card-content">
			<h4 class="title">
				<?php esc_html_e( 'Enrolled Courses', 'learning-management-system' ); ?>
			</h4>

			<span class="count"><?php echo absint( $data['overview_enrolled_courses_count'] ); ?></span>
		</div>
	</div>

	<div class="masteriyo-col-right--cards-single masteriyo-in-progress-courses">
		<div class="masteriyo-icon">
			<?php Svg::get( 'in-progress-courses-card', true ); ?>
		</div>

		<div class="masteriyo-card-content">
			<h4 class="title">
				<?php esc_html_e( 'In Progress Courses', 'learning-management-system' ); ?>
			</h4>

			<span class="count"><?php echo absint( $data['overview_progress_courses_count'] ); ?></span>
		</div>
	</div>

	<div class="masteriyo-col-right--cards-single masteriyo-completed-courses">
		<div class="masteriyo-icon">
			<?php Svg::get( 'completed-courses-card', true ); ?>
		</div>

		<div class="masteriyo-card-content">
			<h4 class="title">
				<?php esc_html_e( 'Completed Courses', 'learning-management-system' ); ?>
			</h4>

			<span class="count"><?php echo absint( $data['overview_completed_courses_count'] ); ?></span>
		</div>
	</div>
</div>
<?php

/**
 * Fires after rendering overview cards section in public profile page.
 *
 * @since 2.6.8
 */
do_action( 'masteriyo_after_public_profile_overview_cards' );
