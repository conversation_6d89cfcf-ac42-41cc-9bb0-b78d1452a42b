<?php
/**
 * Masteriyo CourseFaq setup.
 *
 * @package Masteriyo\CourseFaq
 *
 * @since 2.2.7
 */

namespace Masteriyo\Addons\CourseFaq;

defined( 'ABSPATH' ) || exit;

/**
 * Main Masteriyo CourseFaq class.
 *
 * @class Masteriyo\Addons\CourseFaq\CourseFaq
 */

class CourseFaqAddon {
	/**
	 * Initialize the application.
	 *
	 * @since 2.2.7
	 */
	public function init() {

		$this->init_hooks();
	}

	/**
	 * Initialize hooks.
	 *
	 * @since 2.2.7
	 */
	public function init_hooks() {
		add_action( 'masteriyo_rest_api_get_rest_namespaces', array( $this, 'register_routes' ), 10 );
		add_filter( 'masteriyo_rest_course_schema', array( $this, 'add_course_faq_schema' ) );
		add_action( 'masteriyo_new_course', array( $this, 'save_course_faq_data' ), 10, 2 );
		add_action( 'masteriyo_update_course', array( $this, 'save_course_faq_data' ), 10, 2 );
		add_filter( 'masteriyo_rest_response_course_data', array( $this, 'append_course_faq_data_in_response' ), 10, 4 );
	}

	/**
	 * Register rest routes.
	 *
	 * @since 2.2.7
	 */
	public function register_routes( $namespaces ) {
		$namespace                                      = '\Masteriyo\Addons\CourseFaq\RestApi\Controllers\Version1';
		$namespaces['masteriyo/pro/v1']['courses.faqs'] = "{$namespace}\\CourseFaqsController";
		$namespaces['masteriyo/pro/v1']['courses.faqs.builder'] = "{$namespace}\\CourseFaqBuilderController";

		return $namespaces;
	}

	/**
	 * Add course faq fields to course schema.
	 *
	 * @since 2.2.7
	 *
	 * @param array $schema
	 * @return array
	 */
	public function add_course_faq_schema( $schema ) {
		$schema = wp_parse_args(
			$schema,
			array(
				'course_faq' => array(
					'description' => __( 'Course FAQ setting', 'learning-management-system' ),
					'type'        => 'object',
					'context'     => array( 'view', 'edit' ),
					'items'       => array(
						'type'       => 'object',
						'properties' => array(
							'enable' => array(
								'description' => __( 'Enable course FAQ', 'learning-management-system' ),
								'type'        => 'boolean',
								'default'     => false,
								'context'     => array( 'view', 'edit' ),
							),
						),
					),
				),
			)
		);

		return $schema;
	}

	/**
	 * Save course FAQ data.
	 *
	 * @since 2.2.7
	 *
	 * @param integer $id The course ID.
	 * @param \Masteriyo\Models\Course $object The course object.
	 */
	public function save_course_faq_data( $course_id, $course ) {
		$request = masteriyo_current_http_request();

		if ( null === $request ) {
			return;
		}

		if ( ! isset( $request['course_faq'] ) ) {
			return;
		}

		if ( isset( $request['course_faq']['enable'] ) ) {
			$course->update_meta_data( '_course_faq_enable', masteriyo_string_to_bool( $request['course_faq']['enable'] ) );
			$course->save_meta_data();
		}
	}

	/**
	 * Append course faq to course response.
	 *
	 * @since 2.2.7
	 *
	 * @param array $data Course data.
	 * @param \Masteriyo\Models\Course $course Course object.
	 * @param string $context What the value is for. Valid values are view and edit.
	 * @param \Masteriyo\RestApi\Controllers\Version1\CoursesController $controller REST courses controller object.
	 */
	public function append_course_faq_data_in_response( $data, $course, $context, $controller ) {
		$data['course_faq'] = array(
			'enable' => masteriyo_string_to_bool( $course->get_meta( '_course_faq_enable' ) ),
		);

		return $data;
	}
}
