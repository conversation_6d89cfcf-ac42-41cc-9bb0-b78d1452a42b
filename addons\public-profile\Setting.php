<?php
/**
 * Store global public profile options.
 *
 * @since 2.6.8
 * @package \Masteriyo\Addons\PublicProfile
 */

namespace Masteriyo\Addons\PublicProfile;

defined( 'ABSPATH' ) || exit;


class Setting {

	/**
	 * Global option name.
	 *
	 * @since 2.6.8
	 */
	const OPTION_NAME = 'masteriyo_public_profile_settings';

	/**
	 * Data.
	 *
	 * @since 2.6.8
	 *
	 * @var array
	 */
	protected static $data = array(
		'slug' => 'profile',
	);

	/**
	 * Read the settings.
	 *
	 * @since 2.6.8
	 */
	protected static function read() {
		$settings   = get_option( self::OPTION_NAME, self::$data );
		self::$data = masteriyo_parse_args( $settings, self::$data );

		return self::$data;
	}

	/**
	 * Return all the settings.
	 *
	 * @since 2.6.8
	 *
	 * @return mixed
	 */
	public static function all() {
		return self::read();
	}

	/**
	 * Return global public profile field value.
	 *
	 * @since 2.6.8
	 *
	 * @param string $key
	 *
	 * @return string|array
	 */
	public static function get( $key ) {
		self::read();

		return masteriyo_array_get( self::$data, $key, null );
	}

	/**
	 * Set global public profile field.
	 *
	 * @since 2.6.8
	 *
	 * @param string $key Setting key.
	 * @param mixed $value Setting value.
	 */
	public static function set( $key, $value ) {
		masteriyo_array_set( self::$data, $key, $value );
		self::save();
	}

	/**
	 * Set multiple settings.
	 *
	 * @since 2.6.8
	 *
	 * @param array $args
	 */
	public static function set_props( $args ) {
		self::$data = masteriyo_parse_args( $args, self::$data );
	}

	/**
	 * Save the settings.
	 *
	 * @since 2.6.8
	 */
	public static function save() {
		update_option( self::OPTION_NAME, self::$data );
	}
}
