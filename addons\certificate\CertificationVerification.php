<?php
/**
 * Certificate Verification class.
 *
 * @since 2.6.11
 */

namespace Masteriyo\Addons\Certificate;

defined( 'ABSPATH' ) || exit;


use Masteriyo\Enums\CourseProgressStatus;
use Masteriyo\Query\CourseProgressQuery;
use Masteriyo\Constants;

class CertificationVerification {

	/**
	 * Initialization.
	 *
	 * @since 2.6.11
	 *
	 * @return void
	 */
	public static function init() {
		add_action( 'template_redirect', array( __CLASS__, 'process_certificate_verification' ), 11 );
	}

	/**
	 * Verifies the certificate verification id.
	 *
	 * @since 2.6.11
	 *
	 */
	public static function process_certificate_verification() {
		if ( ! isset( $_GET['certificate-verification-id'] ) ) { // phpcs:ignore WordPress.Security.NonceVerification.Recommended
			return;
		}

		$parts = explode( '-', sanitize_text_field( $_GET['certificate-verification-id'] ) ); // phpcs:ignore WordPress.Security.NonceVerification.Recommended

		if ( count( $parts ) !== 3 ) {
			wp_die( esc_html__( 'Invalid Code Format.', 'learning-management-system' ) );
		}

		list($course_id,$certificate_id,$student_id) = array_map( 'absint', $parts );

		if ( ! masteriyo_get_course( $course_id ) ) {
			wp_die( esc_html__( "We couldn't locate any courses that you asked for.", 'learning-management-system' ) );
		}

		$user = masteriyo_get_user( $student_id );

		if ( is_wp_error( $user ) ) {
			wp_die( esc_html__( 'There are no students with in our records that you asked for.', 'learning-management-system' ) );
		}

		$expected_certificate_id = masteriyo_get_course_certificate_id( $course_id );

		if ( absint( $certificate_id ) !== $expected_certificate_id ) {
			wp_die( esc_html__( 'Sorry, the certificate ID provided is not valid.', 'learning-management-system' ) );
		}

		$progresses = ( new CourseProgressQuery(
			array(
				'user_id'   => $student_id,
				'course_id' => $course_id,
				'status'    => CourseProgressStatus::COMPLETED,
			)
		) )->get_course_progress();

		$progress = current( $progresses );
		if ( empty( $progress ) ) {
			exit;
		}

		$course_completion_date = $progress->get_completed_at();
		$completed_at           = is_null( $course_completion_date ) ? '' : gmdate( 'Y-m-d', $course_completion_date->getTimestamp() );
		$certificate_id         = masteriyo_get_course_certificate_id( $course_id );

		$student         = masteriyo_get_user( $student_id );
		$course          = masteriyo_get_course( $course_id );
		$instructor      = masteriyo_get_user( $course->get_author_id() );
		$verification_id = absint( $_GET['certificate-verification-id'] ); // phpcs:ignore WordPress.Security.NonceVerification.Recommended
		// Pass all required arguments to the render_certificate_verification_template method
		self::render_certificate_verification_template( $student, $course, $instructor, $completed_at, $verification_id );
	}

	/**
	 * Renders Certificate Verification Page
	 *
	 * @since 2.6.11
	 *
	 * @param \Masteriyo\Models\User $object user object
	 * @param \Masteriyo\Models\Course $object course object
	 * @param \Masteriyo\Models\User $object user object
	 * @param date course completion date
	 * @param integer verification id
	 *
	 */
	public static function render_certificate_verification_template( $student, $course, $instructor, $completed_at, $verification_id ) {
			include_once Constants::get( 'MASTERIYO_CERTIFICATE_TEMPLATES' ) . '/certificate-verification.php';
			exit;
	}

}
