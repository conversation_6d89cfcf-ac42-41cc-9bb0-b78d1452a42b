<?php
/**
 * The Template for displaying course attachments in single course page
 *
 * HOWEVER, on occasion Masteriyo will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @package Masteriyo\Addons\CourseAttachments\Templates
 * @version 2.3.0
 */

defined( 'ABSPATH' ) || exit; // Exit if accessed directly.

/**
 * Fires before rendering attachments section in single course page.
 *
 * @since 2.3.0
 */
do_action( 'masteriyo_before_single_course_attachments' );

$layout = masteriyo_get_setting( 'single_course.display.template.layout' ) ?? 'default';
?>

<?php if ( 'layout1' === $layout ) : ?>
<div id="masteriyoSingleCourseAttachmentsTab" class="masteriyo-single-body__main--review-content masteriyo-hidden">
<?php else : ?>
<div class="tab-content course-attachments masteriyo-hidden">
<?php endif; ?>
<?php foreach ( $attachments as $attachment ) : ?>
	<div class="attachment-content-main" data-id="<?php echo absint( $attachment['id'] ); ?>">
		<div class="attachment-content-image">
			<?php echo $attachment['svg']; // phpcs:ignore ?>
			<a href="<?php echo esc_url( $attachment['url'] ); ?>"
				alt="<?php echo esc_attr( $attachment['title'] ); ?>"
				class="attachment-content-image-text">
				<?php echo esc_html( $attachment['filename'] ); ?>
			</a>

				<span class="attachment-content-description">
					<?php echo esc_html( $attachment['formatted_file_size'] ); ?>
				</span>
		</div>
	</div>
<?php endforeach; ?>
</div>
<?php

/**
 * Fires after rendering attachments section in single course page.
 *
 * @since 2.3.0
 */
do_action( 'masteriyo_after_single_course_attachments' );
