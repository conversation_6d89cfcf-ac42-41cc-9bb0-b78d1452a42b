<?php
/**
 * Coupon deduction method type enums.
 *
 * @since 2.18.3
 * @package Masteriyo\Addons\Coupons
 */

namespace Masteriyo\Addons\Coupons\Enums;

defined( 'ABSPATH' ) || exit;

/**
 * Coupon deduction method type enum class.
 *
 * @since 2.18.3
 */
class DeductionMethodType {
	/**
	 * Coupon deduction method type code.
	 *
	 * @since 2.18.3
	 */
	const CODE = 'code';

	/**
	 * Coupon deduction method type automatic.
	 *
	 * @since 2.18.3
	 */
	const AUTOMATIC = 'automatic';

	/**
	 * Return all the Coupon deduction method types.
	 *
	 * @since 2.18.3
	 *
	 * @return array
	 */
	public static function all() {
		/**
		 * Filters Coupon deduction method types list.
		 *
		 * @since 2.18.3
		 *
		 * @param string[] $statuses Coupon deduction method types list.
		 */
		return apply_filters(
			'masteriyo_coupon_deduction_method_types',
			array(
				self::CODE,
				self::AUTOMATIC,
			)
		);
	}
}
