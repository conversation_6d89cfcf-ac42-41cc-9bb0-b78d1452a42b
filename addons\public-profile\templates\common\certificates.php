<?php

/**
 * The Template for displaying certificates tab content in the public profile page.
 *
 * @since 2.6.8
 */

defined( 'ABSPATH' ) || exit; // Exit if accessed directly.

/**
 * Fires before rendering certificates section in public profile page.
 *
 * @since 2.6.8
 */
do_action( 'masteriyo_before_public_profile_certificates' );

?>

<?php if ( count( $certificates ) ) : ?>
	<div class="masteriyo-certificates--content">
		<?php foreach ( $certificates as $certificate ) : ?>
			<div class="masteriyo-certificates--content-gridcard">
				<figure class="masteriyo-certificates-thumbnail">
					<img src="<?php echo esc_attr( $certificate['course']['featured_image_url'] ); ?>" alt="Thumbnail">
				</figure>

				<div class="masteriyo-certificates-desc">

					<h3 class="masteriyo-certificate-title">
						<a href="<?php echo esc_url( $certificate['course']['permalink'] ); ?>" title="<?php echo esc_attr( $certificate['course']['name'] ); ?>">
							<?php echo esc_html( $certificate['course']['name'] ); ?>
						</a>
					</h3>

					<div class="masteriyo-certificate-date">
						<p><?php esc_html_e( 'Started on:', 'learning-management-system' ); ?></p>
						<span><?php echo esc_html( masteriyo_format_datetime( $certificate['course']['started_at'], 'M d, Y' ) ); ?></span>
					</div>

					<div class="masteriyo-certificate-view-button">
						<a href="<?php echo esc_url_raw( $certificate['view_url'] ); ?>" target="_blank" rel="noopener" class="masteriyo-view-btn">
							<?php esc_html_e( 'View Certificate', 'learning-management-system' ); ?>
						</a>
					</div>
				</div>
			</div>
		<?php endforeach; ?>
	</div>

<?php else : ?>
	<?php masteriyo_display_template_notice( 'No Certificates.' ); ?>
<?php endif; ?>
<?php
/**
 * Fires after rendering certificates section in public profile page.
 *
 * @since 2.6.8
 */
do_action( 'masteriyo_after_public_profile_certificates' );
