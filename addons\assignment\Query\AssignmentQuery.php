<?php
/**
 * Class for parameter-based Assignment querying
 *
 * @package Masteriyo\Addons\Assignment
 * @version 2.3.5
 * @since   2.3.5
 */

namespace Masteriyo\Addons\Assignment\Query;

use Masteriyo\Abstracts\ObjectQuery;
use Masteriyo\Enums\PostStatus;

defined( 'ABSPATH' ) || exit;

/**
 * Assignment query class.
 */
class AssignmentQuery extends ObjectQuery {

	/**
	 * Valid query vars for assignments.
	 *
	 * @since 2.3.5
	 *
	 * @return array
	 */
	protected function get_default_query_vars() {
		return array_merge(
			parent::get_default_query_vars(),
			array(
				'type'        => 'mto-assignment',
				'status'      => array( PostStatus::DRAFT, PostStatus::PENDING, PostStatus::PVT, PostStatus::PUBLISH ),
				'include'     => array(),
				'created_at'  => '',
				'modified_at' => '',
				'course_id'   => '',
			)
		);
	}

	/**
	 * Get assignments matching the current query vars.
	 *
	 * @since 2.3.5
	 *
	 * @return Masteriyo\Addons\Assignment\Models\Assignment[] Assignment objects.
	 */
	public function get_assignments() {
		/**
		 * Filters assignment object query args.
		 *
		 * @since 2.3.5
		 *
		 * @param array $query_args The object query args.
		 */
		$args    = apply_filters( 'masteriyo_assignment_object_query_args', $this->get_query_vars() );
		$results = masteriyo( 'assignment.store' )->query( $args );

		/**
		 * Filters assignment object query results.
		 *
		 * @since 2.3.5
		 *
		 * @param Masteriyo\Addons\Assignment\Models\Assignment[] $results The query results.
		 * @param array $query_args The object query args.
		 */
		return apply_filters( 'masteriyo_assignment_object_query', $results, $args );
	}
}
