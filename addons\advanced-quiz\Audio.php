<?php
/**
 * Audio question model.
 *
 * @since 2.4.0
 *
 * @package Masteriyo\Addons\AdvancedQuiz
 */

namespace Masteriyo\Addons\AdvancedQuiz;

use Masteriyo\Models\Question\Question;
use Masteriyo\Models\Question\QuestionInterface;

defined( 'ABSPATH' ) || exit;

/**
 * Audio question model.
 *
 * @since 2.4.0
 */
class Audio extends Question implements QuestionInterface {
	/**
	 * Question type.
	 *
	 * @since 2.4.0
	 *
	 * @var string $type Question type.
	 */
	protected $type = 'audio';

	/**
	 * Return true if the answer should be manually reviewed and manually assigned points.
	 *
	 * @since 2.4.0
	 *
	 * @return boolean
	 */
	public function is_reviewable() {
		return true;
	}

	/**
	 * Extra data.
	 *
	 * @since 2.4.0
	 *
	 * @var array
	 */
	protected $extra_data = array(
		'files' => array(),
	);

	/**
	 * Check whether the chosen answer is correct or not.
	 *
	 * @since 2.4.0
	 *
	 * @param array  $chosen_answer Answer chosen by user.
	 * @param string $context Options: 'edit', 'view'.
	 *
	 * @return bool
	 */
	public function check_answer( $chosen_answer, $context = 'edit' ) {
		/**
		 * Filters boolean: true if the chosen answer is correct.
		 *
		 * @since 2.4.0
		 *
		 * @param boolean $bool true if the chosen answer is correct.
		 * @param array $chosen_answer Chosen answer.
		 * @param string $context Context.
		 * @param Masteriyo\Models\Question\TrueFalse $true_false True/false question object.
		 */
		return apply_filters( "masteriyo_question_check_answer_{$this->type}", false, $chosen_answer, $context, $this );
	}

	/**
	 * Get correct answers only.
	 *
	 * @since 2.4.0
	 *
	 * @return mixed
	 */
	public function get_correct_answers() {
		return array();
	}

	/*
	|--------------------------------------------------------------------------
	| CRUD Getters
	|--------------------------------------------------------------------------
	*/

	/**
	 * Returns audio files.
	 *
	 * @since  2.4.0
	 *
	 * @param  string $context What the value is for. Valid values are view and edit.
	 *
	 * @return array
	 */
	public function get_files( $context = 'view' ) {
		return $this->get_prop( 'files', $context );
	}

	/*
	|--------------------------------------------------------------------------
	| CRUD Setters
	|--------------------------------------------------------------------------
	*/

	/**
	 * Set audio files.
	 *
	 * @since 2.4.0
	 *
	 * @param array $files Files.
	 */
	public function set_files( $files ) {
		$this->set_prop( 'files', $files );
	}
}
