<?php
/**
 * Masteriyo course bundle list pagination elementor widget class.
 *
 * @package Masteriyo\Addons\CourseBundle\Widgets
 *
 * @since 2.14.0
 */

namespace Masteriyo\Addons\CourseBundle\Widgets;

use Masteriyo\Addons\ElementorIntegration\Widgets\CourseArchivePaginationWidget;
use Masteriyo\Enums\PostStatus;
use Masteriyo\PostType\PostType;

defined( 'ABSPATH' ) || exit;

/**
 * Masteriyo course bundle list pagination elementor widget class.
 *
 * @package Masteriyo\Addons\CourseBundle\Widgets
 *
 * @since 2.14.0
 */
class BundleListPaginationWidget extends CourseArchivePaginationWidget {

	/**
	 * Get widget name.
	 *
	 * @since 2.14.0
	 *
	 * @return string
	 */
	public function get_name() {
		return 'masteriyo-course-bundle-list-pagination';
	}

	/**
	 * Get widget title.
	 *
	 * @since 2.14.0
	 *
	 * @return string
	 */
	public function get_title() {
		return __( 'Course Bundle List Pagination', 'learning-management-system' );
	}

	/**
	 * Get widget icon.
	 *
	 * @since 2.14.0
	 *
	 * @return string Widget icon.
	 */
	public function get_icon() {
		return 'masteriyo-course-bundle-list-pagination-widget-icon';
	}

	/**
	 * Get widget keywords.
	 *
	 * @since 2.14.0
	 *
	 * @return string[]
	 */
	public function get_keywords() {
		return array( 'pagination', 'page', 'numbers' );
	}

	/**
	 * Render the widget output on the frontend.
	 *
	 * @since 2.14.0
	 */
	protected function content_template() {
		// Backup original query object.
		$old_query = $GLOBALS['wp_query'];

		$query = isset( $GLOBALS['mto_course_bundle_query'] ) ? $GLOBALS['mto_course_bundle_query'] : null;

		if ( ! $query ) {
			$args  = array(
				'post_type'      => PostType::COURSE_BUNDLE,
				'post_status'    => PostStatus::PUBLISH,
				'posts_per_page' => masteriyo_get_setting( 'course_archive.display.per_page' ),
				'paged'          => 1,
				'order'          => 'DESC',
				'orderby'        => 'date',
			);
			$query = new \WP_Query( $args );
		}

		// Switch to the given query object.
		$GLOBALS['wp_query'] = $query; // phpcs:ignore WordPress.WP.GlobalVariablesOverride.Prohibited

		// Generate pagination links with the new query object.
		masteriyo_archive_navigation();

		// Restore the origin query object.
		$GLOBALS['wp_query'] = $old_query; // phpcs:ignore WordPress.WP.GlobalVariablesOverride.Prohibited
	}

	/**
	 * Render the widget output on the frontend.
	 *
	 * @since 2.14.0
	 */
	protected function render() {
		// Backup original query object.
		$old_query = $GLOBALS['wp_query'];

		$query = isset( $GLOBALS['mto_course_bundle_query'] ) ? $GLOBALS['mto_course_bundle_query'] : null;

		if ( ! $query ) {
			$args  = array(
				'post_type'      => PostType::COURSE_BUNDLE,
				'post_status'    => PostStatus::PUBLISH,
				'posts_per_page' => masteriyo_get_setting( 'course_archive.display.per_page' ),
				'paged'          => 1,
				'order'          => 'DESC',
				'orderby'        => 'date',
			);
			$query = new \WP_Query( $args );
		}

		// Switch to the given query object.
		$GLOBALS['wp_query'] = $query; // phpcs:ignore WordPress.WP.GlobalVariablesOverride.Prohibited

		// Generate pagination links with the new query object.
		masteriyo_archive_navigation();

		// Restore the origin query object.
		$GLOBALS['wp_query'] = $old_query; // phpcs:ignore WordPress.WP.GlobalVariablesOverride.Prohibited
	}
}
