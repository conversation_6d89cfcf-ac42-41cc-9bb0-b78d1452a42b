<?php
/**
 * Class for parameter-based course FAQ query.
 *
 * @package  Masteriyo\Addons\CourseFaq
 * @version 2.2.7
 * @since   2.2.7
 */

namespace Masteriyo\Addons\CourseFaq;

use Masteriyo\Abstracts\ObjectQuery;

defined( 'ABSPATH' ) || exit;

/**
 * Course FAQ query class.
 */
class CourseFaqQuery extends ObjectQuery {

	/**
	 * Valid query vars for course progress.
	 *
	 * @since 2.2.7
	 *
	 * @return array
	 */
	protected function get_default_query_vars() {
		return array_merge(
			parent::get_default_query_vars(),
			array(
				'user_id'    => '',
				'course_id'  => 0,
				'status'     => '',
				'created_at' => null,
				'orderby'    => 'id',
			)
		);
	}

	/**
	 * Get course FAQ matching the current query vars.
	 *
	 * @since 2.2.7
	 *
	 * @return Masteriyo\Addons\CourseFaq\Models\CourseFaq[] Course FAQ objects
	 */
	public function get_course_faqs() {
		/**
		 * Filters course FAQ object query args.
		 *
		 * @since 2.2.7
		 *
		 * @param array $query_args The object query args.
		 */
		$args    = apply_filters( 'masteriyo_course_progress_object_query_args', $this->get_query_vars() );
		$results = masteriyo( 'course-faq.store' )->query( $args );

		/**
		 * Filters course FAQ object query results.
		 *
		 * @since 2.2.7
		 *
		 * @param Masteriyo\Addons\CourseFaq\Models\CourseFaq[] $results The query results.
		 * @param array $query_args The object query args.
		 */
		return apply_filters( 'masteriyo_course_faq_object_query', $results, $args );
	}
}
