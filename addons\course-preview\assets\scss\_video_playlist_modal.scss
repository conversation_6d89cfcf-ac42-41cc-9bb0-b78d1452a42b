// Variables
$primaryColor: #337ab7;
$secondaryColor: #555;

// Modal Styles
#masteriyo-course-preview-video-playlist-modal {
	display: none;
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.8);
	z-index: 9999;
	overflow: auto;

	&.show {
		display: block;
	}

	.masteriyo-course-preview-modal-content {
		background-color: var(--masteriyo-color-white);
		max-width: 700px;
		border-radius: $border_radius_8;
		padding: $spacing_20px;
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: $spacing_16px;
		margin: $spacing_60px auto;

		.masteriyo-course-preview__header {
			width: 100%;
			display: flex;
			justify-content: space-between;
			margin-bottom: $spacing_10px;

			&-title {
				&-sub-title {
					font-size: 15px;
					line-height: 25px;
					color: #383838;
					font-weight: 700;
				}

				&-main-title {
					font-size: 24px;
					line-height: 34px;
					color: #222222;
					font-weight: 700;
				}
			}
		}

		.close {
			color: #aaa;
			font-size: 28px;
			font-weight: bold;
			cursor: pointer;

			&:hover,
			&:focus {
				color: black;
				text-decoration: none;
				cursor: pointer;
			}
		}

		.masteriyo-course-preview-video-player-container {
			position: relative;
			width: 100%;
			padding-bottom: 56.25%; // Aspect ratio 16:9
			margin-bottom: $spacing_10px;

			.masteriyo-course-preview-video-player {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
			}
		}

		.masteriyo-course-preview-title {
			color: #222222;
			font-size: 20px;
			font-weight: 700;
			margin-bottom: $spacing_16px;
		}

		.masteriyo-course-preview-playlist {
			width: 100%;

			&__wrapper {
				overflow-x: hidden;
				overflow-y: auto;
				min-height: 1vh;
				max-height: 30vh;

				&::-webkit-scrollbar {
					width: 6px;
				}

				&::-webkit-scrollbar-track {
					box-shadow: inset 0 0 5px #949494;
					border-radius: 10px;
				}

				&::-webkit-scrollbar-thumb {
					background: #909090;
					border-radius: 10px;
				}

				&::-webkit-scrollbar-thumb:hover {
					background: #515151;
				}
			}

			.masteriyo-course-preview-playlist-item {
				display: flex;
				align-items: center;
				gap: $spacing_16px;
				cursor: pointer;
				transition: background-color 0.3s ease;
				margin-bottom: $spacing_8px;
				padding-right: $spacing_20px;

				&.active {
					background-color: #d1d1d1;
				}

				&:hover {
					background-color: #e2e2e2;
				}

				.masteriyo-course-preview-preview-thumbnail {
					position: relative;
					width: 80px;
					height: 60px;

					img.masteriyo-course-preview-preview-image {
						width: 100%;
						height: 100%;
						object-fit: cover;
					}

					.masteriyo-course-preview-play-icon {
						position: absolute;
						top: 50%;
						left: 50%;
						transform: translate(-50%, -50%);
						width: 40px;
						height: 40px;
						background-color: rgba(0, 0, 0, 0.5);
						border-radius: 50%;
						display: flex;
						align-items: center;
						justify-content: center;
						color: var(--masteriyo-color-white);

						svg {
							width: 24px;
							height: 24px;
							fill: var(--masteriyo-color-white);
							margin-left: $spacing_5px;
						}
					}
				}

				.masteriyo-course-preview-preview-details {
					flex: 1;
					display: flex;
					align-items: center;
					justify-content: space-between;
					gap: $spacing_16px;

					.masteriyo-course-preview-preview-title {
						font-weight: 700;
					}

					.masteriyo-course-preview-preview-duration {
						color: $secondaryColor;
						font-size: 14px;
					}
				}
			}
		}
	}
}
