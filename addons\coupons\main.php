<?php

defined( 'ABSPATH' ) || exit;

/**
 * Addon Name: Coupons
 * Addon URI: https://masteriyo.com/wordpress-lms/
 * Description: Allows to create coupons
 * Author: Masteriyo
 * Author URI: https://masteriyo.com
 * Addon Type: enhancement
 * Plan: Starter,Pro,Elite,Growth,Scale
 */

use Masteriyo\Addons\Coupons\CouponsAddon;
use Masteriyo\Pro\Addons;

define( 'MASTERIYO_COUPONS_ADDON_FILE', __FILE__ );
define( 'MASTERIYO_COUPONS_ADDON_BASENAME', plugin_basename( __FILE__ ) );
define( 'MASTERIYO_COUPONS_ADDON_DIR', __DIR__ );
define( 'MASTERIYO_COUPONS_TEMPLATES', __DIR__ . '/templates' );
define( 'MASTERIYO_COUPONS_ADDON_SLUG', 'coupons' );


add_filter(
	'masteriyo_pro_addon_coupons_activation_requirements',
	function ( $result, $request, $controller ) {
		$addons = masteriyo( 'addons' );

		if ( ! $addons->is_allowed( 'coupons' ) ) {
			$result = __( 'This addon is not allowed in your plan.', 'learning-management-system' );
		}

		return $result;
	},
	10,
	3
);

add_filter(
	'masteriyo_pro_addon_data',
	function( $data, $slug ) {
		if ( 'coupons' !== $slug ) {
			return $data;
		}

		$addons                        = masteriyo( 'addons' );
		$fulfilled                     = $addons->is_allowed( 'coupons' ) ? true : false;
		$data['requirement_fulfilled'] = masteriyo_bool_to_string( $fulfilled );

		return $data;
	},
	10,
	2
);

// Bail early if the addon is not active.
if ( ! ( new Addons() )->is_active( MASTERIYO_COUPONS_ADDON_SLUG ) ) {
	return;
}

require_once __DIR__ . '/helper.php';

/**
 * Include service providers for coupons.
 */
add_filter(
	'masteriyo_service_providers',
	function( $providers ) {
		return array_merge( $providers, require_once __DIR__ . '/config/providers.php' );
	}
);

// Initialize the coupons addon.
CouponsAddon::instance()->init();
