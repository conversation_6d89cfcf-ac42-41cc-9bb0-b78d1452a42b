<?php

/**
 * The template for displaying course content within loops
 *
 * @package Masteriyo\Addons\CourseBundle\Templates
 *
 * @version 2.12.0
 */

use Masteriyo\Enums\PostStatus;
use Masteriyo\Pro\Addons;

defined( 'ABSPATH' ) || exit;

/** @var \Masteriyo\Addons\CourseBundle\Models\CourseBundle */
global $course_bundle;

// Ensure visibility.
if ( empty( $course_bundle ) ) {
	return;
}

// Perform the check outside of array_map
$is_multiple_currency_enabled = ( new Addons() )->is_active( MASTERIYO_MULTIPLE_CURRENCY_ADDON_SLUG ) && masteriyo_string_to_bool( get_post_meta( $course_bundle->get_id(), '_multiple_currency_enabled', true ) );

$courses = $course_bundle->get_bundled_courses( PostStatus::PUBLISH );

$courses_count = count( $courses );

$course_bundle = masteriyo_course_archive_bundle( $course_bundle );

$courses_price = masteriyo_courses_of_course_bundle( $course_bundle, $courses, $is_multiple_currency_enabled );

$is_slider_enabled = masteriyo_is_course_bundle_carousel_enabled();
$slider_class      = '';
if ( $is_slider_enabled ) {
	$slider_class = 'swiper-slide';
}

?>
<div class="masteriyo-col <?php echo esc_attr( $slider_class ); ?>">
	<div class="masteriyo-bundle">
		<div class="masteriyo-bundle--top">
			<h2 class="masteriyo-bundle__title">
				<a href="<?php echo esc_url( $course_bundle->get_permalink() ); ?>" title="<?php echo esc_attr( $course_bundle->get_name() ); ?>">
					<?php echo esc_html( $course_bundle->get_name() ); ?>
				</a>
			</h2>
			<div class="masteriyo-bundle__info">
				<span class="masteriyo-bundle__courses-count">
					<?php
					printf(
						/* translators: %d: number of courses */
						esc_html( _n( '%d course', '%d courses', $courses_count, 'learning-management-system' ) ),
						esc_html( $courses_count )
					);
					?>
				</span>
				<div class="masteriyo-bundle__price">
					<del>
						<?php
						echo esc_html(
							masteriyo_price(
								$courses_price,
								array(
									'currency' => $course_bundle->get_currency(),
									'html'     => false,
								)
							)
						);
						?>
					</del>
					<span>
						<?php
						$price = esc_html(
							masteriyo_price(
								$course_bundle->get_price(),
								array(
									'currency' => $course_bundle->get_currency(),
									'html'     => false,
								)
							)
						);
						/**
						 * Filters the price for a course bundle.
						 *
						 * @since 2.15.0
						 *
						 * @param string $price.
						 * @param Masteriyo\Addons\CourseBundle\Models\CourseBundle $course_bundle Course object.
						 */
						echo esc_html( apply_filters( 'masteriyo_course_bundle_buy_button_price', $price, $course_bundle ) );
						?>
					</span>
				</div>
			</div>
		</div>
		<div class="masteriyo-bundle--bottom">
			<div class="masteriyo-bundle__courses">
				<ul class="masteriyo-bundle__courses-list">
					<?php foreach ( $courses as $course ) : ?>
						<li class="masteriyo-bundle__courses-item">
							<div class="masteriyo-bundle__courses-item-img">
								<?php echo wp_kses( $course->get_image( 'masteriyo_single' ), 'masteriyo_image' ); ?>
							</div>
							<div class="masteriyo-bundle__courses-item-info">
								<h3 class="masteriyo-course-title"><?php echo esc_html( $course->get_title() ); ?></h3>
							</div>
							<span class="masteriyo-bundle__courses-item__price">
								<?php echo esc_html( masteriyo_price( $course->get_price(), array( 'html' => false ) ) ); ?>
							</span>
						</li>
					<?php endforeach; ?>
				</ul>
			</div>
			<?php
			// $additional_attributes = apply_filters( 'masteriyo_add_to_cart_button_attributes', array(), $course );
			?>
			<div class="masteriyo-bundle__cta">
				<?php masteriyo_course_bundle_buy_button( $course_bundle ); ?>
			</div>
		</div>
	</div>
</div>
<?php

/**
 * Action at the end of course bundle content.
 *
 * @since 2.15.0
 *
 */
do_action( 'masteriyo_courses_bundle_popup_modal' );
