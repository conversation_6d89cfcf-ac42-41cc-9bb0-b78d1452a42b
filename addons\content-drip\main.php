<?php

defined( 'ABSPATH' ) || exit;

/**
 * Addon Name: Content Drip
 * Addon URI: https://masteriyo.com/wordpress-lms/
 * Description: Have control on how you want to deliver lessons of the course to your students with content dripping feature.
 * Author: Masteriyo
 * Author URI: https://masteriyo.com
 * Addon Type: enhancement
 * Plan: Starter,Pro,Elite,Growth,Scale
 */

use Masteriyo\Pro\Addons;
use Masteriyo\Pro\License;

define( 'MASTERIYO_CONTENT_DRIP_ADDON_FILE', __FILE__ );
define( 'MASTERIYO_CONTENT_DRIP_ADDON_BASENAME', plugin_basename( __FILE__ ) );
define( 'MASTERIYO_CONTENT_DRIP_ADDON_DIR', __DIR__ );
define( 'MASTERIYO_CONTENT_DRIP_ASSETS', __DIR__ . '/assets' );
define( 'MASTERIYO_CONTENT_DRIP_TEMPLATES', __DIR__ . '/templates' );
define( 'MASTERIYO_CONTENT_DRIP_ADDON_SLUG', 'content-drip' );


add_filter(
	'masteriyo_pro_addon_content-drip_activation_requirements',
	function ( $result, $request, $controller ) {
		$addons = masteriyo( 'addons' );

		if ( ! $addons->is_allowed( 'content-drip' ) ) {
			$result = __( 'This addon is not allowed in your plan.', 'learning-management-system' );
		}

		return $result;
	},
	10,
	3
);

add_filter(
	'masteriyo_pro_addon_data',
	function( $data, $slug ) {
		if ( 'content-drip' !== $slug ) {
			return $data;
		}

		$fulfilled = true;
		$addons    = masteriyo( 'addons' );

		if ( ! $addons->is_allowed( 'content-drip' ) ) {
			$fulfilled = false;
		}

		$data['requirement_fulfilled'] = masteriyo_bool_to_string( $fulfilled );

		return $data;
	},
	10,
	2
);

// Bail early if the addon is not active.
if ( ! ( new Addons() )->is_active( MASTERIYO_CONTENT_DRIP_ADDON_SLUG ) ) {
	return;
}

/**
 * Include service providers for Content Drip.
 */
add_filter(
	'masteriyo_service_providers',
	function( $providers ) {
		return array_merge( $providers, require_once __DIR__ . '/config/providers.php' );
	}
);

/**
 * Initialize Masteriyo Content Drip.
 */
add_action(
	'masteriyo_before_init',
	function() {
		masteriyo( 'addons.content-drip' )->init();
	}
);
