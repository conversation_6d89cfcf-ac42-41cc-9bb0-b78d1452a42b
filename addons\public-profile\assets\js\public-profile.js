/**
 * Masteriyo Public profile JS.
 *
 * @since 2.6.8
 */
(function ($) {
	var masteriyoPublicProfile = {
		/**
		 * Initializes the public profile js.
		 *
		 * @since 2.6.8
		 */
		init: function () {
			this.bindUIActions();
			this.handleInitialTabSelection();
		},

		/**
		 * Binds event handlers to elements.
		 *
		 * @since 2.6.8
		 */
		bindUIActions: function () {
			this.tabContentHandler();
		},

		/**
		 * Handles the tab content functionality.
		 *
		 * @since 2.6.8
		 */
		tabContentHandler: function () {
			$(document.body).on(
				'click',
				'.masteriyo-col-right--tabbar-list, .masteriyo-secondary-btn.show-all',
				function (e) {
					e.preventDefault();

					var targetTabId = $(this).data('target-tab');

					$('.masteriyo-container .masteriyo-col-right--tabbar')
						.find('.masteriyo-col-right--tabbar-list')
						.removeClass('active');

					$(
						'.masteriyo-container .masteriyo-col-right--tabbar-list[data-target-tab="' +
							targetTabId +
							'"]',
					).addClass('active');

					masteriyoPublicProfile.showTabContent(targetTabId);
				},
			);
		},

		/**
		 * Shows the tab content associated with the provided tab ID.
		 *
		 * @since 2.6.8
		 *
		 * @param {string} tabId - The ID of the tab content to show.
		 */
		showTabContent: function (tabId) {
			// Hide all tab contents by default
			$('.masteriyo-container .masteriyo-col-right--tab-content').addClass(
				'hidden',
			);

			$('#' + tabId).removeClass('hidden');
		},

		/**
		 * Handles the initial tab selection based on the query parameter.
		 *
		 * @since 2.13.0
		 */
		handleInitialTabSelection: function () {
			const urlParams = new URLSearchParams(window.location.search);
			const postType = urlParams.get('post_type');

			if (postType === 'mto-course') {
				const targetTabId = 'masteriyo-courses-offered-main-content';

				$('.masteriyo-container .masteriyo-col-right--tabbar-list').removeClass(
					'active',
				);

				$(
					'.masteriyo-container .masteriyo-col-right--tabbar-list[data-target-tab="' +
						targetTabId +
						'"]',
				).addClass('active');

				this.showTabContent(targetTabId);
			}
		},
	};

	masteriyoPublicProfile.init();
})(jQuery);
