<?php

defined( 'ABSPATH' ) || exit;

/**
 * Coupon helper functions.
 *
 * @since 2.5.12
 *
 * @package \Masteriyo\Addons\Coupons
 */

use Masteriyo\Addons\Coupons\CouponDiscounts;
use Masteriyo\Addons\Coupons\Enums\CouponStatus;
use Masteriyo\Addons\Coupons\Enums\DeductionMethodType;
use Masteriyo\Constants;
use Masteriyo\PostType\PostType;

if ( ! function_exists( 'masteriyo_get_coupon' ) ) {
	/**
	 * Get coupon.
	 *
	 * @since 2.5.12
	 *
	 * @param int|\Masteriyo\Addons\Coupons\Models\Coupon|\WP_Post $coupon Coupon id or Coupon Model or Post.
	 *
	 * @return \Masteriyo\Addons\Coupons\Models\Coupon|null
	 */
	function masteriyo_get_coupon( $coupon ) {
		$coupon_obj   = masteriyo( 'coupon' );
		$coupon_store = masteriyo( 'coupon.store' );

		if ( is_a( $coupon, \Masteriyo\Addons\Coupons\Models\Coupon::class ) ) {
			$id = $coupon->get_id();
		} elseif ( is_a( $coupon, 'WP_Post' ) ) {
			$id = $coupon->ID;
		} else {
			$id = absint( $coupon );
		}

		try {
			$id = absint( $id );
			$coupon_obj->set_id( $id );
			$coupon_store->read( $coupon_obj );
		} catch ( \Exception $e ) {
			return null;
		}

		/**
		 * Filters coupon object.
		 *
		 * @since 2.5.12
		 *
		 * @param \Masteriyo\Models\Coupon $coupon_obj Coupon object.
		 * @param int|\Masteriyo\Models\Coupon|WP_Post $coupon Coupon id or Coupon Model or Post.
		 */
		return apply_filters( 'masteriyo_get_coupon', $coupon_obj, $coupon );
	}
}

if ( ! function_exists( 'masteriyo_create_coupon_object' ) ) {
	/**
	 * Create instance of coupon model.
	 *
	 * @since 2.5.12
	 *
	 * @return \Masteriyo\Addons\Coupons\Models\Coupon
	 */
	function masteriyo_create_coupon_object() {
		return masteriyo( 'coupon' );
	}
}

if ( ! function_exists( 'masteriyo_create_coupon_store' ) ) {
	/**
	 * Create instance of coupon repository.
	 *
	 * @since 2.5.12
	 *
	 * @return \Masteriyo\Addons\Coupons\Repository\CouponRepository
	 */
	function masteriyo_create_coupon_store() {
		return masteriyo( 'coupon.store' );
	}
}

if ( ! function_exists( 'masteriyo_coupons' ) ) {
	/**
	 * Create instance of the Coupons class.
	 *
	 * @since 2.5.12
	 *
	 * @return \Masteriyo\Addons\Coupons\Coupons
	 */
	function masteriyo_coupons() {
		return masteriyo( 'coupons' );
	}
}

if ( ! function_exists( 'masteriyo_create_order_item_coupon_object' ) ) {
	/**
	 * Create instance of coupon order item model.
	 *
	 * @since 2.5.12
	 *
	 * @return \Masteriyo\Addons\Coupons\Models\OrderItemCoupon
	 */
	function masteriyo_create_order_item_coupon_object() {
		return masteriyo( 'order-item.coupon' );
	}
}

if ( ! function_exists( 'masteriyo_get_coupon_by_code' ) ) {
	/**
	 * Get coupon object by code.
	 *
	 * @since 2.5.12
	 *
	 * @param string $code Coupon code.
	 *
	 * @return \Masteriyo\Addons\Coupons\Models\Coupon|null
	 */
	function masteriyo_get_coupon_by_code( $code ) {
		$query = new WP_Query(
			array(
				'title'          => $code,
				'post_status'    => CouponStatus::all(),
				'post_type'      => PostType::COUPON,
				'posts_per_page' => 1,
			)
		);

		$posts  = $query->get_posts();
		$coupon = $posts ? masteriyo_get_coupon( $posts[0] ) : null;

		return apply_filters( 'masteriyo_get_coupon_by_code', $coupon, $code );
	}
}

if ( ! function_exists( 'masteriyo_format_coupon_code' ) ) {
	/**
	 * Format a coupon code.
	 *
	 * @since 2.5.12
	 *
	 * @param string $value Coupon code to format.
	 *
	 * @return string
	 */
	function masteriyo_format_coupon_code( $value ) {
		return apply_filters( 'masteriyo_format_coupon_code', $value );
	}
}

if ( ! function_exists( 'masteriyo_round_half_down' ) ) {
	/**
	 * Round half down in PHP 5.2.
	 *
	 * @since 2.5.12
	 *
	 * @param float $value Value to round.
	 * @param int   $precision Precision to round down to.
	 *
	 * @return float
	 */
	function masteriyo_round_half_down( $value, $precision ) {
		$value = masteriyo_float_to_string( $value );

		if ( false !== strstr( $value, '.' ) ) {
			$value = explode( '.', $value );

			if ( strlen( $value[1] ) > $precision && substr( $value[1], -1 ) === '5' ) {
				$value[1] = substr( $value[1], 0, -1 ) . '4';
			}

			$value = implode( '.', $value );
		}

		return masteriyo_round( floatval( $value ), $precision );
	}
}

if ( ! function_exists( 'masteriyo_round_discount' ) ) {
	/**
	 * Round discount.
	 *
	 * @since 2.5.12
	 *
	 * @param  double $value Amount to round.
	 * @param  int    $precision DP to round.
	 *
	 * @return float
	 */
	function masteriyo_round_discount( $value, $precision ) {
		if ( version_compare( PHP_VERSION, '5.3.0', '>=' ) ) {
			return masteriyo_round( $value, $precision, Constants::get( 'MASTERIYO_DISCOUNT_ROUNDING_MODE' ) );
		}

		if ( PHP_ROUND_HALF_DOWN === Constants::get( 'MASTERIYO_DISCOUNT_ROUNDING_MODE' ) ) {
			return masteriyo_round_half_down( $value, $precision );
		}

		return masteriyo_round( $value, $precision );
	}
}

if ( ! function_exists( 'masteriyo_get_automatic_coupon_ids' ) ) {
	/**
	 * Get automatic coupon IDs.
	 *
	 * @since 2.18.3
	 *
	 * @return int[] List of coupon IDs.
	 */
	function masteriyo_get_automatic_coupon_ids() {
		$query = new WP_Query(
			array(
				'post_status'    => CouponStatus::ACTIVE,
				'post_type'      => PostType::COUPON,
				'posts_per_page' => -1,
				'fields'         => 'ids',
				'meta_query'     => array(
					array(
						'key'   => '_method',
						'value' => DeductionMethodType::AUTOMATIC,
					),
				),
				'no_found_rows'  => true,
				'cache_results'  => true,
			)
		);

		// Return an array of coupon IDs.
		return ! empty( $query->posts ) ? $query->posts : array();
	}
}
