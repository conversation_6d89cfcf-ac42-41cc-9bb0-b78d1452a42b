<?php

/**
 * The Template for displaying overview enrolled courses in the public profile page.
 *
 * @since 2.6.8
 */

use Masteriyo\Addons\PublicProfile\Svg;

defined( 'ABSPATH' ) || exit; // Exit if accessed directly.

/**
 * Fires before rendering overview enrolled courses section in public profile page.
 *
 * @since 2.6.8
 */
do_action( 'masteriyo_before_public_profile_overview_enrolled_courses' );

?>
<div class="masteriyo-col-right--enrolled-courses">
	<div class="masteriyo-enrolled-courses--heading">
		<h3 class="title"><?php esc_html_e( 'Enrolled Courses', 'learning-management-system' ); ?></h3>

		<?php if ( count( $data['overview_enrolled_courses'] ) ) : ?>
			<a href="javascript:;" class="masteriyo-secondary-btn show-all" data-target-tab="masteriyo-enrolled-courses-main-content">
				<?php esc_html_e( 'Show All', 'learning-management-system' ); ?>
				<?php Svg::get( 'show-all', true ); ?>
			</a>
		<?php endif; ?>

	</div>

	<div class="masteriyo-enrolled-courses--content">
		<?php if ( count( $data['overview_enrolled_courses'] ) ) : ?>
			<?php foreach ( $data['overview_enrolled_courses'] as $enrolled_course ) : ?>
				<div class="masteriyo-enrolled-courses--content-listcard">
					<figure class="masteriyo-course-thumbnail">
						<img src="<?php echo esc_attr( $enrolled_course->get_featured_image_url( 'masteriyo_medium' ) ); ?>" alt="Thumbnail">
					</figure>

					<div class="masteriyo-enrolled-course-desc">
						<?php foreach ( $enrolled_course->get_categories() as $category ) : ?>
							<a href="<?php echo esc_url( $category->get_permalink() ); ?>" alt="<?php echo esc_attr( $category->get_name() ); ?>">
								<span class="masteriyo-course-category">
									<?php echo esc_html( $category->get_name() ); ?>
								</span>
							</a>
						<?php endforeach; ?>

						<h2 class="masteriyo-course-title">
							<a href="<?php echo esc_url( $enrolled_course->get_permalink() ); ?>" title="<?php echo esc_attr( $enrolled_course->get_name() ); ?>">
								<?php echo esc_html( $enrolled_course->get_name() ); ?>
							</a>
						</h2>

						<div class="masteriyo-course-duration-percent-wrapper">
							<div class="masteriyo-course-duration">
								<?php Svg::get( 'clock', true ); ?>
								<span><?php echo esc_html( masteriyo_minutes_to_time_length_string( $enrolled_course->get_duration() ) ); ?></span>
							</div>

							<span class="masteriyo-course-percent">
								<?php
								printf(
									/* translators: %s: course progress in percentage */
									esc_html__( '%s Completed', 'learning-management-system' ),
									esc_html( $enrolled_course->get_progress_status( true, masteriyo_get_user( $data['user_profile']['id'] ) ) )
								);
								?>
							</span>
						</div>

						<div class="masteriyo-course-progress">
							<div class="masteriyo-course-progress-value" style="--value: <?php echo esc_attr( $enrolled_course->get_progress_status( true, masteriyo_get_user( $data['user_profile']['id'] ) ) ); ?>"></div>
						</div>

						<div class="masteriyo-course-started-date">
							<span> <?php echo esc_html( 'Started on ' . masteriyo_format_datetime( $enrolled_course->progress->get_started_at(), 'M d, Y' ) ); ?></span>
						</div>
					</div>
				</div>
			<?php endforeach; ?>
		<?php else : ?>
			<?php masteriyo_display_template_notice( 'No enrolled courses.' ); ?>
		<?php endif; ?>
	</div>
</div>
<?php

/**
 * Fires after rendering overview enrolled courses section in public profile page.
 *
 * @since 2.6.8
 */
do_action( 'masteriyo_after_public_profile_overview_enrolled_courses' );
