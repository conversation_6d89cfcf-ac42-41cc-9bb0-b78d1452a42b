<?php

/**
 * The Template for displaying about section of overview tab in the public profile page.
 *
 * @since 2.6.8
 */

use Masteriyo\Addons\PublicProfile\Svg;

defined( 'ABSPATH' ) || exit; // Exit if accessed directly.

/**
 * Fires before rendering about section in public profile page.
 *
 * @since 2.6.8
 */
do_action( 'masteriyo_before_public_profile_about' );

?>
<div class="masteriyo-col-left--about">
	<h3 class="masteriyo-user--title">
		<?php esc_html_e( 'About', 'learning-management-system' ); ?>
	</h3>

	<p class="masteriyo-user--desc">
		<?php echo esc_html( $data['user_profile']['description'] ); ?>
	</p>

	<div class="masteriyo-user--personal-info">
		<div class="masteriyo-user--personal-info-lists">
			<div class="masteriyo-icon">
				<?php Svg::get( 'email', true ); ?>
			</div>

			<div class="masteriyo-user--details">
				<p><?php echo esc_html( $data['user_profile']['email'] ); ?></p>
			</div>
		</div>

		<?php if ( ! empty( $data['user_profile']['address'] ) ) : ?>
			<div class="masteriyo-user--personal-info-lists">
				<div class="masteriyo-icon">
					<?php Svg::get( 'address', true ); ?>
				</div>

				<div class="masteriyo-user--details">
					<p><?php echo esc_html( $data['user_profile']['address'] ); ?></p>
				</div>
			</div>
		<?php endif; ?>

		<?php if ( ! empty( $data['user_profile']['phone'] ) ) : ?>
			<div class="masteriyo-user--personal-info-lists">
				<div class="masteriyo-icon">
					<?php Svg::get( 'phone', true ); ?>
				</div>

				<div class="masteriyo-user--details">
					<p><?php echo esc_html( $data['user_profile']['phone'] ); ?></p>
				</div>
			</div>
		<?php endif; ?>
	</div>
</div>
<?php

/**
 * Fires after rendering about section in public profile page.
 *
 * @since 2.6.8
 */
do_action( 'masteriyo_after_public_profile_about' );
