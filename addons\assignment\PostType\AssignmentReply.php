<?php
/**
 * Assignment Reply class.
 *
 * @since 2.3.5
 *
 * @package Masteriyo\Addons\AssignmentReply
 */

namespace Masteriyo\Addons\Assignment\PostType;

defined( 'ABSPATH' ) || exit;


use Masteriyo\PostType\PostType;

/**
 * Assignment Reply class.
 */
class AssignmentReply extends PostType {
	/**
	 * Post slug.
	 *
	 * @since 2.3.5
	 *
	 * @var string
	 */
	protected $slug = 'mto-assignment-reply';

	/**
	 * Constructor.
	 */
	public function __construct() {
		$debug      = masteriyo_is_post_type_debug_enabled();
		$permalinks = masteriyo_get_permalink_structure();

		$this->labels = array(
			'name'                  => _x( 'Assignment Replies', 'Assignment Reply General Name', 'learning-management-system' ),
			'singular_name'         => _x( 'Assignment Reply', 'Assignment Reply Singular Name', 'learning-management-system' ),
			'menu_name'             => __( 'Assignment Replies', 'learning-management-system' ),
			'name_admin_bar'        => __( 'Assignment Reply', 'learning-management-system' ),
			'archives'              => __( 'Assignment Reply Archives', 'learning-management-system' ),
			'attributes'            => __( 'Assignment Reply Attributes', 'learning-management-system' ),
			'parent_item_colon'     => __( 'Parent Assignment Reply:', 'learning-management-system' ),
			'all_items'             => __( 'All Assignment Replies', 'learning-management-system' ),
			'add_new_item'          => __( 'Add New Item', 'learning-management-system' ),
			'add_new'               => __( 'Add New', 'learning-management-system' ),
			'new_item'              => __( 'New Assignment Reply', 'learning-management-system' ),
			'edit_item'             => __( 'Edit Assignment Reply', 'learning-management-system' ),
			'update_item'           => __( 'Update Assignment Reply', 'learning-management-system' ),
			'view_item'             => __( 'View Assignment Reply', 'learning-management-system' ),
			'view_items'            => __( 'View Assignment Replies', 'learning-management-system' ),
			'search_items'          => __( 'Search Assignment Reply', 'learning-management-system' ),
			'not_found'             => __( 'Not found', 'learning-management-system' ),
			'not_found_in_trash'    => __( 'Not found in Trash.', 'learning-management-system' ),
			'featured_image'        => __( 'Featured Image', 'learning-management-system' ),
			'set_featured_image'    => __( 'Set featured image', 'learning-management-system' ),
			'remove_featured_image' => __( 'Remove featured image', 'learning-management-system' ),
			'use_featured_image'    => __( 'Use as featured image', 'learning-management-system' ),
			'insert_into_item'      => __( 'Insert into assignment Reply', 'learning-management-system' ),
			'uploaded_to_this_item' => __( 'Uploaded to this assignment Reply', 'learning-management-system' ),
			'items_list'            => __( 'Assignment Replies list', 'learning-management-system' ),
			'items_list_navigation' => __( 'Assignment Replies list navigation', 'learning-management-system' ),
			'filter_items_list'     => __( 'Filter assignment Replies list', 'learning-management-system' ),
		);

		$this->args = array(
			'label'               => __( 'Assignment Replies', 'learning-management-system' ),
			'description'         => __( 'Assignment Replies Description', 'learning-management-system' ),
			'labels'              => $this->labels,
			'supports'            => array( 'title', 'editor', 'author', 'comments', 'custom-fields', 'post-formats' ),
			'taxonomies'          => array(),
			'hierarchical'        => false,
			'menu_position'       => 5,
			'public'              => true,
			'show_ui'             => true,
			'show_in_menu'        => $debug,
			'show_in_admin_bar'   => $debug,
			'show_in_nav_menus'   => $debug,
			'show_in_rest'        => false,
			'has_archive'         => false,
			'map_meta_cap'        => true,
			'capability_type'     => array( 'assignment_reply', 'assignment_replies' ),
			'exclude_from_search' => true,
			'publicly_queryable'  => false,
			'can_export'          => true,
			'delete_with_user'    => true,
			'rewrite'             => isset( $permalinks['assignment_reply_rewrite_slug'] ) ? array(
				'slug'       => $permalinks['assignment_reply_rewrite_slug'],
				'with_front' => false,
				'feeds'      => true,
			) : false,
		);
	}
}
