<?php
/**
 * Masteriyo Assignment Reply Admin Email setting class.
 *
 * @since 2.12.0
 * @package Masteriyo\Addons\Assignment
 */

namespace Masteriyo\Addons\Assignment;

defined( 'ABSPATH' ) || exit;

/**
 * Masteriyo Assignment Reply admin email setting class.
 *
 * @class Masteriyo\Addons\Assignment\AdminEmailSetting
 */

class AdminEmailSetting {
	/**
	 * Setting option name.
	 *
	 * @var string
	 */
	const OPTION_NAME = 'masteriyo_assignment_reply_admin_email_setting';


	/**
	 * Setting data.
	 *
	 * @since 2.12.0
	 *
	 * @var array
	 */
	protected static $data = array(
		'enable'           => false,
		'recipients'       => array(),
		'subject'          => 'A student has made an assignment submission!',
		'from_address'     => '',
		'from_name'        => '',
		'reply_to_address' => '',
		'reply_to_name'    => '',
		'to_address'       => '{admin_email}',
		'content'          => '<p class="email-template--info">Hi {site_title},</p><p class="email-template--info">A student has just made an assignment submission. Here are the details:</p><p><span class="email-text--bold">Name</span>: {student_name} <br /> <span class="email-text--bold">Course</span>: {course_name}  <br /> <span class="email-text--bold">Assignment</span>: {assignment_name}</p><p class"email--template--info">If necessary, please review the assignment submission.</p>{assignment_submission_review_link}<p class="email-template--info">Thanks.<br/>Masteriyo Pro Team</p>',
	);

	/**
	 * Read the settings.
	 *
	 * @since 2.12.0
	 */
	public static function read() {
		$settings   = get_option( self::OPTION_NAME, self::$data );
		self::$data = masteriyo_parse_args( $settings, self::$data );

		return self::$data;
	}

	/**
	 * Return all the settings.
	 *
	 * @since 2.12.0
	 *
	 * @return mixed
	 */
	public static function all() {
		return self::read();
	}

	/**
	 * Return global white field value.
	 *
	 * @since 2.12.0
	 *
	 * @param string $key
	 * @return string|array
	 */
	public static function get( $key ) {
		self::read();

		return masteriyo_array_get( self::$data, $key, null );
	}

	/**
	 * Set field.
	 *
	 * @since 2.12.0
	 *
	 * @param string $key Setting key.
	 * @param mixed $value Setting value.
	 */
	public static function set( $key, $value ) {
		masteriyo_array_set( self::$data, $key, $value );
		self::save();
	}

	/**
	 * Set multiple settings.
	 *
	 * @since 2.12.0
	 *
	 * @param array $args
	 */
	public static function set_props( $args ) {
		self::$data = masteriyo_parse_args( $args, self::$data );
	}

	/**
	 * Save the settings.
	 *
	 * @since 2.12.0
	 */
	public static function save() {
		$fields = array_keys( self::$data );

		foreach ( $fields as $field ) {
			if ( isset( self::$data[ $field ] ) && is_string( self::$data[ $field ] ) && empty( trim( self::$data[ $field ] ) ) ) {
				$default_data = masteriyo_get_default_email_contents()['admin']['new_assignment_submission'];

				if ( isset( $default_data[ $field ] ) ) {
					self::$data[ $field ] = $default_data[ $field ];
				}
			}
		}

		update_option( self::OPTION_NAME, self::$data );
	}

}
