<?php
/**
 * AssignmentReplyRepository class.
 *
 * @since 2.3.5
 *
 * @package Masteriyo\Addons\Assignment
 */

namespace Masteriyo\Addons\Assignment\Repository;

defined( 'ABSPATH' ) || exit;


use Masteriyo\Database\Model;
use Masteriyo\Enums\PostStatus;
use Masteriyo\Repository\AbstractRepository;
use Masteriyo\Repository\RepositoryInterface;

/**
 * AssignmentReplyRepository class.
 *
 * @since 2.3.5
 */
class AssignmentReplyRepository extends AbstractRepository implements RepositoryInterface {

	/**
	 * Data stored in meta keys, but not considered "meta".
	 *
	 * @since 2.3.5
	 * @var array
	 */
	protected $internal_meta_keys = array(
		'earned_points' => '_earned_points',
		'note'          => '_note',
		'attachments'   => '_attachments',
	);

	/**
	 * Create a assignment reply in the database.
	 *
	 * @since 2.3.5
	 *
	 * @param \Masteriyo\Addons\Assignment\Models\AssignmentReply $assignment_reply Assignment object.
	 */
	public function create( Model &$assignment_reply ) {
		if ( ! $assignment_reply->get_created_at( 'edit' ) ) {
			$assignment_reply->set_created_at( current_time( 'mysql', true ) );
		}

		if ( ! $assignment_reply->get_user_id( 'edit' ) ) {
			$assignment_reply->set_user_id( get_current_user_id() );
		}

		$id = wp_insert_post(
			/**
			 * Filters new assignment reply data before creating.
			 *
			 * @since 2.3.5
			 *
			 * @param array $data New assignment reply data.
			 * @param Masteriyo\Addons\Assignment\Models\AssignmentReply $assignment_reply Assignment reply object.
			 */
			apply_filters(
				'masteriyo_new_assignment_reply_data',
				array(
					'post_type'      => 'mto-assignment-reply',
					'post_status'    => $assignment_reply->get_status() ? $assignment_reply->get_status() : PostStatus::PENDING,
					'post_author'    => get_current_user_id(),
					'post_content'   => $assignment_reply->get_answer(),
					'post_parent'    => $assignment_reply->get_parent_id(),
					'comment_status' => 'closed',
					'ping_status'    => 'closed',
					'menu_order'     => $assignment_reply->get_menu_order(),
					'post_date'      => gmdate( 'Y-m-d H:i:s', $assignment_reply->get_created_at( 'edit' )->getOffsetTimestamp() ),
					'post_date_gmt'  => gmdate( 'Y-m-d H:i:s', $assignment_reply->get_created_at( 'edit' )->getTimestamp() ),
				),
				$assignment_reply
			)
		);

		if ( $id && ! is_wp_error( $id ) ) {
			$assignment_reply->set_id( $id );
			$this->update_post_meta( $assignment_reply, true );
			// TODO Invalidate caches.

			$assignment_reply->save_meta_data();
			$assignment_reply->apply_changes();

			/**
			 * Fires after creating a assignment reply.
			 *
			 * @since 2.3.5
			 *
			 * @param integer $id The assignment reply ID.
			 * @param \Masteriyo\Addons\Assignment\Models\AssignmentReply $object The assignment object.
			 */
			do_action( 'masteriyo_new_assignment_reply', $id, $assignment_reply );
		}
	}

	/**
	 * Read a assignment.
	 *
	 * @since 2.3.5
	 *
	 * @param \Masteriyo\Addons\Assignment\Models\AssignmentReply $assignment_reply Assignment object.
	 * @throws \Exception If invalid assignment.
	 */
	public function read( Model &$assignment_reply ) {
		$assignment_reply_post = get_post( $assignment_reply->get_id() );

		if ( ! $assignment_reply->get_id() || ! $assignment_reply_post || 'mto-assignment-reply' !== $assignment_reply_post->post_type ) {
			throw new \Exception( __( 'Invalid assignment reply.', 'learning-management-system' ) );
		}

		$assignment_reply->set_props(
			array(
				'created_at'  => $this->string_to_timestamp( $assignment_reply_post->post_date_gmt ),
				'modified_at' => $this->string_to_timestamp( $assignment_reply_post->post_modified_gmt ),
				'answer'      => $assignment_reply_post->post_content,
				'parent_id'   => $assignment_reply_post->post_parent,
				'menu_order'  => $assignment_reply_post->menu_order,
				'status'      => $assignment_reply_post->post_status,
				'user_id'     => $assignment_reply_post->post_author,
			)
		);

		$this->read_assignment_reply_data( $assignment_reply );
		$this->read_extra_data( $assignment_reply );
		$assignment_reply->set_object_read( true );

		/**
		 * Fires after reading a assignment from database.
		 *
		 * @since 2.3.5
		 *
		 * @param integer $id The assignment ID.
		 * @param \Masteriyo\Addons\Assignment\Models\AssignmentReply $object The assignment object.
		 */
		do_action( 'masteriyo_assignment_reply_read', $assignment_reply->get_id(), $assignment_reply );
	}

	/**
	 * Update a assignment in the database.
	 *
	 * @since 2.3.5
	 *
	 * @param \Masteriyo\Addons\Assignment\Models\AssignmentReply $assignment_reply Assignment object.
	 *
	 * @return void
	 */
	public function update( Model &$assignment_reply ) {
		$changes = $assignment_reply->get_changes();

		$post_data_keys = array(
			'answer',
			'parent_id',
			'menu_order',
			'created_at',
			'status',
			'modified_at',
		);

		// Only update the post when the post data changes.
		if ( array_intersect( $post_data_keys, array_keys( $changes ) ) ) {
			$post_data = array(
				'post_content'   => $assignment_reply->get_answer( 'edit' ),
				'post_parent'    => $assignment_reply->get_parent_id( 'edit' ),
				'comment_status' => 'closed',
				'post_status'    => $assignment_reply->get_status( 'edit' ),
				'menu_order'     => $assignment_reply->get_menu_order( 'edit' ),
				'post_type'      => 'mto-assignment-reply',
			);

			/**
			 * When updating this object, to prevent infinite loops, use $wpdb
			 * to update data, since wp_update_post spawns more calls to the
			 * save_post action.
			 *
			 * This ensures hooks are fired by either WP itself (admin screen save),
			 * or an update purely from CRUD.
			 */
			if ( doing_action( 'save_post' ) ) {
				// TODO Abstract the $wpdb WordPress class.
				$GLOBALS['wpdb']->update( $GLOBALS['wpdb']->posts, $post_data, array( 'ID' => $assignment_reply->get_id() ) );
				clean_post_cache( $assignment_reply->get_id() );
			} else {
				wp_update_post( array_merge( array( 'ID' => $assignment_reply->get_id() ), $post_data ) );
			}
			$assignment_reply->read_meta_data( true ); // Refresh internal meta data, in case things were hooked into `save_post` or another WP hook.
		} else { // Only update post modified time to record this save event.
			$GLOBALS['wpdb']->update(
				$GLOBALS['wpdb']->posts,
				array(
					'post_modified'     => current_time( 'mysql' ),
					'post_modified_gmt' => current_time( 'mysql', true ),
				),
				array(
					'ID' => $assignment_reply->get_id(),
				)
			);
			clean_post_cache( $assignment_reply->get_id() );
		}

		$this->update_post_meta( $assignment_reply );

		$assignment_reply->apply_changes();

		/**
		 * Fires after updating a assignment reply.
		 *
		 * @since 2.3.5
		 *
		 * @param integer $id The assignment reply ID.
		 * @param \Masteriyo\Addons\Assignment\Models\AssignmentReply $object The assignment object.
		 */
		do_action( 'masteriyo_update_assignment_reply', $assignment_reply->get_id(), $assignment_reply );
	}

	/**
	 * Delete a assignment reply from the database.
	 *
	 * @since 2.3.5
	 *
	 * @param \Masteriyo\Addons\Assignment\Models\AssignmentReply $assignment_reply Assignment reply object.
	 * @param array $args   Array of args to pass.alert-danger.
	 */
	public function delete( Model &$assignment_reply, $args = array() ) {
		$id          = $assignment_reply->get_id();
		$object_type = $assignment_reply->get_object_type();

		$args = array_merge(
			array(
				'force_delete' => false,
			),
			$args
		);

		if ( ! $id ) {
			return;
		}

		if ( $args['force_delete'] ) {
			/**
			 * Fires before deleting a assignment reply.
			 *
			 * @since 2.3.5
			 *
			 * @param integer $id The assignment reply ID.
			 * @param \Masteriyo\Addons\Assignment\Models\AssignmentReply $object The assignment reply object.
			 */
			do_action( 'masteriyo_before_delete_' . $object_type, $id, $assignment_reply );

			wp_delete_post( $id, true );
			$assignment_reply->set_id( 0 );

			/**
			 * Fires after deleting a assignment reply.
			 *
			 * @since 2.3.5
			 *
			 * @param integer $id The assignment reply ID.
			 * @param \Masteriyo\Addons\Assignment\Models\AssignmentReply $object The assignment reply object.
			 */
			do_action( 'masteriyo_after_delete_' . $object_type, $id, $assignment_reply );
		} else {
			/**
			 * Fires before moving a assignment reply to trash.
			 *
			 * @since 2.3.5
			 *
			 * @param integer $id The assignment reply ID.
			 * @param \Masteriyo\Addons\Assignment\Models\AssignmentReply $object The assignment reply object.
			 */
			do_action( 'masteriyo_before_trash_' . $object_type, $id, $assignment_reply );

			wp_trash_post( $id );
			$assignment_reply->set_status( PostStatus::TRASH );

			/**
			 * Fires after moving a assignment reply to trash.
			 *
			 * @since 2.3.5
			 *
			 * @param integer $id The assignment reply ID.
			 * @param \Masteriyo\Addons\Assignment\Models\AssignmentReply $object The assignment reply object.
			 */
			do_action( 'masteriyo_after_trash_' . $object_type, $id, $assignment_reply );
		}
	}

	/**
	 * Read assignment reply data. Can be overridden by child classes to load other props.
	 *
	 * @since 2.3.5
	 *
	 * @param \Masteriyo\Addons\Assignment\Models\AssignmentReply $assignment_reply Assignment reply object.
	 */
	protected function read_assignment_reply_data( &$assignment_reply ) {
		$id          = $assignment_reply->get_id();
		$meta_values = $this->read_meta( $assignment_reply );

		$set_props = array();

		$meta_values = array_reduce(
			$meta_values,
			function( $result, $meta_value ) {
				$result[ $meta_value->key ][] = $meta_value->value;
				return $result;
			},
			array()
		);

		foreach ( $this->internal_meta_keys as $prop => $meta_key ) {
			$meta_value         = isset( $meta_values[ $meta_key ][0] ) ? $meta_values[ $meta_key ][0] : null;
			$set_props[ $prop ] = maybe_unserialize( $meta_value ); // get_post_meta only unserializes single values.
		}

		$assignment_reply->set_props( $set_props );
	}

	/**
	 * Read extra data associated with the assignment reply, like button text or assignment URL for external assignments reply.
	 *
	 * @since 2.3.5
	 *
	 * @param \Masteriyo\Addons\Assignment\Models\AssignmentReply $assignment_reply Assignment reply object.
	 */
	protected function read_extra_data( &$assignment_reply ) {
		$meta_values = $this->read_meta( $assignment_reply );

		foreach ( $assignment_reply->get_extra_data_keys() as $key ) {
			$function = 'set_' . $key;

			if ( is_callable( array( $assignment_reply, $function ) )
				&& isset( $meta_values[ '_' . $key ] ) ) {
				$assignment_reply->{$function}( $meta_values[ '_' . $key ] );
			}
		}
	}

	/**
	 * Fetch assignment replies.
	 *
	 * @since 2.3.5
	 *
	 * @param array $query_vars Query vars.
	 * @return \Masteriyo\Addons\Assignment\Models\AssignmentReply[]
	 */
	public function query( $query_vars ) {
		$args = $this->get_wp_query_args( $query_vars );

		if ( ! empty( $args['errors'] ) ) {
			$query = (object) array(
				'posts'         => array(),
				'found_posts'   => 0,
				'max_num_pages' => 0,
			);
		} else {
			$query = new \WP_Query( $args );
		}

		if ( isset( $query_vars['return'] ) && 'objects' === $query_vars['return'] && ! empty( $query->posts ) ) {
			// Prime caches before grabbing objects.
			update_post_caches( $query->posts, array( 'mto-assignment-reply' ) );
		}

		$assignment_replies = ( isset( $query_vars['return'] ) && 'ids' === $query_vars['return'] ) ? $query->posts : array_filter( array_map( 'masteriyo_get_assignment_reply', $query->posts ) );

		if ( isset( $query_vars['paginate'] ) && $query_vars['paginate'] ) {
			return (object) array(
				'assignments'   => $assignment_replies,
				'total'         => $query->found_posts,
				'max_num_pages' => $query->max_num_pages,
			);
		}

		return $assignment_replies;
	}

	/**
	 * Get valid WP_Query args from a AssignmentReplyQuery's query variables.
	 *
	 * @since 2.3.5
	 * @param array $query_vars Query vars from a AssignmentReplyQuery.
	 * @return array
	 */
	protected function get_wp_query_args( $query_vars ) {
		// Map query vars to ones that get_wp_query_args or WP_Query recognize.
		$key_mapping = array(
			'status'        => 'post_status',
			'page'          => 'paged',
			'parent_id'     => 'post_parent',
			'assignment_id' => 'post_parent',
		);

		foreach ( $key_mapping as $query_key => $db_key ) {
			if ( isset( $query_vars[ $query_key ] ) ) {
				$query_vars[ $db_key ] = $query_vars[ $query_key ];
				unset( $query_vars[ $query_key ] );
			}
		}

		$query_vars['post_type'] = 'mto-assignment-reply';

		$wp_query_args = parent::get_wp_query_args( $query_vars );

		if ( ! isset( $wp_query_args['date_query'] ) ) {
			$wp_query_args['date_query'] = array();
		}
		if ( ! isset( $wp_query_args['meta_query'] ) ) {
			$wp_query_args['meta_query'] = array(); // phpcs:ignore WordPress.DB.SlowDBQuery.slow_db_query_meta_query
		}

		// Handle date queries.
		$date_queries = array(
			'created_at'  => 'post_date',
			'modified_at' => 'post_modified',
		);
		foreach ( $date_queries as $query_var_key => $db_key ) {
			if ( isset( $query_vars[ $query_var_key ] ) && '' !== $query_vars[ $query_var_key ] ) {

				// Remove any existing meta queries for the same keys to prevent conflicts.
				$existing_queries = wp_list_pluck( $wp_query_args['meta_query'], 'key', true );
				foreach ( $existing_queries as $query_index => $query_contents ) {
					unset( $wp_query_args['meta_query'][ $query_index ] );
				}

				$wp_query_args = $this->parse_date_for_wp_query( $query_vars[ $query_var_key ], $db_key, $wp_query_args );
			}
		}

		// Handle paginate.
		if ( ! isset( $query_vars['paginate'] ) || ! $query_vars['paginate'] ) {
			$wp_query_args['no_found_rows'] = true;
		}

		// Handle orderby.
		if ( isset( $query_vars['orderby'] ) && 'include' === $query_vars['orderby'] ) {
			$wp_query_args['orderby'] = 'post__in';
		}

		/**
		 * Filters WP Query args for assignment reply post type query.
		 *
		 * @since 2.3.5
		 *
		 * @param array $wp_query_args WP Query args.
		 * @param array $query_vars Query vars.
		 * @param \Masteriyo\Addons\Assignment\Repository\AssignmentReplyRepository $repository Assignment reply repository object.
		 */
		return apply_filters( 'masteriyo_assignment_reply_wp_query_args', $wp_query_args, $query_vars, $this );
	}
}
