<?php
/**
 * Coupons class.
 *
 * @since 2.5.12
 *
 * @package Masteriyo\Addons\Coupons\PostType;
 */

namespace Masteriyo\Addons\Coupons\PostType;

defined( 'ABSPATH' ) || exit;


use Masteriyo\PostType\PostType;

/**
 * Coupons class.
 */
class Coupon extends PostType {
	/**
	 * Post slug.
	 *
	 * @since 2.5.12
	 *
	 * @var string
	 */
	protected $slug = PostType::COUPON;

	/**
	 * Constructor.
	 */
	public function __construct() {
		$debug = masteriyo_is_post_type_debug_enabled();

		$this->labels = array(
			'name'                  => _x( 'Coupons', 'Coupon General Name', 'learning-management-system' ),
			'singular_name'         => _x( 'Coupon', 'Coupon Singular Name', 'learning-management-system' ),
			'menu_name'             => __( 'Coupons', 'learning-management-system' ),
			'name_admin_bar'        => __( 'Coupon', 'learning-management-system' ),
			'archives'              => __( 'Coupon Archives', 'learning-management-system' ),
			'attributes'            => __( 'Coupon Attributes', 'learning-management-system' ),
			'parent_item_colon'     => __( 'Parent Coupon:', 'learning-management-system' ),
			'all_items'             => __( 'All Coupons', 'learning-management-system' ),
			'add_new_item'          => __( 'Add New Item', 'learning-management-system' ),
			'add_new'               => __( 'Add New', 'learning-management-system' ),
			'new_item'              => __( 'New Coupon', 'learning-management-system' ),
			'edit_item'             => __( 'Edit Coupon', 'learning-management-system' ),
			'update_item'           => __( 'Update Coupon', 'learning-management-system' ),
			'view_item'             => __( 'View Coupon', 'learning-management-system' ),
			'view_items'            => __( 'View Coupons', 'learning-management-system' ),
			'search_items'          => __( 'Search Coupon', 'learning-management-system' ),
			'not_found'             => __( 'Not found', 'learning-management-system' ),
			'not_found_in_trash'    => __( 'Not found in Trash.', 'learning-management-system' ),
			'featured_image'        => __( 'Featured Image', 'learning-management-system' ),
			'set_featured_image'    => __( 'Set featured image', 'learning-management-system' ),
			'remove_featured_image' => __( 'Remove featured image', 'learning-management-system' ),
			'use_featured_image'    => __( 'Use as featured image', 'learning-management-system' ),
			'insert_into_item'      => __( 'Insert into coupon', 'learning-management-system' ),
			'uploaded_to_this_item' => __( 'Uploaded to this coupon', 'learning-management-system' ),
			'items_list'            => __( 'Coupons list', 'learning-management-system' ),
			'items_list_navigation' => __( 'Coupons list navigation', 'learning-management-system' ),
			'filter_items_list'     => __( 'Filter coupons list', 'learning-management-system' ),
		);

		$this->args = array(
			'label'               => __( 'Coupons', 'learning-management-system' ),
			'description'         => __( 'Coupons Description', 'learning-management-system' ),
			'labels'              => $this->labels,
			'supports'            => array( 'title', 'editor', 'author', 'custom-fields', 'post-formats' ),
			'taxonomies'          => array(),
			'hierarchical'        => false,
			'public'              => $debug,
			'menu_position'       => 5,
			'show_in_admin_bar'   => $debug,
			'show_in_nav_menus'   => $debug,
			'can_export'          => true,
			'show_in_rest'        => $debug,
			'has_archive'         => true,
			'map_meta_cap'        => true,
			'capability_type'     => array( 'coupon', 'coupons' ),
			'exclude_from_search' => false,
			'publicly_queryable'  => is_admin(),
			'can_export'          => true,
			'delete_with_user'    => true,
		);
	}
}
