<?php

/**
 * The Template for displaying links section of overview tab in the public profile page.
 *
 * @since 2.6.8
 */

use Masteriyo\Addons\PublicProfile\Svg;

defined( 'ABSPATH' ) || exit; // Exit if accessed directly.

/**
 * Fires before rendering links section in public profile page.
 *
 * @since 2.6.8
 */
do_action( 'masteriyo_before_public_profile_links' );
?>
<div class="masteriyo-col-left--links">
	<h3 class="masteriyo-user--title">
		<?php esc_html_e( 'Links', 'learning-management-system' ); ?>
	</h3>
	</h3>

	<div class="masteriyo-user--links">
		<?php if ( ! empty( $data['user_profile']['links']['website_url'] ) ) : ?>
			<a href="<?php echo esc_url_raw( $data['user_profile']['links']['website_url'] ); ?>" class="masteriyo-user--links-list">
				<?php Svg::get( 'website', true ); ?>
			</a>
		<?php endif; ?>

		<?php if ( ! empty( $data['user_profile']['links']['behance_url'] ) ) : ?>
			<a href="<?php echo esc_url_raw( $data['user_profile']['links']['behance_url'] ); ?>" class="masteriyo-user--links-list">
				<?php Svg::get( 'behance', true ); ?>
			</a>
		<?php endif; ?>

		<?php if ( ! empty( $data['user_profile']['links']['linkedin_url'] ) ) : ?>
			<a href="<?php echo esc_url_raw( $data['user_profile']['links']['linkedin_url'] ); ?>" class="masteriyo-user--links-list">
				<?php Svg::get( 'linkedin', true ); ?>
			</a>
		<?php endif; ?>

		<?php if ( ! empty( $data['user_profile']['links']['facebook_url'] ) ) : ?>
			<a href="<?php echo esc_url_raw( $data['user_profile']['links']['facebook_url'] ); ?>" class="masteriyo-user--links-list">
				<?php Svg::get( 'facebook', true ); ?>
			</a>
		<?php endif; ?>
	</div>
</div>
<?php
/**
 * Fires after rendering links section in public profile page.
 *
 * @since 2.6.8
 */
do_action( 'masteriyo_after_public_profile_links' );
