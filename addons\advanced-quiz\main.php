<?php

defined( 'ABSPATH' ) || exit;

/**
 * Addon Name: Advanced Quiz
 * Addon URI: https://masteriyo.com/wordpress-lms/
 * Description: Add more question types and options to quiz builder. Instructor will also have more control in reviewing the quizzes taken.
 * Author: Masteriyo
 * Author URI: https://masteriyo.com
 * Addon Type: core
 * Plan: Starter,Pro,Elite,Growth,Scale
 */

define( 'MASTERIYO_ADVANCED_QUIZ_ADDON_FILE', __FILE__ );
define( 'MASTERIYO_ADVANCED_QUIZ_ADDON_BASENAME', plugin_basename( __FILE__ ) );
define( 'MASTERIYO_ADVANCED_QUIZ_ADDON_DIR', __DIR__ );
define( 'MASTERIYO_ADVANCED_QUIZ_TEMPLATES', __DIR__ . '/templates' );
define( 'MASTERIYO_ADVANCED_QUIZ_ADDON_SLUG', 'advanced-quiz' );

/**
 * Include service providers for advanced quiz.
 */
add_filter(
	'masteriyo_service_providers',
	function( $providers ) {
		return array_merge( $providers, require_once __DIR__ . '/config/providers.php' );
	}
);

/**
 * Initialize Masteriyo Advanced Quiz.
 */
add_action(
	'masteriyo_before_init',
	function() {
		masteriyo( 'addons.advanced-quiz' )->init();
	}
);
