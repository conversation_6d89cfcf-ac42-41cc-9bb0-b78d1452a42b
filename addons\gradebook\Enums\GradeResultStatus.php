<?php
/**
 * Grade result status.
 *
 * @since 2.5.20
 * @package Masteriyo\Addons\Gradebook
 */

namespace Masteriyo\Addons\Gradebook\Enums;

defined( 'ABSPATH' ) || exit;

/**
 * Grade result status enum class.
 *
 * @since 2.5.20
 */
class GradeResultStatus {
	/**
	 * Grade result pending status.
	 *
	 * @since 2.5.20
	 * @var string
	 */
	const PENDING = 'pending';

	/**
	 * Grade result completed status
	 *
	 * @since 2.5.20
	 * @var string
	 */
	const COMPLETED = 'completed';

	/**
	 * Return all Grade result statuses.
	 *
	 * @since 2.5.20
	 *
	 * @return array
	 */
	public static function all() {
		return array_unique(
			/**
			 * Filters Grade result statuses.
			 *
			 * @since 2.5.20
			 *
			 * @param string[] $statuses Grade result statuses.
			 */
			apply_filters(
				'masteriyo_pro_grade_result_statuses',
				array(
					self::PENDING,
					self::COMPLETED,
				)
			)
		);
	}
}
