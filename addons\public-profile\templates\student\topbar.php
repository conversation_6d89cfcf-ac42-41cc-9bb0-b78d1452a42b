<?php

/**
 * The Template for displaying top-bar menu for students in the public profile page.
 *
 * @since 2.6.8
 */

use Masteriyo\Addons\PublicProfile\Svg;
use Masteriyo\Pro\Addons;

defined( 'ABSPATH' ) || exit; // Exit if accessed directly.

/**
 * Fires before rendering topbar section in public profile page.
 *
 * @since 2.6.8
 */
do_action( 'masteriyo_before_public_profile_topbar' );

?>
<div class="masteriyo-col-right--tabbar">
	<a href="javascript:;" class="masteriyo-col-right--tabbar-list active" data-target-tab="masteriyo-overview-main-content">
		<div class="masteriyo-icon">
			<?php Svg::get( 'overview', true ); ?>
		</div>

		<p class="masteriyo-tabbar-title"><?php esc_html_e( 'Overview', 'learning-management-system' ); ?></p>
	</a>

	<a href="javascript:;" class="masteriyo-col-right--tabbar-list" data-target-tab="masteriyo-enrolled-courses-main-content">
		<div class="masteriyo-icon">
			<?php Svg::get( 'enrolled-courses', true ); ?>
		</div>

		<p class="masteriyo-tabbar-title"><?php esc_html_e( 'Enrolled Courses', 'learning-management-system' ); ?></p>
	</a>

	<?php if ( ( new Addons() )->is_active( MASTERIYO_CERTIFICATE_ADDON_SLUG ) ) { ?>

		<a href="javascript:;" class="masteriyo-col-right--tabbar-list" data-target-tab="masteriyo-certificates-main-content">
			<div class="masteriyo-icon">
				<?php Svg::get( 'certificates', true ); ?>
			</div>

			<p class="masteriyo-tabbar-title"><?php esc_html_e( 'Certificates', 'learning-management-system' ); ?></p>
		</a>
	<?php } ?>
</div>
<?php

/**
 * Fires after rendering topbar section in public profile page.
 *
 * @since 2.6.8
 */
do_action( 'masteriyo_after_public_profile_topbar' );
