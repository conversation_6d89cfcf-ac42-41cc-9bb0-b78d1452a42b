(self.webpackChunklearning_management_system_pro=self.webpackChunklearning_management_system_pro||[]).push([[520],{123:(n,t,u)=>{var a,s,r,m,e;function i(n,t,u,r,e,i,o,c,f){n={y:n,mo:t,w:u,d:r,h:e,m:i,s:o,ms:c};return void 0!==f&&(n.decimal=f),n}function o(n){return 2===n?1:2<n&&n<11?2:0}function c(n){return 1===n?0:Math.floor(n)!==n?1:2<=n%10&&n%10<=4&&!(10<n%100&&n%100<20)?2:3}function f(n){return Math.floor(n)!==n?2:5<=n%100&&n%100<=20||5<=n%10&&n%10<=9||n%10==0?0:n%10==1?1:1<n?2:0}function d(n){return 1===n?0:Math.floor(n)!==n?1:2<=n%10&&n%10<=4&&n%100<10?2:3}function l(n){return 1===n||n%10==1&&20<n%100?0:Math.floor(n)!==n||2<=n%10&&20<n%100||2<=n%10&&n%100<10?1:2}function k(n){return n%10==1&&n%100!=11}function g(n,t){return Object.prototype.hasOwnProperty.call(n,t)}function h(n,t,u){var r,e=n.unitName,n=n.unitCount,i=u.spacer,o=u.maxDecimalPoints,c=g(u,"decimal")?u.decimal:g(t,"decimal")?t.decimal:".",f=("digitReplacements"in u?r=u.digitReplacements:"_digitReplacements"in t&&(r=t._digitReplacements),(void 0===o?n:Math.floor(n*Math.pow(10,o))/Math.pow(10,o)).toString());if(t._hideCountIf2&&2===n)i=a="";else if(r)for(var a="",s=0;s<f.length;s++){var m=f[s];a+="."===m?c:r[m]}else a=f.replace(".",c);u=t[e],o="function"==typeof u?u(n):u;return t._numberFirst?o+i+a:a+i+o}function y(n){function f(n,t){n=Math.abs(n);var t=a({},f,t||{}),u=function(n,t){var u,r=t.units,e=t.unitMeasures,i="largest"in t?t.largest:1/0;if(!r.length)return[];var o={},c=n;for(m=0;m<r.length;m++){var f=e[u=r[m]],a=m===r.length-1?c/f:Math.floor(c/f);c-=(o[u]=a)*f}if(t.round){for(var s=i,m=0;m<r.length;m++)if(0!==(a=o[u=r[m]])&&0==--s){for(var d=m+1;d<r.length;d++){var l=r[d];o[u]+=o[l]*e[l]/e[u],o[l]=0}break}for(m=r.length-1;0<=m;m--)if(0!==(a=o[u=r[m]])){var k=Math.round(a);if(o[u]=k,0===m)break;var g=r[m-1],h=e[g],k=Math.floor(k*e[u]/h);if(!k)break;o[g]+=k,o[u]=0}}var y=[];for(m=0;m<r.length&&y.length<i;m++)(a=o[u=r[m]])&&y.push({unitName:u,unitCount:a});return y}(n,t),r=t,e=function(n){var t=[n.language];if(g(n,"fallbacks")){if(!s(n.fallbacks)||!n.fallbacks.length)throw new Error("fallbacks must be an array with at least one element");t=t.concat(n.fallbacks)}for(var u=0;u<t.length;u++){var r=t[u];if(g(n.languages,r))return n.languages[r];if(g(m,r))return m[r]}throw new Error("No language found.")}(r);if(!u.length)return h({unitName:(i=r.units)[i.length-1],unitCount:0},e,r);for(var i=r.conjunction,n=r.serialComma,t=g(r,"delimiter")?r.delimiter:g(e,"delimiter")?e.delimiter:", ",o=[],c=0;c<u.length;c++)o.push(h(u[c],e,r));return i&&1!==u.length?2===u.length?o.join(i):o.slice(0,-1).join(t)+(n?",":"")+i+o.slice(-1):o.join(t)}return a(f,{language:"en",spacer:" ",conjunction:"",serialComma:!0,units:["y","mo","w","d","h","m","s"],languages:{},round:!1,unitMeasures:{y:315576e5,mo:26298e5,w:6048e5,d:864e5,h:36e5,m:6e4,s:1e3,ms:1}},n)}a=Object.assign||function(n){for(var t,u=1;u<arguments.length;u++)for(var r in t=arguments[u])g(t,r)&&(n[r]=t[r]);return n},s=Array.isArray||function(n){return"[object Array]"===Object.prototype.toString.call(n)},r=i(function(n){return 1===n?"χρόνος":"χρόνια"},function(n){return 1===n?"μήνας":"μήνες"},function(n){return 1===n?"εβδομάδα":"εβδομάδες"},function(n){return 1===n?"μέρα":"μέρες"},function(n){return 1===n?"ώρα":"ώρες"},function(n){return 1===n?"λεπτό":"λεπτά"},function(n){return 1===n?"δευτερόλεπτο":"δευτερόλεπτα"},function(n){return(1===n?"χιλιοστό":"χιλιοστά")+" του δευτερολέπτου"},","),m={af:i("jaar",function(n){return"maand"+(1===n?"":"e")},function(n){return 1===n?"week":"weke"},function(n){return 1===n?"dag":"dae"},function(n){return 1===n?"uur":"ure"},function(n){return 1===n?"minuut":"minute"},function(n){return"sekonde"+(1===n?"":"s")},function(n){return"millisekonde"+(1===n?"":"s")},","),am:i("ዓመት","ወር","ሳምንት","ቀን","ሰዓት","ደቂቃ","ሰከንድ","ሚሊሰከንድ"),ar:a(i(function(n){return["سنة","سنتان","سنوات"][o(n)]},function(n){return["شهر","شهران","أشهر"][o(n)]},function(n){return["أسبوع","أسبوعين","أسابيع"][o(n)]},function(n){return["يوم","يومين","أيام"][o(n)]},function(n){return["ساعة","ساعتين","ساعات"][o(n)]},function(n){return["دقيقة","دقيقتان","دقائق"][o(n)]},function(n){return["ثانية","ثانيتان","ثواني"][o(n)]},function(n){return["جزء من الثانية","جزآن من الثانية","أجزاء من الثانية"][o(n)]},","),{delimiter:" ﻭ ",_hideCountIf2:!0,_digitReplacements:["۰","١","٢","٣","٤","٥","٦","٧","٨","٩"]}),bg:i(function(n){return["години","година","години"][f(n)]},function(n){return["месеца","месец","месеца"][f(n)]},function(n){return["седмици","седмица","седмици"][f(n)]},function(n){return["дни","ден","дни"][f(n)]},function(n){return["часа","час","часа"][f(n)]},function(n){return["минути","минута","минути"][f(n)]},function(n){return["секунди","секунда","секунди"][f(n)]},function(n){return["милисекунди","милисекунда","милисекунди"][f(n)]},","),bn:i("বছর","মাস","সপ্তাহ","দিন","ঘন্টা","মিনিট","সেকেন্ড","মিলিসেকেন্ড"),ca:i(function(n){return"any"+(1===n?"":"s")},function(n){return"mes"+(1===n?"":"os")},function(n){return"setman"+(1===n?"a":"es")},function(n){return"di"+(1===n?"a":"es")},function(n){return"hor"+(1===n?"a":"es")},function(n){return"minut"+(1===n?"":"s")},function(n){return"segon"+(1===n?"":"s")},function(n){return"milisegon"+(1===n?"":"s")},","),ckb:i("ساڵ","مانگ","هەفتە","ڕۆژ","کاژێر","خولەک","چرکە","میلی چرکە","."),cs:i(function(n){return["rok","roku","roky","let"][d(n)]},function(n){return["měsíc","měsíce","měsíce","měsíců"][d(n)]},function(n){return["týden","týdne","týdny","týdnů"][d(n)]},function(n){return["den","dne","dny","dní"][d(n)]},function(n){return["hodina","hodiny","hodiny","hodin"][d(n)]},function(n){return["minuta","minuty","minuty","minut"][d(n)]},function(n){return["sekunda","sekundy","sekundy","sekund"][d(n)]},function(n){return["milisekunda","milisekundy","milisekundy","milisekund"][d(n)]},","),cy:i("flwyddyn","mis","wythnos","diwrnod","awr","munud","eiliad","milieiliad"),da:i("år",function(n){return"måned"+(1===n?"":"er")},function(n){return"uge"+(1===n?"":"r")},function(n){return"dag"+(1===n?"":"e")},function(n){return"time"+(1===n?"":"r")},function(n){return"minut"+(1===n?"":"ter")},function(n){return"sekund"+(1===n?"":"er")},function(n){return"millisekund"+(1===n?"":"er")},","),de:i(function(n){return"Jahr"+(1===n?"":"e")},function(n){return"Monat"+(1===n?"":"e")},function(n){return"Woche"+(1===n?"":"n")},function(n){return"Tag"+(1===n?"":"e")},function(n){return"Stunde"+(1===n?"":"n")},function(n){return"Minute"+(1===n?"":"n")},function(n){return"Sekunde"+(1===n?"":"n")},function(n){return"Millisekunde"+(1===n?"":"n")},","),el:r,en:i(function(n){return"year"+(1===n?"":"s")},function(n){return"month"+(1===n?"":"s")},function(n){return"week"+(1===n?"":"s")},function(n){return"day"+(1===n?"":"s")},function(n){return"hour"+(1===n?"":"s")},function(n){return"minute"+(1===n?"":"s")},function(n){return"second"+(1===n?"":"s")},function(n){return"millisecond"+(1===n?"":"s")}),eo:i(function(n){return"jaro"+(1===n?"":"j")},function(n){return"monato"+(1===n?"":"j")},function(n){return"semajno"+(1===n?"":"j")},function(n){return"tago"+(1===n?"":"j")},function(n){return"horo"+(1===n?"":"j")},function(n){return"minuto"+(1===n?"":"j")},function(n){return"sekundo"+(1===n?"":"j")},function(n){return"milisekundo"+(1===n?"":"j")},","),es:i(function(n){return"año"+(1===n?"":"s")},function(n){return"mes"+(1===n?"":"es")},function(n){return"semana"+(1===n?"":"s")},function(n){return"día"+(1===n?"":"s")},function(n){return"hora"+(1===n?"":"s")},function(n){return"minuto"+(1===n?"":"s")},function(n){return"segundo"+(1===n?"":"s")},function(n){return"milisegundo"+(1===n?"":"s")},","),et:i(function(n){return"aasta"+(1===n?"":"t")},function(n){return"kuu"+(1===n?"":"d")},function(n){return"nädal"+(1===n?"":"at")},function(n){return"päev"+(1===n?"":"a")},function(n){return"tund"+(1===n?"":"i")},function(n){return"minut"+(1===n?"":"it")},function(n){return"sekund"+(1===n?"":"it")},function(n){return"millisekund"+(1===n?"":"it")},","),eu:i("urte","hilabete","aste","egun","ordu","minutu","segundo","milisegundo",","),fa:i("سال","ماه","هفته","روز","ساعت","دقیقه","ثانیه","میلی ثانیه"),fi:i(function(n){return 1===n?"vuosi":"vuotta"},function(n){return 1===n?"kuukausi":"kuukautta"},function(n){return"viikko"+(1===n?"":"a")},function(n){return"päivä"+(1===n?"":"ä")},function(n){return"tunti"+(1===n?"":"a")},function(n){return"minuutti"+(1===n?"":"a")},function(n){return"sekunti"+(1===n?"":"a")},function(n){return"millisekunti"+(1===n?"":"a")},","),fo:i("ár",function(n){return 1===n?"mánaður":"mánaðir"},function(n){return 1===n?"vika":"vikur"},function(n){return 1===n?"dagur":"dagar"},function(n){return 1===n?"tími":"tímar"},function(n){return 1===n?"minuttur":"minuttir"},"sekund","millisekund",","),fr:i(function(n){return"an"+(2<=n?"s":"")},"mois",function(n){return"semaine"+(2<=n?"s":"")},function(n){return"jour"+(2<=n?"s":"")},function(n){return"heure"+(2<=n?"s":"")},function(n){return"minute"+(2<=n?"s":"")},function(n){return"seconde"+(2<=n?"s":"")},function(n){return"milliseconde"+(2<=n?"s":"")},","),gr:r,he:i(function(n){return 1===n?"שנה":"שנים"},function(n){return 1===n?"חודש":"חודשים"},function(n){return 1===n?"שבוע":"שבועות"},function(n){return 1===n?"יום":"ימים"},function(n){return 1===n?"שעה":"שעות"},function(n){return 1===n?"דקה":"דקות"},function(n){return 1===n?"שניה":"שניות"},function(n){return 1===n?"מילישנייה":"מילישניות"}),hr:i(function(n){return n%10==2||n%10==3||n%10==4?"godine":"godina"},function(n){return 1===n?"mjesec":2===n||3===n||4===n?"mjeseca":"mjeseci"},function(n){return n%10==1&&11!==n?"tjedan":"tjedna"},function(n){return 1===n?"dan":"dana"},function(n){return 1===n?"sat":2===n||3===n||4===n?"sata":"sati"},function(n){var t=n%10;return 2!=t&&3!=t&&4!=t||!(n<10||14<n)?"minuta":"minute"},function(n){var t=n%10;return 5==t||Math.floor(n)===n&&10<=n&&n<=19?"sekundi":1==t?"sekunda":2==t||3==t||4==t?"sekunde":"sekundi"},function(n){return 1===n?"milisekunda":n%10==2||n%10==3||n%10==4?"milisekunde":"milisekundi"},","),hi:i("साल",function(n){return 1===n?"महीना":"महीने"},function(n){return 1===n?"हफ़्ता":"हफ्ते"},"दिन",function(n){return 1===n?"घंटा":"घंटे"},"मिनट","सेकंड","मिलीसेकंड"),hu:i("év","hónap","hét","nap","óra","perc","másodperc","ezredmásodperc",","),id:i("tahun","bulan","minggu","hari","jam","menit","detik","milidetik"),is:i("ár",function(n){return"mánuð"+(1===n?"ur":"ir")},function(n){return"vik"+(1===n?"a":"ur")},function(n){return"dag"+(1===n?"ur":"ar")},function(n){return"klukkutím"+(1===n?"i":"ar")},function(n){return"mínút"+(1===n?"a":"ur")},function(n){return"sekúnd"+(1===n?"a":"ur")},function(n){return"millisekúnd"+(1===n?"a":"ur")}),it:i(function(n){return"ann"+(1===n?"o":"i")},function(n){return"mes"+(1===n?"e":"i")},function(n){return"settiman"+(1===n?"a":"e")},function(n){return"giorn"+(1===n?"o":"i")},function(n){return"or"+(1===n?"a":"e")},function(n){return"minut"+(1===n?"o":"i")},function(n){return"second"+(1===n?"o":"i")},function(n){return"millisecond"+(1===n?"o":"i")},","),ja:i("年","ヶ月","週","日","時間","分","秒","ミリ秒"),km:i("ឆ្នាំ","ខែ","សប្តាហ៍","ថ្ងៃ","ម៉ោង","នាទី","វិនាទី","មិល្លីវិនាទី"),kn:i(function(n){return 1===n?"ವರ್ಷ":"ವರ್ಷಗಳು"},function(n){return 1===n?"ತಿಂಗಳು":"ತಿಂಗಳುಗಳು"},function(n){return 1===n?"ವಾರ":"ವಾರಗಳು"},function(n){return 1===n?"ದಿನ":"ದಿನಗಳು"},function(n){return 1===n?"ಗಂಟೆ":"ಗಂಟೆಗಳು"},function(n){return 1===n?"ನಿಮಿಷ":"ನಿಮಿಷಗಳು"},function(n){return 1===n?"ಸೆಕೆಂಡ್":"ಸೆಕೆಂಡುಗಳು"},function(n){return 1===n?"ಮಿಲಿಸೆಕೆಂಡ್":"ಮಿಲಿಸೆಕೆಂಡುಗಳು"}),ko:i("년","개월","주일","일","시간","분","초","밀리 초"),ku:i("sal","meh","hefte","roj","seet","deqe","saniye","mîlîçirk",","),lo:i("ປີ","ເດືອນ","ອາທິດ","ມື້","ຊົ່ວໂມງ","ນາທີ","ວິນາທີ","ມິນລິວິນາທີ",","),lt:i(function(n){return n%10==0||10<=n%100&&n%100<=20?"metų":"metai"},function(n){return["mėnuo","mėnesiai","mėnesių"][l(n)]},function(n){return["savaitė","savaitės","savaičių"][l(n)]},function(n){return["diena","dienos","dienų"][l(n)]},function(n){return["valanda","valandos","valandų"][l(n)]},function(n){return["minutė","minutės","minučių"][l(n)]},function(n){return["sekundė","sekundės","sekundžių"][l(n)]},function(n){return["milisekundė","milisekundės","milisekundžių"][l(n)]},","),lv:i(function(n){return k(n)?"gads":"gadi"},function(n){return k(n)?"mēnesis":"mēneši"},function(n){return k(n)?"nedēļa":"nedēļas"},function(n){return k(n)?"diena":"dienas"},function(n){return k(n)?"stunda":"stundas"},function(n){return k(n)?"minūte":"minūtes"},function(n){return k(n)?"sekunde":"sekundes"},function(n){return k(n)?"milisekunde":"milisekundes"},","),mk:i(function(n){return 1===n?"година":"години"},function(n){return 1===n?"месец":"месеци"},function(n){return 1===n?"недела":"недели"},function(n){return 1===n?"ден":"дена"},function(n){return 1===n?"час":"часа"},function(n){return 1===n?"минута":"минути"},function(n){return 1===n?"секунда":"секунди"},function(n){return 1===n?"милисекунда":"милисекунди"},","),mn:i("жил","сар","долоо хоног","өдөр","цаг","минут","секунд","миллисекунд"),mr:i(function(n){return 1===n?"वर्ष":"वर्षे"},function(n){return 1===n?"महिना":"महिने"},function(n){return 1===n?"आठवडा":"आठवडे"},"दिवस","तास",function(n){return 1===n?"मिनिट":"मिनिटे"},"सेकंद","मिलिसेकंद"),ms:i("tahun","bulan","minggu","hari","jam","minit","saat","milisaat"),nl:i("jaar",function(n){return 1===n?"maand":"maanden"},function(n){return 1===n?"week":"weken"},function(n){return 1===n?"dag":"dagen"},"uur",function(n){return 1===n?"minuut":"minuten"},function(n){return 1===n?"seconde":"seconden"},function(n){return 1===n?"milliseconde":"milliseconden"},","),no:i("år",function(n){return"måned"+(1===n?"":"er")},function(n){return"uke"+(1===n?"":"r")},function(n){return"dag"+(1===n?"":"er")},function(n){return"time"+(1===n?"":"r")},function(n){return"minutt"+(1===n?"":"er")},function(n){return"sekund"+(1===n?"":"er")},function(n){return"millisekund"+(1===n?"":"er")},","),pl:i(function(n){return["rok","roku","lata","lat"][c(n)]},function(n){return["miesiąc","miesiąca","miesiące","miesięcy"][c(n)]},function(n){return["tydzień","tygodnia","tygodnie","tygodni"][c(n)]},function(n){return["dzień","dnia","dni","dni"][c(n)]},function(n){return["godzina","godziny","godziny","godzin"][c(n)]},function(n){return["minuta","minuty","minuty","minut"][c(n)]},function(n){return["sekunda","sekundy","sekundy","sekund"][c(n)]},function(n){return["milisekunda","milisekundy","milisekundy","milisekund"][c(n)]},","),pt:i(function(n){return"ano"+(1===n?"":"s")},function(n){return 1===n?"mês":"meses"},function(n){return"semana"+(1===n?"":"s")},function(n){return"dia"+(1===n?"":"s")},function(n){return"hora"+(1===n?"":"s")},function(n){return"minuto"+(1===n?"":"s")},function(n){return"segundo"+(1===n?"":"s")},function(n){return"milissegundo"+(1===n?"":"s")},","),ro:i(function(n){return 1===n?"an":"ani"},function(n){return 1===n?"lună":"luni"},function(n){return 1===n?"săptămână":"săptămâni"},function(n){return 1===n?"zi":"zile"},function(n){return 1===n?"oră":"ore"},function(n){return 1===n?"minut":"minute"},function(n){return 1===n?"secundă":"secunde"},function(n){return 1===n?"milisecundă":"milisecunde"},","),ru:i(function(n){return["лет","год","года"][f(n)]},function(n){return["месяцев","месяц","месяца"][f(n)]},function(n){return["недель","неделя","недели"][f(n)]},function(n){return["дней","день","дня"][f(n)]},function(n){return["часов","час","часа"][f(n)]},function(n){return["минут","минута","минуты"][f(n)]},function(n){return["секунд","секунда","секунды"][f(n)]},function(n){return["миллисекунд","миллисекунда","миллисекунды"][f(n)]},","),sq:i(function(n){return 1===n?"vit":"vjet"},"muaj","javë","ditë","orë",function(n){return"minut"+(1===n?"ë":"a")},function(n){return"sekond"+(1===n?"ë":"a")},function(n){return"milisekond"+(1===n?"ë":"a")},","),sr:i(function(n){return["години","година","године"][f(n)]},function(n){return["месеци","месец","месеца"][f(n)]},function(n){return["недељи","недеља","недеље"][f(n)]},function(n){return["дани","дан","дана"][f(n)]},function(n){return["сати","сат","сата"][f(n)]},function(n){return["минута","минут","минута"][f(n)]},function(n){return["секунди","секунда","секунде"][f(n)]},function(n){return["милисекунди","милисекунда","милисекунде"][f(n)]},","),ta:i(function(n){return 1===n?"வருடம்":"ஆண்டுகள்"},function(n){return 1===n?"மாதம்":"மாதங்கள்"},function(n){return 1===n?"வாரம்":"வாரங்கள்"},function(n){return 1===n?"நாள்":"நாட்கள்"},function(n){return 1===n?"மணி":"மணிநேரம்"},function(n){return"நிமிட"+(1===n?"ம்":"ங்கள்")},function(n){return"வினாடி"+(1===n?"":"கள்")},function(n){return"மில்லி விநாடி"+(1===n?"":"கள்")}),te:i(function(n){return"సంవత్స"+(1===n?"రం":"రాల")},function(n){return"నెల"+(1===n?"":"ల")},function(n){return 1===n?"వారం":"వారాలు"},function(n){return"రోజు"+(1===n?"":"లు")},function(n){return"గంట"+(1===n?"":"లు")},function(n){return 1===n?"నిమిషం":"నిమిషాలు"},function(n){return 1===n?"సెకను":"సెకన్లు"},function(n){return 1===n?"మిల్లీసెకన్":"మిల్లీసెకన్లు"}),uk:i(function(n){return["років","рік","роки"][f(n)]},function(n){return["місяців","місяць","місяці"][f(n)]},function(n){return["тижнів","тиждень","тижні"][f(n)]},function(n){return["днів","день","дні"][f(n)]},function(n){return["годин","година","години"][f(n)]},function(n){return["хвилин","хвилина","хвилини"][f(n)]},function(n){return["секунд","секунда","секунди"][f(n)]},function(n){return["мілісекунд","мілісекунда","мілісекунди"][f(n)]},","),ur:i("سال",function(n){return 1===n?"مہینہ":"مہینے"},function(n){return 1===n?"ہفتہ":"ہفتے"},"دن",function(n){return 1===n?"گھنٹہ":"گھنٹے"},"منٹ","سیکنڈ","ملی سیکنڈ"),sk:i(function(n){return["rok","roky","roky","rokov"][d(n)]},function(n){return["mesiac","mesiace","mesiace","mesiacov"][d(n)]},function(n){return["týždeň","týždne","týždne","týždňov"][d(n)]},function(n){return["deň","dni","dni","dní"][d(n)]},function(n){return["hodina","hodiny","hodiny","hodín"][d(n)]},function(n){return["minúta","minúty","minúty","minút"][d(n)]},function(n){return["sekunda","sekundy","sekundy","sekúnd"][d(n)]},function(n){return["milisekunda","milisekundy","milisekundy","milisekúnd"][d(n)]},","),sl:i(function(n){return n%10==1?"leto":n%100==2?"leti":n%100==3||n%100==4||Math.floor(n)!==n&&n%100<=5?"leta":"let"},function(n){return n%10==1?"mesec":n%100==2||Math.floor(n)!==n&&n%100<=5?"meseca":n%10==3||n%10==4?"mesece":"mesecev"},function(n){return n%10==1?"teden":n%10==2||Math.floor(n)!==n&&n%100<=4?"tedna":n%10==3||n%10==4?"tedne":"tednov"},function(n){return n%100==1?"dan":"dni"},function(n){return n%10==1?"ura":n%100==2?"uri":n%10==3||n%10==4||Math.floor(n)!==n?"ure":"ur"},function(n){return n%10==1?"minuta":n%10==2?"minuti":n%10==3||n%10==4||Math.floor(n)!==n&&n%100<=4?"minute":"minut"},function(n){return n%10==1?"sekunda":n%100==2?"sekundi":n%100==3||n%100==4||Math.floor(n)!==n?"sekunde":"sekund"},function(n){return n%10==1?"milisekunda":n%100==2?"milisekundi":n%100==3||n%100==4||Math.floor(n)!==n?"milisekunde":"milisekund"},","),sv:i("år",function(n){return"månad"+(1===n?"":"er")},function(n){return"veck"+(1===n?"a":"or")},function(n){return"dag"+(1===n?"":"ar")},function(n){return"timm"+(1===n?"e":"ar")},function(n){return"minut"+(1===n?"":"er")},function(n){return"sekund"+(1===n?"":"er")},function(n){return"millisekund"+(1===n?"":"er")},","),sw:a(i(function(n){return 1===n?"mwaka":"miaka"},function(n){return 1===n?"mwezi":"miezi"},"wiki",function(n){return 1===n?"siku":"masiku"},function(n){return 1===n?"saa":"masaa"},"dakika","sekunde","milisekunde"),{_numberFirst:!0}),tr:i("yıl","ay","hafta","gün","saat","dakika","saniye","milisaniye",","),th:i("ปี","เดือน","สัปดาห์","วัน","ชั่วโมง","นาที","วินาที","มิลลิวินาที"),uz:i("yil","oy","hafta","kun","soat","minut","sekund","millisekund"),uz_CYR:i("йил","ой","ҳафта","кун","соат","минут","секунд","миллисекунд"),vi:i("năm","tháng","tuần","ngày","giờ","phút","giây","mili giây",","),zh_CN:i("年","个月","周","天","小时","分钟","秒","毫秒"),zh_TW:i("年","個月","周","天","小時","分鐘","秒","毫秒")},e=a(y({}),{getSupportedLanguages:function(){var n,t=[];for(n in m)g(m,n)&&"gr"!==n&&t.push(n);return t},humanizer:y}),void 0!==(r=function(){return e}.call(t,u,t,n))&&(n.exports=r)},705:(n,t,u)=>{function e(n){return(e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}function r(n,t,u){return r=function(n){if("object"!=e(n)||!n)return n;var t=n[Symbol.toPrimitive];if(void 0===t)return String(n);t=t.call(n,"string");if("object"!=e(t))return t;throw new TypeError("@@toPrimitive must return a primitive value.")}(r=t),(t="symbol"==e(r)?r:String(r))in n?Object.defineProperty(n,t,{value:u,enumerable:!0,configurable:!0,writable:!0}):n[t]=u,n;var r}u.d(t,{A:()=>r})}}]);