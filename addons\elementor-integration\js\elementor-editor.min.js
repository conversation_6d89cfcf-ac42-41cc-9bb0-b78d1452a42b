!function(e,t){var o={currentDocType:t.is_elementor_template?elementor.config.document.type:"",docTypes:{courseArchivePage:"masteriyo-course-archive-page",singleCoursePage:"masteriyo-single-course-page"},init:function(){o.initElementorComponents(),o.initDocumentLoadHandler()},initElementorComponents:function(){$e.components.register(new l)},initDocumentLoadHandler:function(){elementor.on("document:loaded",e=>{o.maybeOpenLibraryModal(),o.initLibraryModalOpenBtn()})},initLibraryModalOpenBtn:function(){var e;o.isMasteriyoDocumentType()&&(e=window["elementor-preview-iframe"].contentWindow)&&!e.jQuery(".masteriyo-templates-button").length&&o.addLibraryModalOpenBtn(e)},addLibraryModalOpenBtn:function(e){e.jQuery(".elementor-add-template-button").after(t.library_btn_template),e.jQuery(e.document.body).on("click",".masteriyo-templates-button",function(){o.openLibraryModal()})},openLibraryModal:function(){$e.components.components["masteriyo-library"].open()},maybeOpenLibraryModal:function(){o.isMasteriyoDocumentType()&&o.isEmptyDocument()&&o.openLibraryModal()},isMasteriyoDocumentType:function(){return Object.values(o.docTypes).includes(o.currentDocType)},isEmptyDocument:function(){return!elementor.config.document.elements?.length},importWidgetsTemplate:function(e){var o=elementor.getPreviewContainer(),r=void 0;e.forEach(e=>{var t;e.isInner&&(t=$e.run("document/elements/create",{container:o,model:{elType:"section"},columns:1,options:{at:r,edit:!1}}),o=t.view.children.findByIndex(0).getContainer()),$e.run("document/elements/create",{containers:[o],model:e,options:{at:r,clone:!0,edit:!1}})})}};class r extends Marionette.ItemView{getTemplate(){return"#tmpl-masteriyo-templates-modal__header__logo"}className(){return"elementor-templates-modal__header__logo"}events(){return{click:"onClick"}}templateHelpers(){return{title:this.getOption("title")}}onClick(){var e=this.getOption("click");e&&e()}}var n=Marionette.ItemView.extend({template:"#tmpl-masteriyo-template-library-header-actions",id:"masteriyo-template-library-header-actions",ui:{import:"#masteriyo-template-library-header-import"},events:{"click @ui.import":"onImportClick"},onImportClick(){o.currentDocType===o.docTypes.courseArchivePage?o.importWidgetsTemplate(t.page_templates.course_archive_page):o.currentDocType===o.docTypes.singleCoursePage&&o.importWidgetsTemplate(t.page_templates.single_course_page),e(".elementor-templates-modal__header__close").click()}}),i=Marionette.ItemView.extend({template:"#tmpl-masteriyo-single-course-page-preview",id:"masteriyo-single-course-page-preview"}),a=Marionette.ItemView.extend({template:"#tmpl-masteriyo-course-archive-page-preview",id:"masteriyo-course-archive-page-preview"}),s=elementorModules.common.views.modal.Layout.extend({getModalOptions(){return{id:"masteriyo-template-library-modal"}},showLogo(){this.getHeaderView().logoArea.show(new r(this.getLogoOptions()))},getLogoOptions(){return{title:"Import default layout?"}},setHeaderDefaultParts(){this.getHeaderView().tools.show(new n),this.showLogo()},showPreviewView(){o.currentDocType===o.docTypes.singleCoursePage?this.modalContent.show(new i):o.currentDocType===o.docTypes.courseArchivePage&&this.modalContent.show(new a)}});class l extends elementorCommon.api.modules.ComponentModalBase{__construct(e){super.__construct(e),$e.data.deleteCache(this,"masteriyo-library")}getNamespace(){return"masteriyo-library"}open(){return super.open(),this.layout.setHeaderDefaultParts(),this.layout.showPreviewView(),!0}close(){return!!super.close()&&(this.manager.modalConfig={},!0)}getModalLayout(){return s}}o.init()}(jQuery,_MASTERIYO_ELEMENTOR_EDITOR_);