<?php
/**
 * MultipleInstructors service provider.
 *
 * @since 2.3.2
 * @package \Masteriyo\Addons\MultipleInstructors
 */

namespace Masteriyo\Addons\MultipleInstructors\Providers;

defined( 'ABSPATH' ) || exit;

use Masteriyo\Addons\MultipleInstructors\MultipleInstructorsAddon;
use Masteriyo\Addons\MultipleInstructors\Models\MultipleInstructors;
use League\Container\ServiceProvider\AbstractServiceProvider;

/**
 * MultipleInstructors service provider.
 *
 * @since 2.3.2
 */
class MultipleInstructorsServiceProvider extends AbstractServiceProvider {
	/**
	 * The provided array is a way to let the container
	 * know that a service is provided by this service
	 * provider. Every service that is registered via
	 * this service provider must have an alias added
	 * to this array or it will be ignored
	 *
	 * @since 2.3.2
	 *
	 * @var array
	 */
	protected $provides = array(
		'addons.multiple-instructors',
		'\Masteriyo\Addons\MultipleInstructors\MultipleInstructorsAddon',
	);

	/**
	 * This is where the magic happens, within the method you can
	 * access the container and register or retrieve anything
	 * that you need to, but remember, every alias registered
	 * within this method must be declared in the `$provides` array.
	 *
	 * @since 2.3.2
	 */
	public function register() {
		$this->getContainer()->add( 'addons.multiple-instructors', MultipleInstructorsAddon::class, true );
	}
}
