<?php

/**
 * The Template for student or instructor public profile page.
 *
 * This template can be overridden by copying it to yourtheme/masteriyo/public-profile/public-profile.php.
 *
 * HOWEVER, on occasion Masteriyo will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @since 2.6.8
 * @version 2.6.8
 *
 * @package Masteriyo\Pro\Addons\PublicProfile\Templates
 */

defined( 'ABSPATH' ) || exit; // Exit if accessed directly.

masteriyo_custom_header( 'public-profile' );

/**
 * Wrapper div opening.
 *
 * @since 2.6.8
 */
echo '<div class="masteriyo-w-100 masteriyo-container">';

/**
 * Fires before rendering public profile main content.
 *
 * @since 2.6.8
 *
 * @param array $data Public profile data.
 */
do_action( 'masteriyo_before_public_profile_content', $data );
?>
<section id="masteriyo" class="masteriyo">
	<div class="container">
		<div class="masteriyo-row">
			<!-- Left -->
			<div class="masteriyo-col-left">
				<div class="masteriyo-col-left-wrapper">
					<?php
					/**
					 * Fires after rendering the left main content of the public profile.
					 *
					 * @since 2.6.8
					 *
					 * @param array $data Public profile data.
					 */
					do_action( 'masteriyo_public_profile_left_main_content', $data );
					?>

					<!-- Info -->

					<!-- About -->

					<!-- Links -->

					<!-- Joined -->
				</div>
			</div>
			<!-- Right -->
			<div class="masteriyo-col-right">
				<?php
				/**
				 * Fires after rendering the right main content of the public profile.
				 *
				 * @since 2.6.8
				 *
				 * @param array $data Public profile data.
				 */
				do_action( 'masteriyo_public_profile_right_main_content', $data );
				?>
				<!-- Top-bar -->

				<div id="masteriyo-overview-main-content" class="masteriyo-col-right--tab-content">
					<?php
					/**
					 * Fires after rendering the overview main content of the public profile.
					 *
					 * @since 2.6.8
					 *
					 * @param array $data Public profile data.
					 */
					do_action( 'masteriyo_public_profile_overview_main_content', $data );
					?>
					<!-- cards -->

					<!-- Courses -->

					<!-- Enrolled Courses -->
				</div>
				<input type="hidden" name='masteriyo_public_profile_user_id' value="<?php echo esc_attr( $data['user_profile']['id'] ); ?>">
				<div id="masteriyo-courses-offered-main-content" class="masteriyo-col-right--tab-content hidden">
					<?php
					/**
					 * Fires after rendering the courses offered main content of the public profile.
					 *
					 * @since 2.6.8
					 *
					 * @param array $data Public profile data.
					 */
					do_action( 'masteriyo_public_profile_courses_offered_main_content', $data );
					?>
				</div>

				<div id="masteriyo-enrolled-courses-main-content" class="masteriyo-col-right--tab-content hidden">
					<?php
					/**
					 * Fires after rendering the enrolled courses main content of the public profile.
					 *
					 * @since 2.6.8
					 *
					 * @param array $data Public profile data.
					 */
					do_action( 'masteriyo_public_profile_enrolled_courses_main_content', $data );
					?>
				</div>

				<div id="masteriyo-certificates-main-content" class="masteriyo-col-right--tab-content hidden">
					<?php
					/**
					 * Fires after rendering the certificates main content of the public profile.
					 *
					 * @since 2.6.8
					 *
					 * @param array $data Public profile data.
					 */
					do_action( 'masteriyo_public_profile_certificates_main_content', $data );
					?>
				</div>
			</div>
		</div>
	</div>
</section>
<?php

/**
 * Fires after rendering public profile main content.
 *
 * @since 2.6.8
 *
 * @param array $data Public profile data.
 */
do_action( 'masteriyo_after_public_profile_main_content', $data );

/**
 * Wrapper div closing.
 *
 * @since 2.6.8
 */
echo '</div>';

masteriyo_custom_footer( 'public-profile' );
