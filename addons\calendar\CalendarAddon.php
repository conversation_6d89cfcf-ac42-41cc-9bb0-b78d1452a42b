<?php
/**
 * Masteriyo Calendar setup.
 *
 * @package Masteriyo\Addons\Calendar
 *
 * @since 2.8.3
 */
namespace Masteriyo\Addons\Calendar;

use Masteriyo\Addons\Calendar\RestApi\CalendarController;

defined( 'ABSPATH' ) || exit;
/**
 * Main Masteriyo Calendar class.
 *
 * @class Masteriyo\Addons\Calendar
 */
class CalendarAddon {
	/**
	 * Instance
	 *
	 * @since 2.8.3
	 *
	 * @var \Masteriyo\Addons\Calendar\CalendarAddon
	 */
	protected static $instance = null;

	/**
	 * Constructor.
	 *
	 * @since 2.8.3
	 */
	private function __construct() {
	}

	/**
	 * Return the instance.
	 *
	 * @since 2.8.3
	 *
	 * @return \Masteriyo\Addons\Calendar\CalendarAddon
	 */
	public static function instance() {
		if ( null === self::$instance ) {
			self::$instance = new self();
		}

		return self::$instance;
	}

	/**
	 * Initialize module.
	 *
	 * @since 2.8.3
	 */
	public function init() {
		$this->init_hooks();
	}

	/**
	 * Initialize hooks.
	 *
	 * @since 2.8.3
	 */
	public function init_hooks() {
		add_filter( 'masteriyo_rest_api_get_rest_namespaces', array( $this, 'register_rest_namespaces' ) );
	}

	/**
	 * Register namespaces.
	 *
	 * @since 2.8.3
	 *
	 * @param array $namespaces
	 * @return array
	 */
	public function register_rest_namespaces( $namespaces ) {
		$namespaces['masteriyo/pro/v1']['calendar'] = CalendarController::class;
		return $namespaces;
	}
}
