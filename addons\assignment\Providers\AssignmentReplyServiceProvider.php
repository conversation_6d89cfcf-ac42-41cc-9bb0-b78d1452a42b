<?php
/**
 * AssignmentReply service provider.
 *
 * @since 2.3.5
 * @package \Masteriyo\Addons\Assignment
 */

namespace Masteriyo\Addons\Assignment\Providers;

defined( 'ABSPATH' ) || exit;

use Masteriyo\Addons\Assignment\Models\AssignmentReply;
use League\Container\ServiceProvider\AbstractServiceProvider;
use Masteriyo\Addons\Assignment\Query\AssignmentReplyQuery;
use Masteriyo\Addons\Assignment\RestApi\AssignmentRepliesController;
use Masteriyo\Addons\Assignment\Repository\AssignmentReplyRepository;

/**
 * AssignmentReply service provider.
 *
 * @since 2.3.5
 */
class AssignmentReplyServiceProvider extends AbstractServiceProvider {
	/**
	 * The provided array is a way to let the container
	 * know that a service is provided by this service
	 * provider. Every service that is registered via
	 * this service provider must have an alias added
	 * to this array or it will be ignored
	 *
	 * @since 2.3.5
	 *
	 * @var array
	 */
	protected $provides = array(
		'assignment-reply',
		'assignment-reply.store',
		'assignment-reply.rest',
		'mto-assignment-reply',
		'mto-assignment-reply.store',
		'mto-assignment-reply.rest',
		AssignmentRepliesController::class,


		'query.assignment-reply',
		AssignmentReplyQuery::class,
	);

	/**
	 * This is where the magic happens, within the method you can
	 * access the container and register or retrieve anything
	 * that you need to, but remember, every alias registered
	 * within this method must be declared in the `$provides` array.
	 *
	 * @since 2.3.5
	 */
	public function register() {
		$this->getContainer()->add( 'assignment-reply.store', AssignmentReplyRepository::class );

		$this->getContainer()->add( 'assignment-reply.rest', AssignmentRepliesController::class )
			->addArgument( 'permission' );

		$this->getContainer()->add( AssignmentRepliesController::class )
			->addArgument( 'permission' );

		$this->getContainer()->add( 'assignment-reply', AssignmentReply::class )
			->addArgument( 'assignment-reply.store' );

		// Register based on post type.
		$this->getContainer()->add( 'mto-assignment-reply', AssignmentReply::class )
			->addArgument( 'assignment-reply.store' );

		$this->getContainer()->add( 'mto-assignment-reply.store', AssignmentReplyRepository::class );

		$this->getContainer()->add( 'mto-assignment-reply.rest', AssignmentRepliesController::class )
				->addArgument( 'permission' );

		$this->getContainer()->add( 'query.assignment-reply', AssignmentReplyQuery::class );

		$this->getContainer()->add( AssignmentReplyQuery::class );
	}
}
