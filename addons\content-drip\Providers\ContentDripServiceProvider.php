<?php
/**
 * ContentDrip service provider.
 *
 * @since 2.5.0
 * @package \Masteriyo\Addons\ContentDrip
 */

namespace Masteriyo\Addons\ContentDrip\Providers;

defined( 'ABSPATH' ) || exit;

use Masteriyo\Addons\ContentDrip\ContentDripAddon;
use League\Container\ServiceProvider\AbstractServiceProvider;

/**
 * ContentDrip service provider.
 *
 * @since 2.5.0
 */
class ContentDripServiceProvider extends AbstractServiceProvider {
	/**
	 * The provided array is a way to let the container
	 * know that a service is provided by this service
	 * provider. Every service that is registered via
	 * this service provider must have an alias added
	 * to this array or it will be ignored
	 *
	 * @since 2.5.0
	 *
	 * @var array
	 */
	protected $provides = array(
		'addons.content-drip',
	);

	/**
	 * This is where the magic happens, within the method you can
	 * access the container and register or retrieve anything
	 * that you need to, but remember, every alias registered
	 * within this method must be declared in the `$provides` array.
	 *
	 * @since 2.5.0
	 */
	public function register() {
		$this->getContainer()->add( 'addons.content-drip', ContentDripAddon::class, true );
	}
}
