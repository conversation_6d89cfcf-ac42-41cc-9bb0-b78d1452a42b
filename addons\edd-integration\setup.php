<?php
/**
 * Setup edd integration.
 *
 * @since 2.6.8
 */

defined( 'ABSPATH' ) || exit;

use Masteriyo\Capabilities;

// Copy Masteriyo manager capabilities to EDD shop manager.
$manager = get_role( 'shop_manager' );
if ( $manager ) {
	foreach ( Capabilities::get_manager_capabilities() as $cap => $grant ) {
		$manager->add_cap( $cap, $grant );
	}
}

// Add student role to all the EDD Customers.
if ( function_exists( 'edd_get_customers' ) ) {
	$customers = edd_get_customers(
		array(
			'number' => -1,
		)
	);

	foreach ( $customers as $customer ) {
		$customer = new WP_User( $customer->id );

		if (
			empty(
				array_intersect(
					(array) $customer->roles,
					array(
						'administrator',
						'masteriyo_manager',
						'masteriyo_student',
					)
				)
			)
		) {
			$customer->add_role( 'masteriyo_student' );
		}
	}
}
