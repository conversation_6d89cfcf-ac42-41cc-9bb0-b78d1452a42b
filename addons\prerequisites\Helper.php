<?php
/**
 * Masteriyo Prerequisites helper class.
 *
 * @package Masteriyo\Prerequisites
 *
 * @since 2.3.2
 */

namespace Masteriyo\Addons\Prerequisites;

use Masteriyo\Constants;
use Masteriyo\Enums\CourseProgressStatus;
use Masteriyo\Models\UserCourse;
use Masteriyo\Query\CourseProgressQuery;
use Masteriyo\Query\CourseQuery;

defined( 'ABSPATH' ) || exit;

/**
 * Helper Prerequisites class.
 *
 * @class Masteriyo\Addons\Prerequisites\Helper
 */

class Helper {
	/**
	 * Return prerequisites data.
	 *
	 * @since 2.3.2
	 *
	 * @param int|\Masteriyo\Models\Course|WP_Post $course Course object.
	 * @param int|\Masteriyo\Models\User|WP_user $user User object.
	 *
	 * @return array
	 */
	public static function get_prerequisites_courses( $course, $user = null ) {
		$user   = empty( $user ) ? get_current_user_id() : $user;
		$course = masteriyo_get_course( $course );
		$user   = masteriyo_get_user( $user );

		if ( is_null( $course ) ) {
			return array();
		}

		$prerequisite_ids = $course->get_meta( '_prerequisites_courses', true );

		if ( empty( $prerequisite_ids ) ) {
			return array();
		}

		// Get prerequisites courses.
		$query = new CourseQuery(
			array(
				'include' => $prerequisite_ids,
			)
		);

		// Fold the prerequisites courses to dictionary.
		$prerequisites = array_reduce(
			$query->get_courses(),
			function( $map, $course ) {
				$map[ $course->get_id() ] = array(
					'id'        => $course->get_id(),
					'name'      => $course->get_name(),
					'permalink' => $course->get_permalink(),
					'status'    => $course->get_status(),
				);

				return $map;
			},
			array()
		);

		if ( is_wp_error( $user ) ) {
			return array_values( $prerequisites );
		}

		$query = new CourseProgressQuery(
			array(
				'user_id'         => $user->get_id(),
				'include_courses' => $prerequisite_ids,
			)
		);

		$course_progresses = $query->get_course_progress();

		foreach ( $course_progresses as $course_progress ) {
			$course = masteriyo_get_course( $course_progress->get_course_id() );

			if ( is_null( $course ) ) {
				continue;
			}

			if ( isset( $prerequisites[ $course->get_id() ] ) ) {
				$prerequisites[ $course->get_id() ]['status'] = $course_progress->get_status();
			}
		}

		return array_values( $prerequisites );
	}

	/**
	 * Return true if the courses prerequisites are met.
	 *
	 * @since 2.3.2
	 *
	 * @param int|\Masteriyo\Models\Course|WP_Post $course Course object.
	 * @param int|\Masteriyo\Models\User|WP_user $user User object.
	 *
	 * @return boolean
	 */
	public static function prerequisites_satisfied( $course, $user = null ) {
		$prerequisites = self::get_prerequisites_courses( $course, $user );

		foreach ( $prerequisites as $prerequisite ) {
			if ( CourseProgressStatus::COMPLETED !== $prerequisite['status'] ) {
				return false;
			}
		}

		return true;
	}

	/**
	 * Remove prerequisite course that are drafted.
	 *
	 * @since 2.11.0
	 *
	 * @param int|\Masteriyo\Models\Course|WP_Post $course Course object.
	 * @param int|\Masteriyo\Models\User|WP_user $user User object.
	 *
	 * @return boolean
	 */
	public static function is_prerequisite_course_drafted( $course ) {
		$courses = $course->get_meta( '_prerequisites_courses' );

		foreach ( $courses as $key => $pre_course_id ) {
			$course_data = masteriyo_get_course( $pre_course_id );
			if ( 'draft' === $course_data->get_status() ) {
				unset( $courses[ $key ] );
			}
		}
		return $courses;
	}
}
