<?php
/**
 * Grade class.
 *
 * @since 2.5.20
 *
 * @package Masteriyo\PostType;
 */

namespace Masteriyo\Addons\Gradebook\PostType;

defined( 'ABSPATH' ) || exit;


use Masteriyo\PostType\PostType;

/**
 * Grade class.
 */
class Grade extends PostType {
	/**
	 * Post Slug.
	 *
	 * @since 2.5.20
	 *
	 * @var string
	 */
	protected $slug = 'mto-grade';

	/**
	 * Constructor.
	 */
	public function __construct() {
		$debug      = masteriyo_is_post_type_debug_enabled();
		$permalinks = masteriyo_get_permalink_structure();

		$this->labels = array(
			'name'                  => _x( 'Grade', 'Grade General Name', 'learning-management-system' ),
			'singular_name'         => _x( 'Grade', 'Grade Singular Name', 'learning-management-system' ),
			'menu_name'             => __( 'Grade', 'learning-management-system' ),
			'name_admin_bar'        => __( 'Grade', 'learning-management-system' ),
			'archives'              => __( 'Grade Archives', 'learning-management-system' ),
			'attributes'            => __( 'Grade Attributes', 'learning-management-system' ),
			'parent_item_colon'     => __( 'Parent Grade:', 'learning-management-system' ),
			'all_items'             => __( 'All Grades', 'learning-management-system' ),
			'add_new_item'          => __( 'Add New Item', 'learning-management-system' ),
			'add_new'               => __( 'Add New', 'learning-management-system' ),
			'new_item'              => __( 'New Grade', 'learning-management-system' ),
			'edit_item'             => __( 'Edit Grade', 'learning-management-system' ),
			'update_item'           => __( 'Update Grade', 'learning-management-system' ),
			'view_item'             => __( 'View Grade', 'learning-management-system' ),
			'view_items'            => __( 'View Grades', 'learning-management-system' ),
			'search_items'          => __( 'Search Grade', 'learning-management-system' ),
			'not_found'             => __( 'Not found', 'learning-management-system' ),
			'not_found_in_trash'    => __( 'Not found in Trash.', 'learning-management-system' ),
			'featured_image'        => __( 'Featured Image', 'learning-management-system' ),
			'set_featured_image'    => __( 'Set featured image', 'learning-management-system' ),
			'remove_featured_image' => __( 'Remove featured image', 'learning-management-system' ),
			'use_featured_image'    => __( 'Use as featured image', 'learning-management-system' ),
			'insert_into_item'      => __( 'Insert into Grade', 'learning-management-system' ),
			'uploaded_to_this_item' => __( 'Uploaded to this Grade', 'learning-management-system' ),
			'items_list'            => __( 'Grades list', 'learning-management-system' ),
			'items_list_navigation' => __( 'Grades list navigation', 'learning-management-system' ),
			'filter_items_list'     => __( 'Filter Grades list', 'learning-management-system' ),
		);

		$this->args = array(
			'label'               => __( 'Grades', 'learning-management-system' ),
			'description'         => __( 'Grades Description', 'learning-management-system' ),
			'labels'              => $this->labels,
			'supports'            => array( 'title', 'editor', 'author', 'comments', 'custom-fields', 'post-formats' ),
			'taxonomies'          => array(),
			'hierarchical'        => false,
			'menu_position'       => 6,
			'public'              => true,
			'show_ui'             => true,
			'show_in_menu'        => $debug,
			'show_in_admin_bar'   => $debug,
			'show_in_nav_menus'   => $debug,
			'show_in_rest'        => false,
			'has_archive'         => false,
			'map_meta_cap'        => true,
			'capability_type'     => array( 'grade', 'grades' ),
			'exclude_from_search' => true,
			'publicly_queryable'  => false,
			'can_export'          => true,
			'delete_with_user'    => true,
		);
	}
}
