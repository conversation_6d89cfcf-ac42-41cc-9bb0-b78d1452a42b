jQuery(function(t){if("undefined"==typeof _MASTERIYO_CHECKOUT_)return!1;function e(){return{message:'<span class="spinner" style="visibility:visible"></span>',css:{border:"",width:"0%"},overlayCSS:{background:"#fff",opacity:.6}}}var n={$form:t("form.masteriyo-checkout"),init:function(){this.$form.on("click",".masteriyo-apply-coupon-btn",this.applyCouponHandler),this.$form.on("click",".masteriyo-remove-coupon",this.removeCoupon<PERSON>and<PERSON>)},getAjaxURL:function(){return _MASTERIYO_CHECKOUT_.ajaxURL},getCheckoutURL:function(){return _MASTERIYO_CHECKOUT_.checkoutURL},removeErrorNotices:function(){t(".masteriyo-NoticeGroup-checkout, .masteriyo-error, .masteriyo-message").remove()},showError:function(o){n.$form.prepend('<div class="masteriyo-NoticeGroup masteriyo-NoticeGroup-checkout">'+o+"</div>"),n.$form.find(".input-text, select, input:checkbox").trigger("validate").trigger("blur"),n.scrollToNotices(),t(document.body).trigger("checkout_error",[o])},scrollToNotices:function(){var o=t(".masteriyo-NoticeGroup-updateOrderReview, .masteriyo-NoticeGroup-checkout");(o=o.length?o:t("form.masteriyo-checkout")).length&&t("html, body").animate({scrollTop:o.offset().top-100},1e3)},applyCouponHandler:function(o){o.preventDefault();o=t("#masteriyo-coupon-input").val().trim();o&&!n.$form.is(".processing")&&t.ajax({type:"POST",url:n.getAjaxURL(),dataType:"json",data:{action:"masteriyo_apply_coupon",_wpnonce:t('[name="masteriyo-apply-coupon-nonce"]').val(),coupon:o},beforeSend:function(o){n.removeErrorNotices(),n.$form.block(e())},success:function(o,e,r){o.success?(t.each(o.data.fragments,function(o,e){n.fragments&&n.fragments[o]===e||t(o).replaceWith(e)}),n.fragments=o.data.fragments,t("#masteriyo-coupon-input").val("")):n.showError('<div class="masteriyo-error">'+o.data.message+"</div>")},error:function(o,e,r){try{var t=o.responseJSON;n.showError('<div class="masteriyo-error">'+t.data.messages+"</div>")}catch(t){console.log(t)}},complete:function(o,e){n.$form.unblock()}})},removeCouponHandler:function(o){o.preventDefault();o=t(o.currentTarget).data("coupon-code");o&&!n.$form.is(".processing")&&t.ajax({type:"POST",url:n.getAjaxURL(),dataType:"json",data:{action:"masteriyo_remove_applied_coupon",_wpnonce:t('[name="masteriyo-remove-applied-coupon-nonce"]').val(),coupon:o},beforeSend:function(o){n.removeErrorNotices(),n.$form.block(e())},success:function(o,e,r){o.success?(t.each(o.data.fragments,function(o,e){n.fragments&&n.fragments[o]===e||t(o).replaceWith(e)}),n.fragments=o.data.fragments):n.showError('<div class="masteriyo-error">'+o.data.message+"</div>")},error:function(o,e,r){try{var t=o.responseJSON;n.showError('<div class="masteriyo-error">'+t.data.messages+"</div>")}catch(t){console.log(t)}},complete:function(o,e){n.$form.unblock()}})}};n.init()});