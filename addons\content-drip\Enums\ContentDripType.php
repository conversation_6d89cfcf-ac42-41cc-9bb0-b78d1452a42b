<?php
/**
 * Content Drip type enums.
 *
 * @since 2.5.0
 * @package Masteriyo\Addons\ContentDrip\Enums
 */

namespace Masteriyo\Addons\ContentDrip\Enums;

defined( 'ABSPATH' ) || exit;

/**
 * Content Drip type enum class.
 *
 * @since 2.5.0
 */
class ContentDripType {
	/**
	 * Content Drip date type.
	 *
	 * @since 2.5.0
	 * @var string
	 */
	const DATE = 'date';

	/**
	 * Content Drip days type.
	 *
	 * @since 2.5.5
	 * @var string
	 */
	const DAYS = 'days';

	/**
	 * Return all content drip types.
	 *
	 * @since 2.5.0
	 *
	 * @return array
	 */
	public static function all() {
		return array_unique(
			/**
			 * Filters content drip status list.
			 *
			 * @since 2.5.0
			 *
			 * @param string[] $types Content drip types.
			 */
			apply_filters(
				'masteriyo_content_drip_types',
				array(
					self::DATE,
					self::DAYS,
				)
			)
		);
	}
}
