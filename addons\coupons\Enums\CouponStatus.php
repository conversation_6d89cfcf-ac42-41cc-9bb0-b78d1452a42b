<?php
/**
 * Coupon status enum class.
 *
 * @since 2.5.12
 * @package Masteriyo\Addons\Coupons
 */

namespace Masteriyo\Addons\Coupons\Enums;

use Masteriyo\Enums\PostStatus;

defined( 'ABSPATH' ) || exit;

/**
 * Coupon status enum class.
 *
 * @since 2.5.12
 */
class CouponStatus extends PostStatus {

	/**
	 * Coupon active status.
	 *
	 * @since 2.5.12
	 *
	 * @var string
	 */
	const ACTIVE = 'active';

	/**
	 * Coupon expired status.
	 *
	 * @since 2.5.12
	 *
	 * @var string
	 */
	const EXPIRED = 'expired';

	/**
	 * Coupon scheduled status.
	 *
	 * @since 2.5.12
	 *
	 * @var string
	 */
	const SCHEDULED = 'scheduled';

	/**
	 * Return all the coupon statuses.
	 *
	 * @since 2.5.12
	 *
	 * @return array
	 */
	public static function all() {
		return array_unique(
			/**
			 * Filters coupon status list.
			 *
			 * @since 2.5.12
			 *
			 * @param string[] $statuses Coupon status list.
			 */
			apply_filters(
				'masteriyo_coupon_statuses',
				array_merge(
					parent::all(),
					array(
						self::ACTIVE,
						self::EXPIRED,
						self::SCHEDULED,
					)
				)
			)
		);
	}

	/**
	 * List coupon status primarily used for registering status.
	 *
	 * @since 2.5.27
	 *
	 * @return array
	 */
	public static function list() {
		$coupon_statuses = array(
			'active'    => array(
				'label'                     => _x( 'Active', 'Coupon status', 'learning-management-system' ),
				'public'                    => true,
				'exclude_from_search'       => false,
				'show_in_admin_all_list'    => true,
				'show_in_admin_status_list' => true,
				// translators: %s: number of coupons
				'label_count'               => _n_noop( 'Active <span class="count">(%s)</span>', 'Active <span class="count">(%s)</span>', 'learning-management-system' ),
			),
			'expired'   => array(
				'label'                     => _x( 'Expired', 'Coupon status', 'learning-management-system' ),
				'public'                    => true,
				'exclude_from_search'       => false,
				'show_in_admin_all_list'    => true,
				'show_in_admin_status_list' => true,
				/* translators: %s: number of coupons */
				'label_count'               => _n_noop( 'On hold <span class="count">(%s)</span>', 'On hold <span class="count">(%s)</span>', 'learning-management-system' ),
			),
			'scheduled' => array(
				'label'                     => _x( 'Scheduled', 'Coupon status', 'learning-management-system' ),
				'public'                    => true,
				'exclude_from_search'       => false,
				'show_in_admin_all_list'    => true,
				'show_in_admin_status_list' => true,
				/* translators: %s: number of coupons */
				'label_count'               => _n_noop( 'Completed <span class="count">(%s)</span>', 'Completed <span class="count">(%s)</span>', 'learning-management-system' ),
			),
		);

		/**
		 * Filters coupon statuses.
		 *
		 * @since 1.0.0
		 *
		 * @param array $coupon_statuses The coupon statuses and its parameters.
		 */
		return apply_filters( 'masteriyo_coupon_statuses', $coupon_statuses );
	}
}
