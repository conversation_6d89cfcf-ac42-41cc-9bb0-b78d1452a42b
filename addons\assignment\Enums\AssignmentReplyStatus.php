<?php
/**
 * Assignment reply/submission status enums.
 *
 * @since 2.5.20
 * @package Masteriyo\Addons\Assignment
 */

namespace Masteriyo\Addons\Assignment\Enums;

use Masteriyo\Enums\PostStatus;

defined( 'ABSPATH' ) || exit;

/**
 * Assignment reply/submission status enum class.
 *
 * @since 2.5.20
 */
class AssignmentReplyStatus extends PostStatus {
	/**
	 * Assignment reply/submission pending status.
	 *
	 * @since 2.5.20
	 * @var string
	 */
	const PENDING = PostStatus::DRAFT;

	/**
	 * Assignment reply/submission reviewed status.
	 *
	 * @since 2.5.20
	 * @var string
	 */
	const REVIEWED = PostStatus::PUBLISH;

	/**
	 * Return all Assignment reply/submission statuses.
	 *
	 * @since 2.5.20
	 *
	 * @return array
	 */
	public static function all() {
		return array_unique(
			/**
			 * Filters Assignment reply/submission status list.
			 *
			 * @since 2.5.20
			 *
			 * @param string[] $statuses Assignment reply/submission status list.
			 */
			apply_filters(
				'masteriyo_assignment_reply_statuses',
				array(
					self::PENDING,
					self::REVIEWED,
				)
			)
		);
	}
}
