<?php
/**
 * Shortcode for listing course bundles.
 *
 * @since 2.12.0
 * @package Masteriyo\Addons\CourseBundle\Shortcodes
 */

namespace Masteriyo\Addons\CourseBundle\Shortcodes;

use Masteriyo\Addons\CourseBundle\Query\CourseBundleQuery;
use Masteriyo\Shortcodes\CoursesShortcode;
use Masteriyo\Addons\CourseBundle\Models\CourseBundle;

defined( 'ABSPATH' ) || exit;

/**
 * Course bundle shortcode class.
 */
class CourseBundlesShortcode extends CoursesShortcode {

	/**
	 * Shortcode tag.
	 *
	 * @since 2.12.0
	 *
	 * @var string
	 */
	protected $tag = 'masteriyo_course_bundles';

	/**
	 * Get shortcode content.
	 *
	 * @since 2.12.0
	 *
	 * @return string
	 */
	public function get_content() {
		$attr = $this->get_attributes();

		$current_url  = $_SERVER['REQUEST_URI'];
		$current_page = $this->get_page_from_url( $current_url );
		$is_paginate  = 'on' === $attr['show_pagination'] ? true : false;

		$args = array(
			'limit'          => absint( $attr['count'] ),
			'order'          => sanitize_text_field( $attr['order'] ),
			'orderby'        => sanitize_text_field( $attr['orderby'] ),
			'include'        => $this->parse_values_attribute( $attr['ids'], ',', 'absint' ),
			'exclude'        => $this->parse_values_attribute( $attr['exclude_ids'], ',', 'absint' ),
			'posts_per_page' => $is_paginate ? absint( $attr['per_page'] ) : -1,
			'paginate'       => true,
			'page'           => absint( $current_page ),
			'offset'         => ( absint( $current_page ) - 1 ) * absint( $attr['per_page'] ),
		);

		$course_bundle_query = new CourseBundleQuery( $args );
		$result              = $course_bundle_query->get_course_bundles();

		/**
		 * Filters courses that will be displayed in courses shortcode.
		 *
		 * @since 2.12.0
		 *
		 * @param CourseBundle[] $course_bundles The course bundles objects.
		 */
		$course_bundles = apply_filters( 'masteriyo_shortcode_course_bundles_result', $result->course_bundles );
		masteriyo_set_loop_prop( 'columns', absint( $attr['columns'] ) );

		$columns = absint( $attr['columns'] );
		$columns = $columns > 4 || 1 > $columns ? 3 : $columns;

		masteriyo_set_loop_prop( 'columns', $columns );

		ob_start();

		echo '<div class="masteriyo-w-100 masteriyo-bundle-list-display-section">';

		if ( $result->total ) {
			$original_course_bundle = isset( $GLOBALS['course_bundle'] ) ? $GLOBALS['course_bundle'] : null;

			/**
			 * Fires before course loop in courses shortcode.
			 *
			 * @since 2.12.0
			 *
			 * @param array $attr Shortcode attributes.
			 * @param CourseBundle[] $courses The courses objects.
			 */
			do_action( 'masteriyo_shortcode_before_course_bundles_loop', $attr, $course_bundles );

			masteriyo_course_bundle_loop_start();

			foreach ( $course_bundles as $course_bundle ) {
				$GLOBALS['course_bundle'] = $course_bundle;

				masteriyo_get_template( 'course-bundle/content-course-bundle.php' );

			}

			$GLOBALS['course_bundle'] = $original_course_bundle;

			masteriyo_course_bundle_loop_end();

			/**
			 * Fires after course loop in courses shortcode.
			 *
			 * @since 2.12.0
			 *
			 * @param array $attr Shortcode attributes.
			 * @param CourseBundle[] $courses The courses objects.
			 */
			do_action( 'masteriyo_shortcode_after_course_bundles_loop', $attr, $course_bundles );

			masteriyo_reset_loop();
		} else {
			/**
			 * Fires when there is no course to display in courses shortcode.
			 *
			 * @since 2.12.0
			 */
			do_action( 'masteriyo_shortcode_no_course_bundles_found' );
		}

		echo '</div>';

		if ( $is_paginate ) {
			echo wp_kses(
				paginate_links(
					array(
						'type'      => 'list',
						'prev_text' => masteriyo_get_svg( 'left-arrow' ),
						'next_text' => masteriyo_get_svg( 'right-arrow' ),
						'total'     => $result->max_num_pages,
						'current'   => $current_page,
					)
				),
				'masteriyo_pagination'
			);
		}

		return \ob_get_clean();
	}
}
