<?php
/**
 * Coupon discount type enums.
 *
 * @since 2.18.3
 * @package Masteriyo\Addons\Coupons
 */

namespace Masteriyo\Addons\Coupons\Enums;

defined( 'ABSPATH' ) || exit;

/**
 * Coupon applies to enum class.
 *
 * @since 2.18.3
 */
class AppliesToType {
	/**
	 * Coupon applies to all.
	 *
	 * @since 2.18.3
	 */
	const ALL = 'all';

	/**
	 * Coupon applies to all courses.
	 *
	 * @since 2.18.3
	 */
	const ALL_COURSES = 'all_courses';

	/**
	 * Coupon applies to all bundles.
	 *
	 * @since 2.18.3
	 */
	const ALL_BUNDLES = 'all_bundles';

	/**
	 * Coupon applies to specific courses.
	 *
	 * @since 2.18.3
	 */
	const SPECIFIC_COURSES = 'specific_courses';

	/**
	 * Coupon applies to specific bundles.
	 *
	 * @since 2.18.3
	 */
	const SPECIFIC_BUNDLES = 'specific_bundles';

	/**
	 * Coupon applies to specific categories.
	 *
	 * @since 2.18.3
	 */
	const SPECIFIC_CATEGORIES = 'specific_categories';

	/**
	 * Return all the Coupon discount types.
	 *
	 * @since 2.18.3
	 *
	 * @return array
	 */
	public static function all() {
		/**
		 * Filters Coupon discount types list.
		 *
		 * @since 2.18.3
		 *
		 * @param string[] $statuses Coupon discount types list.
		 */
		return apply_filters(
			'masteriyo_coupon_applies_to_types',
			array(
				self::ALL,
				self::ALL_COURSES,
				self::ALL_BUNDLES,
				self::SPECIFIC_COURSES,
				self::SPECIFIC_BUNDLES,
				self::SPECIFIC_CATEGORIES,
			)
		);
	}
}
