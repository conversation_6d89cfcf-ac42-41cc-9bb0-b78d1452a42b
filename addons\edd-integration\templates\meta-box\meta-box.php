<?php
/**
 * Meta box template.
 *
 * @since 2.6.8
 *
 * @package Masteriyo\Addons\EDDIntegration
 */

defined( 'ABSPATH' ) || exit;

use Masteriyo\PostType\PostType;

?>

<div class="edd-form-group">
	<div class="edd-form-group__control">
		<input type="checkbox" onchange="masteriyo_handle_course_input_change(event)" class="edd-form-group__input" name="_is_masteriyo_course" id="edd_is_masteriyo_course" value="yes" <?php checked( 'yes', $is_masteriyo_course ); ?> />
		<label for="edd_is_masteriyo_course">
			<?php esc_html_e( 'Is Masteriyo Course?', 'learning-management-system' ); ?>
		</label>
	</div>
	<div class="edd-form-group__control" style="<?php echo esc_attr( 'yes' === $is_masteriyo_course ? '' : 'display: none;' ); ?>">
		<label for="edd_masteriyo_course_id" class="edd-form-group__label">
			<?php esc_html_e( 'Course', 'learning-management-system' ); ?>
			<span alt="f223" class="edd-help-tip dashicons dashicons-editor-help" title="<?php esc_attr_e( 'Select a course to connect with the download.', 'learning-management-system' ); ?>"></span>
		</label>
		<select
			name="_masteriyo_course_id"
			id="masteriyo-course-id"
			class="edd-select edd-select-chosen mto-course-select edd-form-group__input"
			data-placeholder="<?php esc_attr_e( 'Select a course', 'learning-management-system' ); ?>"
			data-search-type="<?php echo esc_attr( masteriyo_kebab_to_snake( PostType::COURSE ) ); ?>"
			data-search-placeholder="<?php esc_attr_e( 'Search courses...', 'learning-management-system' ); ?>"
		>
			<?php foreach ( $courses as $id => $title ) : ?>
				<option value="<?php echo esc_attr( $id ); ?>" <?php selected( $id, $masteriyo_course_id ); ?>><?php echo esc_html( $title ); ?></option>
			<?php endforeach; ?>
		</select>
	</div>
</div>
