<?php

defined( 'ABSPATH' ) || exit;

/**
 * Single course bundle content template.
 *
 * @since 2.12.0
 */

global $course_bundle;

if ( empty( $course_bundle ) ) {
	return;
}


/**
 * Fires before rendering single course page content.
 *
 * @since 2.12.0
 */
do_action( 'masteriyo_before_single_course_bundle_content' );
?>
<div id="bundle-<?php the_ID(); ?>" class="masteriyo-bundle">
	<div class="masteriyo-col-8">
		<div class="masteriyo-bundle__content">
			<?php
			/**
			 * Action hook for rendering single course bundle page content.
			 *
			 * @since 2.12.0
			 */
			do_action( 'masteriyo_single_course_bundle_content', $course_bundle );
			?>
		</div>
	</div>
	<div class="masteriyo-col-4">
		<aside class="masteriyo-bundle__sidebar">
		<div class="masteriyo-bundle__sidebar__one">
			<?php
			/**
			 * Action hook for rendering single course bundle page sidebar content one.
			 *
			 * @since 2.12.0
			 */
			do_action( 'masteriyo_single_course_bundle_sidebar_content_one', $course_bundle );
			?>
			</div>
			<div class="masteriyo-bundle__sidebar__two">
			<?php
			/**
			 * Action hook for rendering single course bundle page sidebar content two.
			 *
			 * @since 2.12.0
			 */
			do_action( 'masteriyo_single_course_bundle_sidebar_content_two', $course_bundle );
			?>
			</div>
		</aside>
	</div>
</div>
<?php
do_action( 'masteriyo_after_single_course_bundle_content' );
