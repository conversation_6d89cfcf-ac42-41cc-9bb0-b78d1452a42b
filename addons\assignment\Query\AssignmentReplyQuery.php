<?php
/**
 * Class for parameter-based Assignment Reply querying
 *
 * @package  Masteriyo\Addons\Assignment
 * @version 2.3.5
 * @since   2.3.5
 */

namespace Masteriyo\Addons\Assignment\Query;

use Masteriyo\Abstracts\ObjectQuery;

defined( 'ABSPATH' ) || exit;

/**
 * Assignment query class.
 */
class AssignmentReplyQuery extends ObjectQuery {

	/**
	 * Valid query vars for assignment replys.
	 *
	 * @since 2.3.5
	 *
	 * @return array
	 */
	protected function get_default_query_vars() {
		return array_merge(
			parent::get_default_query_vars(),
			array(
				'course_id' => '',
				'status'    => 'all',
			)
		);
	}

	/**
	 * Get assignment replies matching the current query vars.
	 *
	 * @since 2.3.5
	 * @deprecated 2.5.20
	 *
	 * @return Masteriyo\Addons\Models\AssignmentReply[] Assignment Reply objects
	 */
	public function get_course_reviews() {
		$this->get_assignment_replies();
	}

	/**
	 * Get assignment replies matching the current query vars.
	 *
	 * @since 2.5.20
	 *
	 * @return Masteriyo\Addons\Models\AssignmentReply[] Assignment Reply objects
	 */
	public function get_assignment_replies() {
		/**
		 * Filters Assignment Reply object query args.
		 *
		 * @since 2.3.5
		 *
		 * @param array $query_args The object query args.
		 */
		$args    = apply_filters( 'masteriyo_assignment_reply_object_query_args', $this->get_query_vars() );
		$results = masteriyo( 'assignment-reply.store' )->query( $args );

		/**
		 * Filters Assignment Reply object query results.
		 *
		 * @since 2.3.5
		 *
		 * @param Masteriyo\Addons\Models\AssignmentReply[] $results The query results.
		 * @param array $query_args The object query args.
		 */
		return apply_filters( 'masteriyo_assignment_reply_object_query', $results, $args );
	}
}
