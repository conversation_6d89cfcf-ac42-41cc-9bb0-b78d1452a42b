<?php
/**
 * New assignment webhook event listener class.
 *
 * @since 2.8.3
 */

namespace Masteriyo\Addons\Assignment\Listener;

use Masteriyo\Abstracts\Listener;
use Masteriyo\Addons\Assignment\Resources\AssignmentResource;
use Masteriyo\Resources\WebhookResource;

defined( 'ABSPATH' ) || exit;

/**
 * New assignment created webhook event listener class.
 *
 * @since 2.8.3
 */
class NewAssignmentListener extends Listener {

	/**
	 * Event name the listener is listening to.
	 *
	 * @since 2.8.3
	 */
	protected $event_name = 'assignment.created';

	/**
	 * Get event label.
	 *
	 * @since 2.8.3
	 *
	 * @return string
	 */
	public function get_label() {
		return __( 'New Assignment', 'learning-management-system' );
	}

	/**
	 * Setup the webhook event.
	 *
	 * @since 2.8.3
	 *
	 * @param callable $deliver_callback
	 * @param \Masteriyo\Models\Webhook $webhook
	 */
	public function setup( $deliver_callback, $webhook ) {
		add_action(
			'masteriyo_new_assignment',
			function( $id, $assignment ) use ( $deliver_callback, $webhook ) {
				if ( ! $this->can_deliver( $webhook, $assignment->get_id() ) ) {
					return;
				}

				call_user_func_array(
					$deliver_callback,
					array(
						WebhookResource::to_array( $webhook ),
						$this->get_payload( $assignment, $webhook ),
					)
				);
			},
			10,
			2
		);
	}

	/**
	 * Get payload data for the currently triggered webhook.
	 *
	 * @since 2.8.3
	 *
	 * @param \Masteriyo\Addons\Assignment $assignment
	 * @param \Masteriyo\Models\Webhook $webhook
	 *
	 * @return array
	 */
	protected function get_payload( $assignment, $webhook ) {
		$data = AssignmentResource::to_array( $assignment );

		/**
		 * Filters the payload data for the currently triggered webhook.
		 *
		 * @since 2.8.3
		 *
		 * @param array $data The payload data.
		 * @param \Masteriyo\Models\Webhook $webhook
		 * @param \Masteriyo\Listeners\Webhook\NewAssignmentListener $listener Listener object.
		 * @param \Masteriyo\Addons\Assignment $assignment Assignment model object.
		 */
		return apply_filters( "masteriyo_webhook_payload_for_{$this->event_name}", $data, $webhook );
	}
}
