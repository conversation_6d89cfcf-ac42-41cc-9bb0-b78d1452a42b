.masteriyo-checkout {
	&-summary {
		&-order-details {
			.masteriyo-coupon-row {				
				cursor: pointer;

				.coupon-discount-label {
					display: flex;
					align-items: center;

					span {
						margin-left: 6px;

						.masteriyo-remove-coupon {
							display: flex;
							align-items: center;
							width: 20px;
							height: 20px;
							background: #ffecec;
							justify-content: center;
							border-radius: 3px;
							padding: 3px;

							svg {
								width: 18px;
							}
						}
					}
				}
				
				.coupon-code {
					margin: 0;
					border: 1.5px dashed #4BB543;
					padding: 0 $spacing_8px;
					background: #F4FBF4;
					display: inline;
					color: #4BB543;
					font-weight: 500;
					cursor: text;
					text-align: center;
					border-radius: 3px;
				}

				// &:hover {
				// 	.coupon-discount-amount {
				// 		> span {
				// 			&:first-child {
				// 				transform: translateX(-35px);
				// 			}

				// 			&:last-child {								
				// 				opacity: 1;
				// 				transform: translateX(7px);
				// 			}
				// 		}
				// 	}
				// }
			}

			.coupon-discount-amount {
				display: flex;
				align-items: center;
				justify-content: center;
				position: relative;

				> span {
					&:first-child {
						transition: all .3s ease-in-out;
						font-weight: 500;
						color: #ff4c4c;
					}

					// &:last-child {
					// 	transition: all .3s ease-in-out;
					// 	margin-left: 8px;
					// 	position: absolute;
					// 	transform: translateX(40px);
					// 	opacity: 0;
					// }
				}

				// .masteriyo-remove-coupon {
				// 	display: flex;
				// 	align-items: center;
				// 	background: #ffecec;
				// 	padding: 4px;
				// 	border-radius: 3px;

				// 	svg {
				// 		width: 18px;
				// 	}
				// }
			}
		}

		&-apply-coupon {
			padding: $spacing_15px 0 $spacing_20px;
			margin-bottom: 0;
			background-color: var(--masteriyo-color-white);
			border: 0;
			border-bottom: 1px solid var(--masteriyo-color-border);
			color: var(--masteriyo-color-text);
			border-radius: 0;
			display: grid;
			grid-template-areas: "coupon-label coupon-label"
			"coupon-input coupon-btn";

			.masteriyo-label {
				grid-area: coupon-label;
				font-size: 15px;
				line-height: 28px;
				font-weight: bold;
				color: var(--masteriyo-color-grey-dark);
			}

			.masteriyo-input {
				grid-area: coupon-input;
				margin-bottom: 0;
				width: calc(100% - 20px);
				background: transparent;
			}

			.masteriyo-apply-coupon-btn {
				grid-area: coupon-btn;
				border: 1px solid #4584FF;
				background: transparent;
				color: #4584FF;
				transition: all .3s ease-in-out;
				
				&:hover {
					background: #4584FF;
					color: var(--masteriyo-color-white);
				}

				&.masteriyo-disabled {
					position: relative;
					border-color: #e4e4e4;
					color: #9e9e9e;
					cursor: not-allowed;

					&::before {
						content: "";
						background: #dfdfdf;
						border: 1px solid #dfdfdf;
						width: 100%;
						height: 100%;
						top: 0;
						left: 0;
						position: absolute;
						opacity: 0.4;
					}

					&:hover {
						background: unset;
						color: #9e9e9e;
					}
				}				
			}

			ul {
				margin: 0;
				padding: 0;
			}

			li {
				list-style-type: none;
				border-bottom: 1px solid var(--masteriyo-color-border);
				display: flex;
				flex-direction: row;
				justify-content: space-between;
				padding: $spacing_15px 0;

				&.h-border {
					border-bottom: 1px solid var(--masteriyo-color-black);
				}

				&:last-of-type {
					border-bottom: none;
				}
			}

			input {
				margin-bottom: 8px;
			}

			button {
				background: black;
			}
		}
	}
}
