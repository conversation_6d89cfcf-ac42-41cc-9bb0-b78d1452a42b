body.masteriyo {
	margin: 0;
}

.masteriyo-scorm-course-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	background: #ffffff;
	box-shadow: 0px 6px 26px 0px rgba(117, 117, 117, 0.12);
	padding: $spacing-12px $spacing-16px $spacing-12px $spacing-20px;
	margin-bottom: $spacing-60px;

	@media screen and (max-width: 460px) {
		flex-direction: column;
		padding: $spacing-20px;
		gap: $spacing-20px;
		align-items: flex-start;
	}

	&__course {
		max-width: 500px;

		&-name {
			color: #909090;
			font-family: Inter;
			font-size: 14px;
			font-weight: 400;
			line-height: 22px;
		}

		&-title {
			color: #383838;
			font-family: Inter;
			font-size: 16px;
			font-weight: 500;
			line-height: 26px;
			margin: 0;
		}
	}

	&__button-exit {
		border-radius: 4px;
		border: 1px solid #646464;
		background: #ffffff;
		padding: $spacing-6px $spacing-14px;
		color: #646464;
		font-family: Inter;
		font-size: 14px;
		font-weight: 500;
		line-height: 22px;
		text-decoration: none;
		transition: all 0.3s ease-in-out;

		@media screen and (max-width: 460px) {
			width: 100%;
			text-align: center;
			padding: $spacing-8px $spacing-16px;
		}

		&:hover {
			color: #ffffff;
			background: var(--masteriyo-color-badge-red);
			border-color: var(--masteriyo-color-badge-red);
		}
	}

	&__button-download {
		border-radius: 4px;
		border: 1px solid var(--masteriyo-color-primary);
		background: #ffffff;
		padding: $spacing-6px $spacing-14px;
		color: var(--masteriyo-color-primary);
		font-family: Inter;
		font-size: 14px;
		font-weight: 500;
		line-height: 22px;
		text-decoration: none;
		transition: all 0.3s ease-in-out;

		@media screen and (max-width: 460px) {
			margin: 0;
			width: 100%;
			text-align: center;
			padding: $spacing-8px $spacing-16px;
		}

		&:hover {
			color: #ffffff;
			background: var(--masteriyo-color-primary-light);
			border-color: var(--masteriyo-color-primary-light);
		}
	}

	&__button-complete {
		border-radius: 4px;
		border: 1px solid #3ca03c;
		background: #3ca03c;
		padding: $spacing-6px $spacing-14px;
		color: #ffffff;
		font-family: Inter;
		font-size: 14px;
		font-weight: 500;
		line-height: 22px;
		text-decoration: none;
		transition: all 0.3s ease-in-out;
		opacity: 0.8;

		@media screen and (max-width: 460px) {
			margin: 0;
			width: 100%;
			text-align: center;
			padding: $spacing-8px $spacing-16px;
		}

		&:hover {
			color: #ffffff;
		}
	}

	&__button-continue {
		border-radius: 4px;
		border: 1px solid var(--masteriyo-color-primary);
		background: var(--masteriyo-color-primary);
		padding: $spacing-6px $spacing-14px;
		color: #ffffff;
		font-family: Inter;
		font-size: 14px;
		font-weight: 500;
		line-height: 22px;
		text-decoration: none;
		transition: all 0.3s ease-in-out;

		@media screen and (max-width: 460px) {
			margin: 0;
			width: 100%;
			text-align: center;
			padding: $spacing-8px $spacing-16px;
		}

		&:hover {
			color: var(--masteriyo-color-primary);
			background: #ffffff;
		}
	}
}

.masteriyo-wrapper {
	&.scorm {
		position: relative;
		padding: 0;
		height: 100vh;

		iframe {
			display: block;
			width: 100%;
			height: 100%;
			border: 0;
		}
	}
}
