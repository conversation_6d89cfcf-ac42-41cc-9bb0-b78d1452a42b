<?php
/**
 * Course Coming Soon service provider.
 *
 * @since 1.11.0 [free]
 * @package \Masteriyo\Addons\CourseComingSoon
 */

namespace Masteriyo\Addons\CourseComingSoon\Providers;

defined( 'ABSPATH' ) || exit;

use League\Container\ServiceProvider\AbstractServiceProvider;
use Masteriyo\Addons\CourseComingSoon\CourseComingSoonAddon;

/**
 * CourseComingSoon service provider.
 *
 * @since 1.11.0 [free]
 */
class CourseComingSoonServiceProvider extends AbstractServiceProvider {
	/**
	 * The provided array is a way to let the container
	 * know that a service is provided by this service
	 * provider. Every service that is registered via
	 * this service provider must have an alias added
	 * to this array or it will be ignored
	 *
	 * @since 1.11.0 [free]
	 *
	 * @var array
	 */
	protected $provides = array(
		'addons.course-coming-soon',
	);

	/**
	 * This is where the magic happens, within the method you can
	 * access the container and register or retrieve anything
	 * that you need to, but remember, every alias registered
	 * within this method must be declared in the `$provides` array.
	 *
	 * @since 1.11.0 [free]
	 */
	public function register() {
		$this->getLeagueContainer()->add( 'addons.course-coming-soon', CourseComingSoonAddon::class, true );
	}
}
