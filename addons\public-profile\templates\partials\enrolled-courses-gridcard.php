<?php

/**
 * The Template for displaying enrolled courses grid-card of enrolled courses tab in the public profile page.
 *
 * @since 2.6.8
 */

use Masteriyo\Addons\PublicProfile\Svg;

defined( 'ABSPATH' ) || exit; // Exit if accessed directly.
?>

<?php foreach ( $enrolled_courses as $enrolled_course ) : ?>
	<div class="masteriyo-enrolled-courses--content-gridcard">
		<div class="masteriyo-course-thumbnail">
			<img src="<?php echo esc_attr( $enrolled_course->get_featured_image_url( 'masteriyo_medium' ) ); ?>" alt="Thumbnail">

			<?php
			$author     = masteriyo_get_user( $enrolled_course->get_author_id() );
			$difficulty = $enrolled_course->get_difficulty();
			?>

			<?php if ( $difficulty ) : ?>
				<span class="masteriyo-course-level masteriyo-<?php echo esc_attr( $difficulty['slug'] ); ?>"><?php echo esc_html( $difficulty['name'] ); ?></span>
			<?php endif; ?>
		</div>

		<div class="masteriyo-enrolled-course-desc">
			<?php foreach ( $enrolled_course->get_categories() as $category ) : ?>
				<span class="masteriyo-course-category">
					<?php echo esc_html( $category->get_name() ); ?>
				</span>
			<?php endforeach; ?>

			<h2 class="masteriyo-course-title">
				<a href="<?php echo esc_url( $enrolled_course->get_permalink() ); ?>" title="<?php echo esc_attr( $enrolled_course->get_name() ); ?>">
					<?php echo esc_html( $enrolled_course->get_name() ); ?>
				</a>
			</h2>

			<div class="masteriyo-course-author-rating-wrapper">
				<div class="masteriyo-course-author">
					<figure>
						<img src="<?php echo esc_attr( $author->profile_image_url() ); ?>" alt="<?php echo esc_attr( $author->get_display_name() ); ?>">
					</figure>
					<span class="masteriyo-course-author-name"><?php echo esc_html( $author->get_display_name() ); ?></span>
				</div>

				<div class="masteriyo-course-rating">
					<?php if ( $enrolled_course->is_review_allowed() ) : ?>
						<?php Svg::get( 'rating', true ); ?>
						<a href="#" class="masteriyo-course-rating-count">
							<?php echo esc_html( masteriyo_format_decimal( $enrolled_course->get_average_rating(), 1, true ) ); ?> (<?php echo esc_html( $enrolled_course->get_rating_count() ); ?>)
						</a>
					<?php endif; ?>
				</div>

			</div>

			<div class="masteriyo-course-duration-percent-wrapper">
				<div class="masteriyo-course-duration">
					<?php Svg::get( 'clock', true ); ?>
					<span><?php echo esc_html( masteriyo_minutes_to_time_length_string( $enrolled_course->get_duration() ) ); ?></span>
				</div>

				<span class="masteriyo-course-percent">
					<?php
					printf(
						/* translators: %s: course progress in percentage */
						esc_html__( '%s Completed', 'learning-management-system' ),
						esc_html( $enrolled_course->get_progress_status( true, masteriyo_get_user( $user_id ) ) )
					);
					?>
				</span>
			</div>

			<div class="masteriyo-course-progress">
				<div class="masteriyo-course-progress-value" style="--value: <?php echo esc_attr( $enrolled_course->get_progress_status( true, masteriyo_get_user( $user_id ) ) ); ?>"></div>
			</div>

			<div class="masteriyo-course-started-date">
				<span><?php echo esc_html( 'Started on ' . masteriyo_format_datetime( $enrolled_course->progress->get_started_at(), 'M d, Y' ) ); ?></span>
			</div>
		</div>
	</div>
<?php endforeach; ?>
