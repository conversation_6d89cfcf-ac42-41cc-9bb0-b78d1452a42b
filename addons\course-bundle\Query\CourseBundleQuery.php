<?php
/**
 * Class for parameter-based course bundle querying
 *
 * @package  Masteriyo\Query
 * @version 2.12.0
 * @since   2.12.0
 */

namespace Masteriyo\Addons\CourseBundle\Query;

defined( 'ABSPATH' ) || exit;


use Masteriyo\Abstracts\ObjectQuery;
use Masteriyo\Enums\PostStatus;
use Masteriyo\PostType\PostType;

class CourseBundleQuery extends ObjectQuery {

	/**
	 * Valid query vars for courses.
	 *
	 * @since 2.12.0
	 *
	 * @return array
	 */
	protected function get_default_query_vars() {
		return array_merge(
			parent::get_default_query_vars(),
			array(
				'type'          => PostType::COURSE_BUNDLE,
				'status'        => array( PostStatus::DRAFT, PostStatus::PENDING, PostStatus::PVT, PostStatus::PUBLISH ),
				'include'       => array(),
				'date_created'  => '',
				'date_modified' => '',
				'price'         => '',
				'regular_price' => '',
				'sale_price'    => '',
				'course'        => '',
			)
		);
	}

	/**
	 * Get courses matching the current query vars.
	 *
	 * @since 2.12.0
	 *
	 * @return mixed Courses query result.
	 */
	public function get_course_bundles() {
		/**
		 * Filters course bundle object query args.
		 *
		 * @since 2.12.0
		 *
		 * @param array $query_args The object query args.
		 */
		$args    = apply_filters( 'masteriyo_course_bundle_object_query_args', $this->get_query_vars() );
		$results = masteriyo( 'course-bundle.store' )->query( $args );

		/**
		 * Filters course bundle object query results.
		 *
		 * @since 2.12.0
		 *
		 * @param Masteriyo\Models\Course[] $results The query results.
		 * @param array $query_args The object query args.
		 */
		return apply_filters( 'masteriyo_course_bundle_object_query', $results, $args );
	}
}
