<?php
/**
 * Class for parameter-based Grade querying
 *
 * @package  Masteriyo\Query
 * @version 2.5.20
 * @since   2.5.20
 */

namespace Masteriyo\Addons\Gradebook\Query;

use Masteriyo\Enums\PostStatus;
use Masteriyo\Abstracts\ObjectQuery;
use Masteriyo\PostType\PostType;

defined( 'ABSPATH' ) || exit;

/**
 * Grade query class.
 */
class GradeQuery extends ObjectQuery {

	/**
	 * Valid query vars for grades.
	 *
	 * @since 2.5.20
	 *
	 * @return array
	 */
	protected function get_default_query_vars() {
		return array_merge(
			parent::get_default_query_vars(),
			array(
				'type'   => PostType::GRADE,
				'author' => '',
				'status' => array( PostStatus::DRAFT, PostStatus::PENDING, PostStatus::PVT, PostStatus::PUBLISH ),
			)
		);
	}

	/**
	 * Get grades matching the current query vars.
	 *
	 * @since 2.5.20
	 *
	 * @return \Masteriyo\Addons\Gradebook\Models\Grade[] Grade objects
	 */
	public function get_grades() {
		/**
		 * Filters grade object query args.
		 *
		 * @since 2.5.20
		 *
		 * @param array $query_args The object query args.
		 */
		$args    = apply_filters( 'masteriyo_grade_object_query_args', $this->get_query_vars() );
		$results = masteriyo( 'grade.store' )->query( $args );

		/**
		 * Filters grade object query results.
		 *
		 * @since 2.5.20
		 *
		 * @param \Masteriyo\Addons\Gradebook\Models\Grade[] $results The query results.
		 * @param array $query_args The object query args.
		 */
		return apply_filters( 'masteriyo_grade_object_query', $results, $args );
	}
}
