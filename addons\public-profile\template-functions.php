<?php

defined( 'ABSPATH' ) || exit;

/**
 * Public Profile Template Functions.
 *
 * @since 2.6.8
 *
 * @package Masteriyo\Addons\PublicProfile
 */

use Masteriyo\Constants;
use Masteriyo\Roles;

if ( ! function_exists( 'masteriyo_public_profile_info' ) ) {
	/**
	 * Renders the info section in the public profile.
	 *
	 * This function includes the template file "partials/info.php" to display the info section in the public profile.
	 *
	 * @since 2.6.8
	 *
	 * @param array $data Profile data.
	 */
	function masteriyo_public_profile_info( $data ) {
		$role_title = masteriyo_get_user_role_title( $data['user_profile']['role'] );
		require Constants::get( 'MASTERIYO_PUBLIC_PROFILE_ADDON_TEMPLATES' ) . '/partials/info.php';
	}
}

if ( ! function_exists( 'masteriyo_public_profile_about' ) ) {
	/**
	 * Renders the about section in the public profile.
	 *
	 * This function includes the template file "partials/about.php" to display the about section in the public profile.
	 *
	 * @since 2.6.8
	 *
	 * @param array $data Profile data.
	 */
	function masteriyo_public_profile_about( $data ) {
		require Constants::get( 'MASTERIYO_PUBLIC_PROFILE_ADDON_TEMPLATES' ) . '/partials/about.php';
	}
}

if ( ! function_exists( 'masteriyo_public_profile_links' ) ) {
	/**
 * Renders the links section in the public profile.
 *
 * This function includes the template file "partials/links.php" to display the links section in the public profile.
 *
 * @since 2.6.8
 *
 * @param array $data Profile data.
 */
	function masteriyo_public_profile_links( $data ) {

		if ( ! array_filter(
			$data['user_profile']['links'],
			function( $link ) {
				return ! empty( $link );
			}
		) ) {
			return; // Return if all URLs are empty.
		}

		require Constants::get( 'MASTERIYO_PUBLIC_PROFILE_ADDON_TEMPLATES' ) . '/partials/links.php';
	}
}

if ( ! function_exists( 'masteriyo_public_profile_joined' ) ) {
	/**
	 * Renders the joined section in the public profile.
	 *
	 * This function includes the template file "partials/joined.php" to display the joined section in the public profile.
	 *
	 * @since 2.6.8
	 *
	 * @param array $data Profile data.
	 */
	function masteriyo_public_profile_joined( $data ) {
		require Constants::get( 'MASTERIYO_PUBLIC_PROFILE_ADDON_TEMPLATES' ) . '/partials/joined.php';
	}
}

if ( ! function_exists( 'masteriyo_public_profile_topbar' ) ) {
	/**
	 * Renders the top bar section in the public profile.
	 *
	 * This function checks the role of the user and includes the corresponding template file,
	 * either "instructor/topbar.php" or "student/topbar.php", to display the top bar section in the public profile.
	 *
	 * @since 2.6.8
	 *
	 * @param array $data Profile data.
	 */
	function masteriyo_public_profile_topbar( $data ) {
		if ( Roles::INSTRUCTOR === $data['user_profile']['role'] ) {
			require Constants::get( 'MASTERIYO_PUBLIC_PROFILE_ADDON_TEMPLATES' ) . '/instructor/topbar.php';
		} else {
			require Constants::get( 'MASTERIYO_PUBLIC_PROFILE_ADDON_TEMPLATES' ) . '/student/topbar.php';
		}
	}
}

if ( ! function_exists( 'masteriyo_public_profile_overview_cards' ) ) {
	/**
	 * Renders the overview cards section in the public profile.
	 *
	 * This function checks the role of the user and includes the corresponding template file,
	 * either "instructor/overview-cards.php" or "student/overview-cards.php", to display the overview cards section in the public profile.
	 *
	 * @since 2.6.8
	 *
	 * @param array $data Profile data.
	 */
	function masteriyo_public_profile_overview_cards( $data ) {
		if ( Roles::INSTRUCTOR === $data['user_profile']['role'] ) {
			require Constants::get( 'MASTERIYO_PUBLIC_PROFILE_ADDON_TEMPLATES' ) . '/instructor/overview-cards.php';
		} else {
			require Constants::get( 'MASTERIYO_PUBLIC_PROFILE_ADDON_TEMPLATES' ) . '/student/overview-cards.php';
		}
	}
}

if ( ! function_exists( 'masteriyo_public_profile_overview_courses' ) ) {
	/**
	 * Renders the overview courses section in the public profile.
	 *
	 * This function checks if the user is an instructor and includes the template file "overview-courses.php"
	 * to display the overview courses section in the public profile.
	 *
	 * @since 2.6.8
	 *
	 * @param array $data Profile data.
	 */
	function masteriyo_public_profile_overview_courses( $data ) {
		if ( Roles::INSTRUCTOR === $data['user_profile']['role'] ) {
			require Constants::get( 'MASTERIYO_PUBLIC_PROFILE_ADDON_TEMPLATES' ) . '/instructor/overview-courses.php';
		}
	}
}

if ( ! function_exists( 'masteriyo_public_profile_overview_enrolled_courses' ) ) {
	/**
	 * Renders the public profile overview of enrolled courses.
	 *
	 * This function includes the template file "overview-enrolled-courses.php" to display the enrolled courses section in the public profile.
	 *
	 * @since 2.6.8
	 *
	 * @param array $data Profile data.
	 */
	function masteriyo_public_profile_overview_enrolled_courses( $data ) {
		require Constants::get( 'MASTERIYO_PUBLIC_PROFILE_ADDON_TEMPLATES' ) . '/common/overview-enrolled-courses.php';
	}
}

if ( ! function_exists( 'masteriyo_public_profile_courses_offered_content' ) ) {
	/**
 * Renders the content for courses offered in the public profile.
 *
 * This function checks if the user is an instructor and includes the template file "courses-offered.php"
 * to display the courses offered section in the public profile.
 *
 * @since 2.6.8
 *
 * @param array $data Profile data.
 */
	function masteriyo_public_profile_courses_offered_content( $data ) {
		if ( Roles::INSTRUCTOR === $data['user_profile']['role'] ) {
			list( $offered_courses_list, $total_pages, $current_page ) = $data['courses_offered'];
			require Constants::get( 'MASTERIYO_PUBLIC_PROFILE_ADDON_TEMPLATES' ) . '/instructor/courses-offered.php';
		}
	}
}


if ( ! function_exists( 'masteriyo_public_profile_overview_courses_offered_pagination' ) ) {
	/**
	 * Renders the pagination for the enrolled courses section in the public profile.
	 *
	 * This function includes the template file "pagination.php" to display the pagination for the enrolled courses section in the public profile.
	 *
	 * @since 2.6.8
	 *
	 * @param int $total_pages The total number of pages.
	 * @param int $current_page The current page number.
	 */
	function masteriyo_public_profile_overview_courses_offered_pagination( $total_pages, $current_page ) {
		require Constants::get( 'MASTERIYO_PUBLIC_PROFILE_ADDON_TEMPLATES' ) . '/partials/pagination.php';
	}
}

if ( ! function_exists( 'masteriyo_public_profile_enrolled_courses' ) ) {
	/**
	 * Renders the content for enrolled courses in the public profile.
	 *
	 * This function includes the template file "enrolled-courses.php" to display the enrolled courses section in the public profile.
	 *
	 * @since 2.6.8
	 *
	 * @param array $data Profile data.
	 */
	function masteriyo_public_profile_enrolled_courses( $data ) {
		list( $enrolled_courses, $total_pages, $current_page ) = $data['enrolled_courses'];

		$user_id = $data['user_profile']['id'];

		require Constants::get( 'MASTERIYO_PUBLIC_PROFILE_ADDON_TEMPLATES' ) . '/common/enrolled-courses.php';
	}
}

if ( ! function_exists( 'masteriyo_public_profile_overview_enrolled_courses_gridcard' ) ) {
	/**
	 * Renders the grid card content for enrolled courses in the public profile.
	 *
	 * This function includes the template file "enrolled-courses-gridcard.php" to display the enrolled courses as grid cards in the public profile.
	 *
	 * @since 2.6.8
	 *
	 * @param array $enrolled_courses The array of enrolled courses data.
	 * @param int   $user_id          The user ID associated with the profile.
	 */
	function masteriyo_public_profile_overview_enrolled_courses_gridcard( $enrolled_courses, $user_id ) {

		if ( ! is_array( $enrolled_courses ) ) {
			$enrolled_courses = array( $enrolled_courses );
		}

		require Constants::get( 'MASTERIYO_PUBLIC_PROFILE_ADDON_TEMPLATES' ) . '/partials/enrolled-courses-gridcard.php';
	}
}

if ( ! function_exists( 'masteriyo_public_profile_overview_enrolled_courses_pagination' ) ) {
	/**
	 * Renders the pagination for enrolled courses in the public profile.
	 *
	 * This function includes the template file "pagination.php" to display the pagination for the enrolled courses section in the public profile.
	 *
	 * @since 2.6.8
	 *
	 * @param int $total_pages   The total number of pages.
	 * @param int $current_page  The current page number.
	 */
	function masteriyo_public_profile_overview_enrolled_courses_pagination( $total_pages, $current_page ) {
		require Constants::get( 'MASTERIYO_PUBLIC_PROFILE_ADDON_TEMPLATES' ) . '/partials/pagination.php';
	}
}

if ( ! function_exists( 'masteriyo_public_profile_certificates_content' ) ) {
	/**
	 * Renders the content for certificates in the public profile.
	 *
	 * This function includes the template file "certificates.php" to display the certificates section in the public profile.
	 *
	 * @since 2.6.8
	 *
	 * @param array $data Profile data.
	 */
	function masteriyo_public_profile_certificates_content( $data ) {
		$certificates = $data['certificates'];
		require Constants::get( 'MASTERIYO_PUBLIC_PROFILE_ADDON_TEMPLATES' ) . '/common/certificates.php';
	}
}
