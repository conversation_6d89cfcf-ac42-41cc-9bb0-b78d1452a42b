(self.webpackChunklearning_management_system_pro=self.webpackChunklearning_management_system_pro||[]).push([[520],{123:(n,t,u)=>{var r;!function(){var e=Object.assign||function(n){for(var t,u=1;u<arguments.length;u++)for(var r in t=arguments[u])g(t,r)&&(n[r]=t[r]);return n},i=Array.isArray||function(n){return"[object Array]"===Object.prototype.toString.call(n)},o=f((function(n){return 1===n?"χρόνος":"χρόνια"}),(function(n){return 1===n?"μήνας":"μήνες"}),(function(n){return 1===n?"εβδομάδα":"εβδομάδες"}),(function(n){return 1===n?"μέρα":"μέρες"}),(function(n){return 1===n?"ώρα":"ώρες"}),(function(n){return 1===n?"λεπτό":"λεπτά"}),(function(n){return 1===n?"δευτερόλεπτο":"δευτερόλεπτα"}),(function(n){return(1===n?"χιλιοστό":"χιλιοστά")+" του δευτερολέπτου"}),","),c={af:f("jaar",(function(n){return"maand"+(1===n?"":"e")}),(function(n){return 1===n?"week":"weke"}),(function(n){return 1===n?"dag":"dae"}),(function(n){return 1===n?"uur":"ure"}),(function(n){return 1===n?"minuut":"minute"}),(function(n){return"sekonde"+(1===n?"":"s")}),(function(n){return"millisekonde"+(1===n?"":"s")}),","),am:f("ዓመት","ወር","ሳምንት","ቀን","ሰዓት","ደቂቃ","ሰከንድ","ሚሊሰከንድ"),ar:e(f((function(n){return["سنة","سنتان","سنوات"][a(n)]}),(function(n){return["شهر","شهران","أشهر"][a(n)]}),(function(n){return["أسبوع","أسبوعين","أسابيع"][a(n)]}),(function(n){return["يوم","يومين","أيام"][a(n)]}),(function(n){return["ساعة","ساعتين","ساعات"][a(n)]}),(function(n){return["دقيقة","دقيقتان","دقائق"][a(n)]}),(function(n){return["ثانية","ثانيتان","ثواني"][a(n)]}),(function(n){return["جزء من الثانية","جزآن من الثانية","أجزاء من الثانية"][a(n)]}),","),{delimiter:" ﻭ ",_hideCountIf2:!0,_digitReplacements:["۰","١","٢","٣","٤","٥","٦","٧","٨","٩"]}),bg:f((function(n){return["години","година","години"][m(n)]}),(function(n){return["месеца","месец","месеца"][m(n)]}),(function(n){return["седмици","седмица","седмици"][m(n)]}),(function(n){return["дни","ден","дни"][m(n)]}),(function(n){return["часа","час","часа"][m(n)]}),(function(n){return["минути","минута","минути"][m(n)]}),(function(n){return["секунди","секунда","секунди"][m(n)]}),(function(n){return["милисекунди","милисекунда","милисекунди"][m(n)]}),","),bn:f("বছর","মাস","সপ্তাহ","দিন","ঘন্টা","মিনিট","সেকেন্ড","মিলিসেকেন্ড"),ca:f((function(n){return"any"+(1===n?"":"s")}),(function(n){return"mes"+(1===n?"":"os")}),(function(n){return"setman"+(1===n?"a":"es")}),(function(n){return"di"+(1===n?"a":"es")}),(function(n){return"hor"+(1===n?"a":"es")}),(function(n){return"minut"+(1===n?"":"s")}),(function(n){return"segon"+(1===n?"":"s")}),(function(n){return"milisegon"+(1===n?"":"s")}),","),ckb:f("ساڵ","مانگ","هەفتە","ڕۆژ","کاژێر","خولەک","چرکە","میلی چرکە","."),cs:f((function(n){return["rok","roku","roky","let"][d(n)]}),(function(n){return["měsíc","měsíce","měsíce","měsíců"][d(n)]}),(function(n){return["týden","týdne","týdny","týdnů"][d(n)]}),(function(n){return["den","dne","dny","dní"][d(n)]}),(function(n){return["hodina","hodiny","hodiny","hodin"][d(n)]}),(function(n){return["minuta","minuty","minuty","minut"][d(n)]}),(function(n){return["sekunda","sekundy","sekundy","sekund"][d(n)]}),(function(n){return["milisekunda","milisekundy","milisekundy","milisekund"][d(n)]}),","),cy:f("flwyddyn","mis","wythnos","diwrnod","awr","munud","eiliad","milieiliad"),da:f("år",(function(n){return"måned"+(1===n?"":"er")}),(function(n){return"uge"+(1===n?"":"r")}),(function(n){return"dag"+(1===n?"":"e")}),(function(n){return"time"+(1===n?"":"r")}),(function(n){return"minut"+(1===n?"":"ter")}),(function(n){return"sekund"+(1===n?"":"er")}),(function(n){return"millisekund"+(1===n?"":"er")}),","),de:f((function(n){return"Jahr"+(1===n?"":"e")}),(function(n){return"Monat"+(1===n?"":"e")}),(function(n){return"Woche"+(1===n?"":"n")}),(function(n){return"Tag"+(1===n?"":"e")}),(function(n){return"Stunde"+(1===n?"":"n")}),(function(n){return"Minute"+(1===n?"":"n")}),(function(n){return"Sekunde"+(1===n?"":"n")}),(function(n){return"Millisekunde"+(1===n?"":"n")}),","),el:o,en:f((function(n){return"year"+(1===n?"":"s")}),(function(n){return"month"+(1===n?"":"s")}),(function(n){return"week"+(1===n?"":"s")}),(function(n){return"day"+(1===n?"":"s")}),(function(n){return"hour"+(1===n?"":"s")}),(function(n){return"minute"+(1===n?"":"s")}),(function(n){return"second"+(1===n?"":"s")}),(function(n){return"millisecond"+(1===n?"":"s")})),eo:f((function(n){return"jaro"+(1===n?"":"j")}),(function(n){return"monato"+(1===n?"":"j")}),(function(n){return"semajno"+(1===n?"":"j")}),(function(n){return"tago"+(1===n?"":"j")}),(function(n){return"horo"+(1===n?"":"j")}),(function(n){return"minuto"+(1===n?"":"j")}),(function(n){return"sekundo"+(1===n?"":"j")}),(function(n){return"milisekundo"+(1===n?"":"j")}),","),es:f((function(n){return"año"+(1===n?"":"s")}),(function(n){return"mes"+(1===n?"":"es")}),(function(n){return"semana"+(1===n?"":"s")}),(function(n){return"día"+(1===n?"":"s")}),(function(n){return"hora"+(1===n?"":"s")}),(function(n){return"minuto"+(1===n?"":"s")}),(function(n){return"segundo"+(1===n?"":"s")}),(function(n){return"milisegundo"+(1===n?"":"s")}),","),et:f((function(n){return"aasta"+(1===n?"":"t")}),(function(n){return"kuu"+(1===n?"":"d")}),(function(n){return"nädal"+(1===n?"":"at")}),(function(n){return"päev"+(1===n?"":"a")}),(function(n){return"tund"+(1===n?"":"i")}),(function(n){return"minut"+(1===n?"":"it")}),(function(n){return"sekund"+(1===n?"":"it")}),(function(n){return"millisekund"+(1===n?"":"it")}),","),eu:f("urte","hilabete","aste","egun","ordu","minutu","segundo","milisegundo",","),fa:f("سال","ماه","هفته","روز","ساعت","دقیقه","ثانیه","میلی ثانیه"),fi:f((function(n){return 1===n?"vuosi":"vuotta"}),(function(n){return 1===n?"kuukausi":"kuukautta"}),(function(n){return"viikko"+(1===n?"":"a")}),(function(n){return"päivä"+(1===n?"":"ä")}),(function(n){return"tunti"+(1===n?"":"a")}),(function(n){return"minuutti"+(1===n?"":"a")}),(function(n){return"sekunti"+(1===n?"":"a")}),(function(n){return"millisekunti"+(1===n?"":"a")}),","),fo:f("ár",(function(n){return 1===n?"mánaður":"mánaðir"}),(function(n){return 1===n?"vika":"vikur"}),(function(n){return 1===n?"dagur":"dagar"}),(function(n){return 1===n?"tími":"tímar"}),(function(n){return 1===n?"minuttur":"minuttir"}),"sekund","millisekund",","),fr:f((function(n){return"an"+(n>=2?"s":"")}),"mois",(function(n){return"semaine"+(n>=2?"s":"")}),(function(n){return"jour"+(n>=2?"s":"")}),(function(n){return"heure"+(n>=2?"s":"")}),(function(n){return"minute"+(n>=2?"s":"")}),(function(n){return"seconde"+(n>=2?"s":"")}),(function(n){return"milliseconde"+(n>=2?"s":"")}),","),gr:o,he:f((function(n){return 1===n?"שנה":"שנים"}),(function(n){return 1===n?"חודש":"חודשים"}),(function(n){return 1===n?"שבוע":"שבועות"}),(function(n){return 1===n?"יום":"ימים"}),(function(n){return 1===n?"שעה":"שעות"}),(function(n){return 1===n?"דקה":"דקות"}),(function(n){return 1===n?"שניה":"שניות"}),(function(n){return 1===n?"מילישנייה":"מילישניות"})),hr:f((function(n){return n%10==2||n%10==3||n%10==4?"godine":"godina"}),(function(n){return 1===n?"mjesec":2===n||3===n||4===n?"mjeseca":"mjeseci"}),(function(n){return n%10==1&&11!==n?"tjedan":"tjedna"}),(function(n){return 1===n?"dan":"dana"}),(function(n){return 1===n?"sat":2===n||3===n||4===n?"sata":"sati"}),(function(n){var t=n%10;return 2!==t&&3!==t&&4!==t||!(n<10||n>14)?"minuta":"minute"}),(function(n){var t=n%10;return 5===t||Math.floor(n)===n&&n>=10&&n<=19?"sekundi":1===t?"sekunda":2===t||3===t||4===t?"sekunde":"sekundi"}),(function(n){return 1===n?"milisekunda":n%10==2||n%10==3||n%10==4?"milisekunde":"milisekundi"}),","),hi:f("साल",(function(n){return 1===n?"महीना":"महीने"}),(function(n){return 1===n?"हफ़्ता":"हफ्ते"}),"दिन",(function(n){return 1===n?"घंटा":"घंटे"}),"मिनट","सेकंड","मिलीसेकंड"),hu:f("év","hónap","hét","nap","óra","perc","másodperc","ezredmásodperc",","),id:f("tahun","bulan","minggu","hari","jam","menit","detik","milidetik"),is:f("ár",(function(n){return"mánuð"+(1===n?"ur":"ir")}),(function(n){return"vik"+(1===n?"a":"ur")}),(function(n){return"dag"+(1===n?"ur":"ar")}),(function(n){return"klukkutím"+(1===n?"i":"ar")}),(function(n){return"mínút"+(1===n?"a":"ur")}),(function(n){return"sekúnd"+(1===n?"a":"ur")}),(function(n){return"millisekúnd"+(1===n?"a":"ur")})),it:f((function(n){return"ann"+(1===n?"o":"i")}),(function(n){return"mes"+(1===n?"e":"i")}),(function(n){return"settiman"+(1===n?"a":"e")}),(function(n){return"giorn"+(1===n?"o":"i")}),(function(n){return"or"+(1===n?"a":"e")}),(function(n){return"minut"+(1===n?"o":"i")}),(function(n){return"second"+(1===n?"o":"i")}),(function(n){return"millisecond"+(1===n?"o":"i")}),","),ja:f("年","ヶ月","週","日","時間","分","秒","ミリ秒"),km:f("ឆ្នាំ","ខែ","សប្តាហ៍","ថ្ងៃ","ម៉ោង","នាទី","វិនាទី","មិល្លីវិនាទី"),kn:f((function(n){return 1===n?"ವರ್ಷ":"ವರ್ಷಗಳು"}),(function(n){return 1===n?"ತಿಂಗಳು":"ತಿಂಗಳುಗಳು"}),(function(n){return 1===n?"ವಾರ":"ವಾರಗಳು"}),(function(n){return 1===n?"ದಿನ":"ದಿನಗಳು"}),(function(n){return 1===n?"ಗಂಟೆ":"ಗಂಟೆಗಳು"}),(function(n){return 1===n?"ನಿಮಿಷ":"ನಿಮಿಷಗಳು"}),(function(n){return 1===n?"ಸೆಕೆಂಡ್":"ಸೆಕೆಂಡುಗಳು"}),(function(n){return 1===n?"ಮಿಲಿಸೆಕೆಂಡ್":"ಮಿಲಿಸೆಕೆಂಡುಗಳು"})),ko:f("년","개월","주일","일","시간","분","초","밀리 초"),ku:f("sal","meh","hefte","roj","seet","deqe","saniye","mîlîçirk",","),lo:f("ປີ","ເດືອນ","ອາທິດ","ມື້","ຊົ່ວໂມງ","ນາທີ","ວິນາທີ","ມິນລິວິນາທີ",","),lt:f((function(n){return n%10==0||n%100>=10&&n%100<=20?"metų":"metai"}),(function(n){return["mėnuo","mėnesiai","mėnesių"][l(n)]}),(function(n){return["savaitė","savaitės","savaičių"][l(n)]}),(function(n){return["diena","dienos","dienų"][l(n)]}),(function(n){return["valanda","valandos","valandų"][l(n)]}),(function(n){return["minutė","minutės","minučių"][l(n)]}),(function(n){return["sekundė","sekundės","sekundžių"][l(n)]}),(function(n){return["milisekundė","milisekundės","milisekundžių"][l(n)]}),","),lv:f((function(n){return k(n)?"gads":"gadi"}),(function(n){return k(n)?"mēnesis":"mēneši"}),(function(n){return k(n)?"nedēļa":"nedēļas"}),(function(n){return k(n)?"diena":"dienas"}),(function(n){return k(n)?"stunda":"stundas"}),(function(n){return k(n)?"minūte":"minūtes"}),(function(n){return k(n)?"sekunde":"sekundes"}),(function(n){return k(n)?"milisekunde":"milisekundes"}),","),mk:f((function(n){return 1===n?"година":"години"}),(function(n){return 1===n?"месец":"месеци"}),(function(n){return 1===n?"недела":"недели"}),(function(n){return 1===n?"ден":"дена"}),(function(n){return 1===n?"час":"часа"}),(function(n){return 1===n?"минута":"минути"}),(function(n){return 1===n?"секунда":"секунди"}),(function(n){return 1===n?"милисекунда":"милисекунди"}),","),mn:f("жил","сар","долоо хоног","өдөр","цаг","минут","секунд","миллисекунд"),mr:f((function(n){return 1===n?"वर्ष":"वर्षे"}),(function(n){return 1===n?"महिना":"महिने"}),(function(n){return 1===n?"आठवडा":"आठवडे"}),"दिवस","तास",(function(n){return 1===n?"मिनिट":"मिनिटे"}),"सेकंद","मिलिसेकंद"),ms:f("tahun","bulan","minggu","hari","jam","minit","saat","milisaat"),nl:f("jaar",(function(n){return 1===n?"maand":"maanden"}),(function(n){return 1===n?"week":"weken"}),(function(n){return 1===n?"dag":"dagen"}),"uur",(function(n){return 1===n?"minuut":"minuten"}),(function(n){return 1===n?"seconde":"seconden"}),(function(n){return 1===n?"milliseconde":"milliseconden"}),","),no:f("år",(function(n){return"måned"+(1===n?"":"er")}),(function(n){return"uke"+(1===n?"":"r")}),(function(n){return"dag"+(1===n?"":"er")}),(function(n){return"time"+(1===n?"":"r")}),(function(n){return"minutt"+(1===n?"":"er")}),(function(n){return"sekund"+(1===n?"":"er")}),(function(n){return"millisekund"+(1===n?"":"er")}),","),pl:f((function(n){return["rok","roku","lata","lat"][s(n)]}),(function(n){return["miesiąc","miesiąca","miesiące","miesięcy"][s(n)]}),(function(n){return["tydzień","tygodnia","tygodnie","tygodni"][s(n)]}),(function(n){return["dzień","dnia","dni","dni"][s(n)]}),(function(n){return["godzina","godziny","godziny","godzin"][s(n)]}),(function(n){return["minuta","minuty","minuty","minut"][s(n)]}),(function(n){return["sekunda","sekundy","sekundy","sekund"][s(n)]}),(function(n){return["milisekunda","milisekundy","milisekundy","milisekund"][s(n)]}),","),pt:f((function(n){return"ano"+(1===n?"":"s")}),(function(n){return 1===n?"mês":"meses"}),(function(n){return"semana"+(1===n?"":"s")}),(function(n){return"dia"+(1===n?"":"s")}),(function(n){return"hora"+(1===n?"":"s")}),(function(n){return"minuto"+(1===n?"":"s")}),(function(n){return"segundo"+(1===n?"":"s")}),(function(n){return"milissegundo"+(1===n?"":"s")}),","),ro:f((function(n){return 1===n?"an":"ani"}),(function(n){return 1===n?"lună":"luni"}),(function(n){return 1===n?"săptămână":"săptămâni"}),(function(n){return 1===n?"zi":"zile"}),(function(n){return 1===n?"oră":"ore"}),(function(n){return 1===n?"minut":"minute"}),(function(n){return 1===n?"secundă":"secunde"}),(function(n){return 1===n?"milisecundă":"milisecunde"}),","),ru:f((function(n){return["лет","год","года"][m(n)]}),(function(n){return["месяцев","месяц","месяца"][m(n)]}),(function(n){return["недель","неделя","недели"][m(n)]}),(function(n){return["дней","день","дня"][m(n)]}),(function(n){return["часов","час","часа"][m(n)]}),(function(n){return["минут","минута","минуты"][m(n)]}),(function(n){return["секунд","секунда","секунды"][m(n)]}),(function(n){return["миллисекунд","миллисекунда","миллисекунды"][m(n)]}),","),sq:f((function(n){return 1===n?"vit":"vjet"}),"muaj","javë","ditë","orë",(function(n){return"minut"+(1===n?"ë":"a")}),(function(n){return"sekond"+(1===n?"ë":"a")}),(function(n){return"milisekond"+(1===n?"ë":"a")}),","),sr:f((function(n){return["години","година","године"][m(n)]}),(function(n){return["месеци","месец","месеца"][m(n)]}),(function(n){return["недељи","недеља","недеље"][m(n)]}),(function(n){return["дани","дан","дана"][m(n)]}),(function(n){return["сати","сат","сата"][m(n)]}),(function(n){return["минута","минут","минута"][m(n)]}),(function(n){return["секунди","секунда","секунде"][m(n)]}),(function(n){return["милисекунди","милисекунда","милисекунде"][m(n)]}),","),ta:f((function(n){return 1===n?"வருடம்":"ஆண்டுகள்"}),(function(n){return 1===n?"மாதம்":"மாதங்கள்"}),(function(n){return 1===n?"வாரம்":"வாரங்கள்"}),(function(n){return 1===n?"நாள்":"நாட்கள்"}),(function(n){return 1===n?"மணி":"மணிநேரம்"}),(function(n){return"நிமிட"+(1===n?"ம்":"ங்கள்")}),(function(n){return"வினாடி"+(1===n?"":"கள்")}),(function(n){return"மில்லி விநாடி"+(1===n?"":"கள்")})),te:f((function(n){return"సంవత్స"+(1===n?"రం":"రాల")}),(function(n){return"నెల"+(1===n?"":"ల")}),(function(n){return 1===n?"వారం":"వారాలు"}),(function(n){return"రోజు"+(1===n?"":"లు")}),(function(n){return"గంట"+(1===n?"":"లు")}),(function(n){return 1===n?"నిమిషం":"నిమిషాలు"}),(function(n){return 1===n?"సెకను":"సెకన్లు"}),(function(n){return 1===n?"మిల్లీసెకన్":"మిల్లీసెకన్లు"})),uk:f((function(n){return["років","рік","роки"][m(n)]}),(function(n){return["місяців","місяць","місяці"][m(n)]}),(function(n){return["тижнів","тиждень","тижні"][m(n)]}),(function(n){return["днів","день","дні"][m(n)]}),(function(n){return["годин","година","години"][m(n)]}),(function(n){return["хвилин","хвилина","хвилини"][m(n)]}),(function(n){return["секунд","секунда","секунди"][m(n)]}),(function(n){return["мілісекунд","мілісекунда","мілісекунди"][m(n)]}),","),ur:f("سال",(function(n){return 1===n?"مہینہ":"مہینے"}),(function(n){return 1===n?"ہفتہ":"ہفتے"}),"دن",(function(n){return 1===n?"گھنٹہ":"گھنٹے"}),"منٹ","سیکنڈ","ملی سیکنڈ"),sk:f((function(n){return["rok","roky","roky","rokov"][d(n)]}),(function(n){return["mesiac","mesiace","mesiace","mesiacov"][d(n)]}),(function(n){return["týždeň","týždne","týždne","týždňov"][d(n)]}),(function(n){return["deň","dni","dni","dní"][d(n)]}),(function(n){return["hodina","hodiny","hodiny","hodín"][d(n)]}),(function(n){return["minúta","minúty","minúty","minút"][d(n)]}),(function(n){return["sekunda","sekundy","sekundy","sekúnd"][d(n)]}),(function(n){return["milisekunda","milisekundy","milisekundy","milisekúnd"][d(n)]}),","),sl:f((function(n){return n%10==1?"leto":n%100==2?"leti":n%100==3||n%100==4||Math.floor(n)!==n&&n%100<=5?"leta":"let"}),(function(n){return n%10==1?"mesec":n%100==2||Math.floor(n)!==n&&n%100<=5?"meseca":n%10==3||n%10==4?"mesece":"mesecev"}),(function(n){return n%10==1?"teden":n%10==2||Math.floor(n)!==n&&n%100<=4?"tedna":n%10==3||n%10==4?"tedne":"tednov"}),(function(n){return n%100==1?"dan":"dni"}),(function(n){return n%10==1?"ura":n%100==2?"uri":n%10==3||n%10==4||Math.floor(n)!==n?"ure":"ur"}),(function(n){return n%10==1?"minuta":n%10==2?"minuti":n%10==3||n%10==4||Math.floor(n)!==n&&n%100<=4?"minute":"minut"}),(function(n){return n%10==1?"sekunda":n%100==2?"sekundi":n%100==3||n%100==4||Math.floor(n)!==n?"sekunde":"sekund"}),(function(n){return n%10==1?"milisekunda":n%100==2?"milisekundi":n%100==3||n%100==4||Math.floor(n)!==n?"milisekunde":"milisekund"}),","),sv:f("år",(function(n){return"månad"+(1===n?"":"er")}),(function(n){return"veck"+(1===n?"a":"or")}),(function(n){return"dag"+(1===n?"":"ar")}),(function(n){return"timm"+(1===n?"e":"ar")}),(function(n){return"minut"+(1===n?"":"er")}),(function(n){return"sekund"+(1===n?"":"er")}),(function(n){return"millisekund"+(1===n?"":"er")}),","),sw:e(f((function(n){return 1===n?"mwaka":"miaka"}),(function(n){return 1===n?"mwezi":"miezi"}),"wiki",(function(n){return 1===n?"siku":"masiku"}),(function(n){return 1===n?"saa":"masaa"}),"dakika","sekunde","milisekunde"),{_numberFirst:!0}),tr:f("yıl","ay","hafta","gün","saat","dakika","saniye","milisaniye",","),th:f("ปี","เดือน","สัปดาห์","วัน","ชั่วโมง","นาที","วินาที","มิลลิวินาที"),uz:f("yil","oy","hafta","kun","soat","minut","sekund","millisekund"),uz_CYR:f("йил","ой","ҳафта","кун","соат","минут","секунд","миллисекунд"),vi:f("năm","tháng","tuần","ngày","giờ","phút","giây","mili giây",","),zh_CN:f("年","个月","周","天","小时","分钟","秒","毫秒"),zh_TW:f("年","個月","周","天","小時","分鐘","秒","毫秒")};function f(n,t,u,r,e,i,o,c,f){var a={y:n,mo:t,w:u,d:r,h:e,m:i,s:o,ms:c};return void 0!==f&&(a.decimal=f),a}function a(n){return 2===n?1:n>2&&n<11?2:0}function s(n){return 1===n?0:Math.floor(n)!==n?1:n%10>=2&&n%10<=4&&!(n%100>10&&n%100<20)?2:3}function m(n){return Math.floor(n)!==n?2:n%100>=5&&n%100<=20||n%10>=5&&n%10<=9||n%10==0?0:n%10==1?1:n>1?2:0}function d(n){return 1===n?0:Math.floor(n)!==n?1:n%10>=2&&n%10<=4&&n%100<10?2:3}function l(n){return 1===n||n%10==1&&n%100>20?0:Math.floor(n)!==n||n%10>=2&&n%100>20||n%10>=2&&n%100<10?1:2}function k(n){return n%10==1&&n%100!=11}function g(n,t){return Object.prototype.hasOwnProperty.call(n,t)}function h(n,t,u){var r,e,i,o=n.unitName,c=n.unitCount,f=u.spacer,a=u.maxDecimalPoints;r=g(u,"decimal")?u.decimal:g(t,"decimal")?t.decimal:".","digitReplacements"in u?e=u.digitReplacements:"_digitReplacements"in t&&(e=t._digitReplacements);var s=(void 0===a?c:Math.floor(c*Math.pow(10,a))/Math.pow(10,a)).toString();if(t._hideCountIf2&&2===c)i="",f="";else if(e){i="";for(var m=0;m<s.length;m++){var d=s[m];i+="."===d?r:e[d]}}else i=s.replace(".",r);var l,k=t[o];return l="function"==typeof k?k(c):k,t._numberFirst?l+f+i:i+f+l}function y(n,t){var u=function(n){var t=[n.language];if(g(n,"fallbacks")){if(!i(n.fallbacks)||!n.fallbacks.length)throw new Error("fallbacks must be an array with at least one element");t=t.concat(n.fallbacks)}for(var u=0;u<t.length;u++){var r=t[u];if(g(n.languages,r))return n.languages[r];if(g(c,r))return c[r]}throw new Error("No language found.")}(t);if(!n.length){var r=t.units;return h({unitName:r[r.length-1],unitCount:0},u,t)}var e,o=t.conjunction,f=t.serialComma;e=g(t,"delimiter")?t.delimiter:g(u,"delimiter")?u.delimiter:", ";for(var a=[],s=0;s<n.length;s++)a.push(h(n[s],u,t));return o&&1!==n.length?2===n.length?a.join(o):a.slice(0,-1).join(e)+(f?",":"")+o+a.slice(-1):a.join(e)}function v(n){var t=function(n,u){n=Math.abs(n);var r=e({},t,u||{}),i=function(n,t){var u,r,e,i,o=t.units,c=t.unitMeasures,f="largest"in t?t.largest:1/0;if(!o.length)return[];var a={};for(i=n,r=0;r<o.length;r++){var s=c[u=o[r]];e=r===o.length-1?i/s:Math.floor(i/s),a[u]=e,i-=e*s}if(t.round){var m=f;for(r=0;r<o.length;r++)if(0!==(e=a[u=o[r]])&&0==--m){for(var d=r+1;d<o.length;d++){var l=o[d],k=a[l];a[u]+=k*c[l]/c[u],a[l]=0}break}for(r=o.length-1;r>=0;r--)if(0!==(e=a[u=o[r]])){var g=Math.round(e);if(a[u]=g,0===r)break;var h=o[r-1],y=c[h],v=Math.floor(g*c[u]/y);if(!v)break;a[h]+=v,a[u]=0}}var p=[];for(r=0;r<o.length&&p.length<f;r++)(e=a[u=o[r]])&&p.push({unitName:u,unitCount:e});return p}(n,r);return y(i,r)};return e(t,{language:"en",spacer:" ",conjunction:"",serialComma:!0,units:["y","mo","w","d","h","m","s"],languages:{},round:!1,unitMeasures:{y:315576e5,mo:26298e5,w:6048e5,d:864e5,h:36e5,m:6e4,s:1e3,ms:1}},n)}var p=e(v({}),{getSupportedLanguages:function(){var n=[];for(var t in c)g(c,t)&&"gr"!==t&&n.push(t);return n},humanizer:v});void 0===(r=function(){return p}.call(t,u,t,n))||(n.exports=r)}()},705:(n,t,u)=>{"use strict";function r(n){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},r(n)}function e(n){var t=function(n,t){if("object"!=r(n)||!n)return n;var u=n[Symbol.toPrimitive];if(void 0!==u){var e=u.call(n,t||"default");if("object"!=r(e))return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(n)}(n,"string");return"symbol"==r(t)?t:String(t)}function i(n,t,u){return(t=e(t))in n?Object.defineProperty(n,t,{value:u,enumerable:!0,configurable:!0,writable:!0}):n[t]=u,n}u.d(t,{A:()=>i})}}]);