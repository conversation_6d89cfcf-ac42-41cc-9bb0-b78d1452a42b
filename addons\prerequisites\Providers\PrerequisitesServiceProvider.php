<?php
/**
 * Prerequisites service provider.
 *
 * @since 2.3.2
 */

namespace Masteriyo\Addons\Prerequisites\Providers;

defined( 'ABSPATH' ) || exit;

use Masteriyo\Addons\Prerequisites\PrerequisitesAddon;
use League\Container\ServiceProvider\AbstractServiceProvider;

/**
 * Prerequisites service provider.
 *
 * @since 2.3.2
 */
class PrerequisitesServiceProvider extends AbstractServiceProvider {
	/**
	 * The provided array is a way to let the container
	 * know that a service is provided by this service
	 * provider. Every service that is registered via
	 * this service provider must have an alias added
	 * to this array or it will be ignored
	 *
	 * @since 2.3.2
	 *
	 * @var array
	 */
	protected $provides = array(
		'addons.prerequisites',
		PrerequisitesAddon::class,
	);

	/**
	 * This is where the magic happens, within the method you can
	 * access the container and register or retrieve anything
	 * that you need to, but remember, every alias registered
	 * within this method must be declared in the `$provides` array.
	 *
	 * @since 2.3.2
	 */
	public function register() {
		$this->getContainer()->add( 'addons.prerequisites', PrerequisitesAddon::class, true );
	}
}
