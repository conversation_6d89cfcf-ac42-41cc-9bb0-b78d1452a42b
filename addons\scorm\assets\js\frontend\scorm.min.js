var SCORM=pipwerks.SCORM,SCORM_API={CourseId:null,LastError:0,Initialized:!1,Data:{},LMSProgress:{},Initialize:function(){return this.ModuleRunning=!0,this.Initialized=!0,this.LMSProgress={},!0},Terminate:function(){var t=this.CourseId;return this.ModuleRunning=!1,this.Initialized=!1,this.CourseId=null,this.Data={},SCORM.connection.isActive=!1,localStorage.getItem("course_"+t+"_completed")||(localStorage.setItem("course_"+t+"_completed","true"),location.reload(!0)),this.LMSProgress.hasOwnProperty("lesson_status")&&this.LMSProgress&&this.LMSProgress.hasOwnProperty("completion_status")&&this.LMSProgress.hasOwnProperty("success_status")&&("passed"==this.LMSProgress.lesson_status||"completed"==this.LMSProgress.completion_status&&"passed"==this.LMSProgress.success_status)&&location.reload(!0),this.LMSProgress={},"true"},GetValue:function(t){return this.LastError=0,this.Initialized?void 0!==this.Data[t]?this.Data[t]:"":(this.LastError=scormErrors.GetValueBeforeInit,"")},SetValue:function(t,r){return this.LastError=0,this.Initialized?(this.Data[t]=r,"true"):(this.LastError=scormErrors.SetValueBeforeInit,"")},Commit:function(){var t,r,s;return this.LastError=0,this.CourseId?(t=_MASTERIYO_SCORM_COURSE_.restUrl+"/course_progress/"+this.CourseId,r=this.Data,s=this,jQuery.ajax({url:t,type:"POST",dataType:"json",contentType:"application/json",data:JSON.stringify(r),beforeSend:function(t){t.setRequestHeader("X-WP-Nonce",_MASTERIYO_SCORM_COURSE_.wp_rest_nonce)},success:function(t){s.LMSProgress=t}}),"true"):"false"},GetLastError:function(){var t=this.LastError;return this.LastError=0,t},GetErrorString:function(t){return"Error: "+t},GetDiagnostic:function(){var t="Diagnostic: "+this.LastError;return this.LastError=0,t},LMSInitialize:function(){return this.Initialize()},LMSFinish:function(){return this.Terminate()},LMSGetValue:function(t){return this.GetValue(t)},LMSSetValue:function(t,r){return this.SetValue(t,r)},LMSCommit:function(){return this.Commit()},LMSGetLastError:function(){return this.GetLastError()},LMSGetErrorString:function(){return this.GetErrorString()},LMSGetDiagnostic:function(){return this.GetDiagnostic()}},API_1484_11=null,API=null;async function initLms(e,i,n,r){var t=r.restUrl+"/course_progress/"+e;await jQuery.ajax({url:t,type:"GET",dataType:"json",contentType:"application/json",beforeSend:function(t){t.setRequestHeader("X-WP-Nonce",r.wp_rest_nonce)},success:function(t){SCORM.version=i,"1.2"===SCORM.version?(API_1484_11=null,API=SCORM_API):(API=null,API_1484_11=SCORM_API),jQuery("iframe#masteriyo-scorm-course-iframe").attr("src",n);SCORM.init();var r=SCORM.API.get();if(r.Data={},r.CourseId=e,t.hasOwnProperty("cmi.suspend_data")&&""!==t["cmi.suspend_data"])for(var s in t)t.hasOwnProperty(s)&&(r.Data[s]=t[s])}})}jQuery(document).ready(function(t){t=t("iframe#masteriyo-scorm-course-iframe");0<t.length&&initLms(t.data("course-id"),t.data("scorm-version").toString(),t.data("src"),_MASTERIYO_SCORM_COURSE_)});