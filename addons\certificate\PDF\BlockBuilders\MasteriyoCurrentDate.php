<?php
/**
 * Current date block builder.
 *
 * @since 2.14.0
 */

namespace Masteriyo\Addons\Certificate\PDF\BlockBuilders;

defined( 'ABSPATH' ) || exit;


class MasteriyoCurrentDate extends BlockBuilder {

	/**
	 * Build and return the block HTML.
	 *
	 * @since 2.14.0
	 *
	 * @return string
	 */
	public function build() {
		$pdf          = $this->get_pdf();
		$current_date = $pdf->is_preview() ? __( 'Current Date', 'learning-management-system' ) : '';
		$block_data   = $this->get_block_data();
		$date_format  = masteriyo_array_get( $block_data, 'attrs.dateFormat' );
		$date_format  = empty( $date_format ) ? 'F j, Y' : $date_format;

		$current_date = gmdate( $date_format, strtotime( 'now' ) );

		/**
		 * Filters the current date displayed on the certificate.
		 *
		 * @since 2.14.0
		 *
		 * @param string $current_date The current date.
		 * @param string $date_format  The date format.
		 *
		 * @return string The filtered current date.
		 */
		$current_date = apply_filters( 'masteriyo_certificate_current_date', $current_date, $date_format );

		$html  = str_replace( '{{masteriyo_current_date}}', $current_date, $block_data['innerHTML'] );
		$html .= '<style>' . masteriyo_array_get( $block_data, 'attrs.blockCSS', '' ) . '</style>';
		return $html;
	}
}
