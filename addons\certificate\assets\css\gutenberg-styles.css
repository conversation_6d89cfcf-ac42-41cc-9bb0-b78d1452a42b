.wp-block-image {
	margin-bottom: 0;
}

.wp-block-image img {
	max-width: 100%;
}

.wp-block-image.aligncenter {
	text-align: center;
}

.wp-block-image.alignfull img,
.wp-block-image.alignwide img {
	width: 100%;
}

.wp-block-image .alignleft,
.wp-block-image .alignright,
.wp-block-image .aligncenter {
	display: table;
}

.wp-block-image .alignleft > figcaption,
.wp-block-image .alignright > figcaption,
.wp-block-image .aligncenter > figcaption {
	display: table-caption;
	caption-side: bottom;
}

.wp-block-image .alignleft {
	/*rtl:ignore*/
	float: left;
	/*rtl:ignore*/
	margin-left: 0;
	margin-right: 1em;
	margin-top: 0.5em;
	margin-bottom: 0.5em;
}

.wp-block-image .alignright {
	/*rtl:ignore*/
	float: right;
	/*rtl:ignore*/
	margin-right: 0;
	margin-left: 1em;
	margin-top: 0.5em;
	margin-bottom: 0.5em;
}

.aligncenter {
	margin-left: auto;
	margin-right: auto;
}

.wp-block-image figcaption {
	margin-top: 0.5em;
	margin-bottom: 1em;
}

.is-style-rounded img {
	border-radius: 9999px;
}

.is-style-circle-mask img {
	border-radius: 9999px;
}

ol.has-background,
ul.has-background {
	padding: 1.25em 2.375em;
}

.wp-block-media-text {
	/*!rtl:begin:ignore*/
	direction: ltr;
	/*!rtl:end:ignore*/
	display: -ms-grid;
	display: grid;
	-ms-grid-columns: 50% 1fr;
	grid-template-columns: 50% 1fr;
	-ms-grid-rows: auto;
	grid-template-rows: auto;
}

.wp-block-media-text.has-media-on-the-right {
	-ms-grid-columns: 1fr 50%;
	grid-template-columns: 1fr 50%;
}

.wp-block-media-text.is-vertically-aligned-top .wp-block-media-text__content,
.wp-block-media-text.is-vertically-aligned-top .wp-block-media-text__media {
	-ms-grid-row-align: start;
	align-self: start;
}

.wp-block-media-text .wp-block-media-text__content,
.wp-block-media-text .wp-block-media-text__media,
.wp-block-media-text.is-vertically-aligned-center .wp-block-media-text__content,
.wp-block-media-text.is-vertically-aligned-center .wp-block-media-text__media {
	-ms-grid-row-align: center;
	align-self: center;
}

.wp-block-media-text.is-vertically-aligned-bottom .wp-block-media-text__content,
.wp-block-media-text.is-vertically-aligned-bottom .wp-block-media-text__media {
	-ms-grid-row-align: end;
	align-self: end;
}

.wp-block-media-text .wp-block-media-text__media {
	/*!rtl:begin:ignore*/
	-ms-grid-column: 1;
	grid-column: 1;
	-ms-grid-row: 1;
	grid-row: 1;
	/*!rtl:end:ignore*/
	margin: 0;
}

.wp-block-media-text .wp-block-media-text__content {
	direction: ltr;
	/*!rtl:begin:ignore*/
	-ms-grid-column: 2;
	grid-column: 2;
	-ms-grid-row: 1;
	grid-row: 1;
	/*!rtl:end:ignore*/
	padding: 0 8% 0 8%;
	word-break: break-word;
}

.wp-block-media-text.has-media-on-the-right .wp-block-media-text__media {
	/*!rtl:begin:ignore*/
	-ms-grid-column: 2;
	grid-column: 2;
	-ms-grid-row: 1;
	grid-row: 1;
	/*!rtl:end:ignore*/
}

.wp-block-media-text.has-media-on-the-right .wp-block-media-text__content {
	/*!rtl:begin:ignore*/
	-ms-grid-column: 1;
	grid-column: 1;
	-ms-grid-row: 1;
	grid-row: 1;
	/*!rtl:end:ignore*/
}

.wp-block-media-text__media img,
.wp-block-media-text__media video {
	max-width: unset;
	width: 100%;
	vertical-align: middle;
}

.wp-block-media-text.is-image-fill figure.wp-block-media-text__media {
	height: 100%;
	min-height: 250px;
	background-size: cover;
}

.wp-block-media-text.is-image-fill figure.wp-block-media-text__media > img {
	position: absolute;
	width: 1px;
	height: 1px;
	padding: 0;
	margin: -1px;
	overflow: hidden;
	clip: rect(0, 0, 0, 0);
	border: 0;
}

.is-vertical .wp-block-navigation__container {
	display: block;
}

.has-child > .wp-block-navigation-link__content {
	padding-right: 0.5em;
}

.has-child .wp-block-navigation__container {
	border: 1px solid rgba(0, 0, 0, 0.15);
	background-color: inherit;
	color: inherit;
	position: absolute;
	left: 0;
	top: 100%;
	width: -webkit-fit-content;
	width: -moz-fit-content;
	width: fit-content;
	z-index: 2;
	opacity: 0;
	transition: opacity 0.1s linear;
	visibility: hidden;
}

.has-child
	.wp-block-navigation__container
	> .wp-block-navigation-link
	> .wp-block-navigation-link__content {
	flex-grow: 1;
}

.has-child
	.wp-block-navigation__container
	> .wp-block-navigation-link
	> .wp-block-navigation-link__submenu-icon {
	padding-right: 0.5em;
}

.has-child:hover {
	cursor: pointer;
}

.has-child:hover > .wp-block-navigation__container {
	visibility: visible;
	opacity: 1;
	display: flex;
	flex-direction: column;
}

.has-child:focus-within {
	cursor: pointer;
}

.has-child:focus-within > .wp-block-navigation__container {
	visibility: visible;
	opacity: 1;
	display: flex;
	flex-direction: column;
}

.wp-block-navigation-link__content {
	color: inherit;
	text-decoration: none;
	padding: 0.5em 1em;
}

.wp-block-navigation-link__content + .wp-block-navigation-link__content {
	padding-top: 0;
}

.has-text-color .wp-block-navigation-link__content {
	color: inherit;
}

.wp-block-navigation-link__label {
	word-break: normal;
	overflow-wrap: break-word;
}

.wp-block-navigation-link__submenu-icon {
	height: inherit;
	padding: 0.375em 1em 0.375em 0;
}

.wp-block-navigation-link__submenu-icon svg {
	fill: currentColor;
}

@media (min-width: 782px) {
	.wp-block-navigation-link__submenu-icon svg {
		transform: rotate(90deg);
	}
}

.is-small-text {
	font-size: 0.875em;
}

.is-regular-text {
	font-size: 1em;
}

.is-large-text {
	font-size: 2.25em;
}

.is-larger-text {
	font-size: 3em;
}

.has-drop-cap:not(:focus)::first-letter {
	float: left;
	font-size: 8.4em;
	line-height: 0.68;
	font-weight: 100;
	margin: 0.05em 0.1em 0 0;
	text-transform: uppercase;
	font-style: normal;
}

p.has-background {
	padding: 1.25em 2.375em;
}

p.has-text-color a {
	color: inherit;
}

.wp-block-post-author {
	display: flex;
	flex-wrap: wrap;
}

.wp-block-post-author__byline {
	width: 100%;
	margin-top: 0;
	margin-bottom: 0;
	font-size: 0.5em;
}

.wp-block-post-author__avatar {
	margin-right: 1em;
}

.wp-block-post-author__bio {
	margin-bottom: 0.7em;
	font-size: 0.7em;
}

.wp-block-post-author__content {
	flex-grow: 1;
	flex-basis: 0;
}

.wp-block-post-author__name {
	font-weight: bold;
	margin: 0;
}

.wp-block-pullquote {
	padding: 3em 0;
	margin-left: 0;
	margin-right: 0;
	text-align: center;
}

.wp-block-pullquote.alignleft,
.wp-block-pullquote.alignright {
	max-width: 290px;
}

.wp-block-pullquote.alignleft p,
.wp-block-pullquote.alignright p {
	font-size: 1.25em;
}

.wp-block-pullquote p {
	font-size: 1.75em;
	line-height: 1.6;
}

.wp-block-pullquote cite,
.wp-block-pullquote footer {
	position: relative;
}

.wp-block-pullquote .has-text-color a {
	color: inherit;
}

.wp-block-pullquote:not(.is-style-solid-color) {
	background: none;
}

.wp-block-pullquote.is-style-solid-color {
	border: none;
}

.wp-block-pullquote.is-style-solid-color blockquote {
	margin-left: auto;
	margin-right: auto;
	text-align: left;
	max-width: 60%;
}

.wp-block-pullquote.is-style-solid-color blockquote p {
	margin-top: 0;
	margin-bottom: 0;
	font-size: 2em;
}

.wp-block-pullquote.is-style-solid-color blockquote cite {
	text-transform: none;
	font-style: normal;
}

.wp-block-pullquote cite {
	color: inherit;
}

.wp-block-quote.is-style-large,
.wp-block-quote.is-large {
	margin-bottom: 1em;
	padding: 0 1em;
}

.wp-block-quote.is-style-large p,
.wp-block-quote.is-large p {
	font-size: 1.5em;
	font-style: italic;
	line-height: 1.6;
}

.wp-block-quote.is-style-large cite,
.wp-block-quote.is-style-large footer,
.wp-block-quote.is-large cite,
.wp-block-quote.is-large footer {
	font-size: 1.125em;
	text-align: right;
}

.wp-block-separator.is-style-wide {
	border-bottom-width: 1px;
}

.wp-block-separator.is-style-dots {
	background: none !important;
	border: none;
	text-align: center;
	max-width: none;
	line-height: 1;
	height: auto;
}

.wp-block-separator.is-style-dots::before {
	content: '\00b7 \00b7 \00b7';
	color: currentColor;
	font-size: 1.5em;
	letter-spacing: 2em;
	/*rtl:ignore*/
	padding-left: 2em;
	font-family: serif;
}

.wp-block-spacer {
	clear: both;
}

p.wp-block-subhead {
	font-size: 1.1em;
	font-style: italic;
	opacity: 0.75;
}

.wp-block-table {
	overflow-x: auto;
}

.wp-block-table table {
	width: 100%;
}

.wp-block-table .has-fixed-layout {
	table-layout: fixed;
	width: 100%;
}

.wp-block-table .has-fixed-layout td,
.wp-block-table .has-fixed-layout th {
	word-break: break-word;
}

.wp-block-table.alignleft,
.wp-block-table.aligncenter,
.wp-block-table.alignright {
	display: table;
	width: auto;
}

.wp-block-table.alignleft td,
.wp-block-table.alignleft th,
.wp-block-table.aligncenter td,
.wp-block-table.aligncenter th,
.wp-block-table.alignright td,
.wp-block-table.alignright th {
	word-break: break-word;
}

.wp-block-table .has-subtle-light-gray-background-color {
	background-color: #f3f4f5;
}

.wp-block-table .has-subtle-pale-green-background-color {
	background-color: #e9fbe5;
}

.wp-block-table .has-subtle-pale-blue-background-color {
	background-color: #e7f5fe;
}

.wp-block-table .has-subtle-pale-pink-background-color {
	background-color: #fcf0ef;
}

.wp-block-table.is-style-stripes {
	border-spacing: 0;
	border-collapse: inherit;
	background-color: transparent;
	border-bottom: 1px solid #f0f0f0;
}

.wp-block-table.is-style-stripes tbody tr:nth-child(odd) {
	background-color: #f0f0f0;
}

.wp-block-table.is-style-stripes.has-subtle-light-gray-background-color
	tbody
	tr:nth-child(odd) {
	background-color: #f3f4f5;
}

.wp-block-table.is-style-stripes.has-subtle-pale-green-background-color
	tbody
	tr:nth-child(odd) {
	background-color: #e9fbe5;
}

.wp-block-table.is-style-stripes.has-subtle-pale-blue-background-color
	tbody
	tr:nth-child(odd) {
	background-color: #e7f5fe;
}

.wp-block-table.is-style-stripes.has-subtle-pale-pink-background-color
	tbody
	tr:nth-child(odd) {
	background-color: #fcf0ef;
}

.wp-block-table.is-style-stripes th,
.wp-block-table.is-style-stripes td {
	border-color: transparent;
}

.wp-block-text-columns {
	display: flex;
}

.has-pale-pink-background-color {
	background-color: #f78da7;
}

.has-vivid-red-background-color {
	background-color: #cf2e2e;
}

.has-luminous-vivid-orange-background-color {
	background-color: #ff6900;
}

.has-luminous-vivid-amber-background-color {
	background-color: #fcb900;
}

.has-light-green-cyan-background-color {
	background-color: #7bdcb5;
}

.has-vivid-green-cyan-background-color {
	background-color: #00d084;
}

.has-pale-cyan-blue-background-color {
	background-color: #8ed1fc;
}

.has-vivid-cyan-blue-background-color {
	background-color: #0693e3;
}

.has-vivid-purple-background-color {
	background-color: #9b51e0;
}

.has-white-background-color {
	background-color: #fff;
}

.has-very-light-gray-background-color {
	background-color: #eee;
}

.has-cyan-bluish-gray-background-color {
	background-color: #abb8c3;
}

.has-very-dark-gray-background-color {
	background-color: #313131;
}

.has-black-background-color {
	background-color: #000;
}

.has-pale-pink-color {
	color: #f78da7;
}

.has-vivid-red-color {
	color: #cf2e2e;
}

.has-luminous-vivid-orange-color {
	color: #ff6900;
}

.has-luminous-vivid-amber-color {
	color: #fcb900;
}

.has-light-green-cyan-color {
	color: #7bdcb5;
}

.has-vivid-green-cyan-color {
	color: #00d084;
}

.has-pale-cyan-blue-color {
	color: #8ed1fc;
}

.has-vivid-cyan-blue-color {
	color: #0693e3;
}

.has-vivid-purple-color {
	color: #9b51e0;
}

.has-white-color {
	color: #fff;
}

.has-very-light-gray-color {
	color: #eee;
}

.has-cyan-bluish-gray-color {
	color: #abb8c3;
}

.has-very-dark-gray-color {
	color: #313131;
}

.has-black-color {
	color: #000;
}

.has-vivid-cyan-blue-to-vivid-purple-gradient-background {
	background: linear-gradient(135deg, #0693e3 0%, #9b51e0 100%);
}

.has-vivid-green-cyan-to-vivid-cyan-blue-gradient-background {
	background: linear-gradient(135deg, #00d084 0%, #0693e3 100%);
}

.has-light-green-cyan-to-vivid-green-cyan-gradient-background {
	background: linear-gradient(135deg, #7adcb4 0%, #00d082 100%);
}

.has-luminous-vivid-amber-to-luminous-vivid-orange-gradient-background {
	background: linear-gradient(135deg, #fcb900 0%, #ff6900 100%);
}

.has-luminous-vivid-orange-to-vivid-red-gradient-background {
	background: linear-gradient(135deg, #ff6900 0%, #cf2e2e 100%);
}

.has-very-light-gray-to-cyan-bluish-gray-gradient-background {
	background: linear-gradient(135deg, #eeeeee 0%, #a9b8c3 100%);
}

.has-cool-to-warm-spectrum-gradient-background {
	background: linear-gradient(
		135deg,
		#4aeadc 0%,
		#9778d1 20%,
		#cf2aba 40%,
		#ee2c82 60%,
		#fb6962 80%,
		#fef84c 100%
	);
}

.has-blush-light-purple-gradient-background {
	background: linear-gradient(135deg, #ffceec 0%, #9896f0 100%);
}

.has-blush-bordeaux-gradient-background {
	background: linear-gradient(135deg, #fecda5 0%, #fe2d2d 50%, #6b003e 100%);
}

.has-purple-crush-gradient-background {
	background: linear-gradient(135deg, #34e2e4 0%, #4721fb 50%, #ab1dfe 100%);
}

.has-luminous-dusk-gradient-background {
	background: linear-gradient(135deg, #ffcb70 0%, #c751c0 50%, #4158d0 100%);
}

.has-hazy-dawn-gradient-background {
	background: linear-gradient(135deg, #faaca8 0%, #dad0ec 100%);
}

.has-pale-ocean-gradient-background {
	background: linear-gradient(135deg, #fff5cb 0%, #b6e3d4 50%, #33a7b5 100%);
}

.has-electric-grass-gradient-background {
	background: linear-gradient(135deg, #caf880 0%, #71ce7e 100%);
}

.has-subdued-olive-gradient-background {
	background: linear-gradient(135deg, #fafae1 0%, #67a671 100%);
}

.has-atomic-cream-gradient-background {
	background: linear-gradient(135deg, #fdd79a 0%, #004a59 100%);
}

.has-nightshade-gradient-background {
	background: linear-gradient(135deg, #330968 0%, #31cdcf 100%);
}

.has-midnight-gradient-background {
	background: linear-gradient(135deg, #020381 0%, #2874fc 100%);
}

.has-link-color a {
	color: #00e;
	color: var(--wp--style--color--link, #00e);
}

.has-small-font-size {
	font-size: 0.8125em;
}

.has-regular-font-size,
.has-normal-font-size {
	font-size: 1em;
}

.has-medium-font-size {
	font-size: 1.25em;
}

.has-large-font-size {
	font-size: 2.25em;
}

.has-larger-font-size,
.has-huge-font-size {
	font-size: 2.625em;
}

.has-text-align-center {
	text-align: center;
}

.has-text-align-left {
	/*rtl:ignore*/
	text-align: left;
}

.has-text-align-right {
	/*rtl:ignore*/
	text-align: right;
}

#end-resizable-editor-section {
	display: none;
}

h1 {
	font-size: 3em;
	font-weight: normal;
	clear: both;
	margin: 0;
}

h2 {
	font-size: 2.25em;
	font-weight: normal;
	clear: both;
	margin: 0;
}

h3 {
	font-size: 1.5em;
	font-weight: normal;
	margin: 0;
}

h4 {
	font-size: 1.25em;
	font-weight: normal;
	margin: 0;
}

h5 {
	font-size: 1.125em;
	font-weight: normal;
	margin: 0;
}

h6 {
	font-size: 1em;
	font-weight: normal;
	margin: 0;
}

p {
	font-size: 1em;
	margin: 0 !important;
}

hr {
	margin: 0;
}

.aligncenter {
	text-align: center;
}

.alignright {
	text-align: right;
}

/*.cb-block {*/
/*	border: solid 1px black;*/
/*}*/
