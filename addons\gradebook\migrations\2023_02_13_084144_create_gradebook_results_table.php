<?php

defined( 'ABSPATH' ) || exit;

/**
 * Create gradebook results table.
 *
 * @since 2.5.20
 */

use Masteriyo\Database\Migration;

/**
 * Create gradebook results table.
 */
class CreateGradebookResultsTable extends Migration {
	/**
	 * Run the migration.
	 *
	 * @since 2.5.20
	 */
	public function up() {
		$sql = "CREATE TABLE {$this->prefix}masteriyo_gradebook_results (
			id BIGINT UNSIGNED AUTO_INCREMENT,
			user_id BIGINT UNSIGNED NOT NULL DEFAULT 0,
			item_id BIGINT UNSIGNED NOT NULL DEFAULT 0,
			item_name TEXT NOT NULL DEFAULT '',
			item_type VARCHAR(255) NOT NULL DEFAULT '',
			parent_id BIGINT UNSIGNED NOT NULL DEFAULT 0,
			status VARCHAR(255) NOT NULL DEFAULT '',
			grade_id BIGINT UNSIGNED NOT NULL DEFAULT 0,
			grade_name VARCHA<PERSON>(255) NOT NULL DEFAULT '',
			grade_point DECIMAL(20,2) NOT NULL DEFAULT 0.0,
			earned_grade_point DECIMAL(20,2) NOT NULL DEFAULT 0.0,
			earned_percent DECIMAL(20,2) NOT NULL DEFAULT 0.0,
			weight INT UNSIGNED NOT NULL DEFAULT 1,
			created_at DATETIME DEFAULT '0000-00-00 00:00:00',
			modified_at DATETIME DEFAULT '0000-00-00 00:00:00',
			PRIMARY KEY  (id),
			KEY user_id (user_id),
			KEY parent_id (parent_id),
			KEY item_id (item_id),
			KEY item_type (item_type(191)),
			KEY status (status(191)),
			KEY created_at (created_at),
			KEY modified_at (modified_at)
		) $this->charset_collate;";

		dbDelta( $sql );
	}

	/**
	 * Reverse the migrations.
	 *
	 * @since 2.5.20
	 */
	public function down() {
		$this->connection->query( "DROP TABLE IF EXISTS {$this->prefix}masteriyo_gradebook_results;" );
	}
}
