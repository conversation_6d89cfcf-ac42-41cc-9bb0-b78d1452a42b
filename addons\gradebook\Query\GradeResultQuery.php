<?php
/**
 * Class for parameter-based GradeResult querying
 *
 * @package  Masteriyo\Query
 * @version 2.5.20
 * @since   2.5.20
 */

namespace Masteriyo\Addons\Gradebook\Query;

use Masteriyo\Enums\PostStatus;
use Masteriyo\Abstracts\ObjectQuery;
use Masteriyo\PostType\PostType;

defined( 'ABSPATH' ) || exit;

/**
 * GradeResult query class.
 */
class GradeResultQuery extends ObjectQuery {

	/**
	 * Valid query vars for grade results.
	 *
	 * @since 2.5.20
	 *
	 * @return array
	 */
	protected function get_default_query_vars() {
		return array_merge(
			parent::get_default_query_vars(),
			array(
				'orderby' => 'created_at',
				'author'  => '',
				'status'  => array( PostStatus::DRAFT, PostStatus::PENDING, PostStatus::PVT, PostStatus::PUBLISH ),
			)
		);
	}

	/**
	 * Get grade results matching the current query vars.
	 *
	 * @since 2.5.20
	 *
	 * @return \Masteriyo\Addons\Gradebook\Models\GradeResult[] GradeResult objects
	 */
	public function get_grade_results() {
		/**
		 * Filters grade result object query args.
		 *
		 * @since 2.5.20
		 *
		 * @param array $query_args The object query args.
		 */
		$args    = apply_filters( 'masteriyo_grade_result_object_query_args', $this->get_query_vars() );
		$results = masteriyo( 'grade-result.store' )->query( $args, $this );

		/**
		 * Filters grade result object query results.
		 *
		 * @since 2.5.20
		 *
		 * @param \Masteriyo\Addons\Gradebook\Models\GradeResult[] $results The query results.
		 * @param array $query_args The object query args.
		 */
		return apply_filters( 'masteriyo_grade_result_object_query', $results, $args );
	}
}
