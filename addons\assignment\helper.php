<?php

defined( 'ABSPATH' ) || exit;

/**
 * Assignment addon helper functions.
 *
 * @since 2.3.5
 * @package Masteriyo\Addons\Assignment
 */

use Masteriyo\PostType\PostType;

/**
 * Get assignment reply.
 *
 * @since 2.3.5
 *
 * @param int|Masteriyo\Addons\Assignment\Models\Assignment|WP_Comment $reply Assignment reply id or Assignment reply Model or Comment.
 *
 * @return Masteriyo\Addons\Assignment\Models\Assignment|null
 */
function masteriyo_get_assignment_reply( $reply ) {
	$reply_obj   = masteriyo( 'assignment-reply' );
	$reply_store = masteriyo( 'assignment-reply.store' );

	if ( is_a( $reply, 'Masteriyo\Addons\Assignment\Models\AssignmentReply' ) ) {
		$id = $reply->get_id();
	} elseif ( is_a( $reply, 'WP_Comment' ) ) {
		$id = $reply->comment_ID;
	} else {
		$id = $reply;
	}

	try {
		$id = absint( $id );
		$reply_obj->set_id( $id );
		$reply_store->read( $reply_obj );
	} catch ( \Exception $e ) {
		return null;
	}

	/**
	 * Filters assignment reply object.
	 *
	 * @since 2.3.5
	 *
	 * @param Masteriyo\Addons\Assignment\Models\AssignmentReply $reply_obj Assignment reply object.
	 * @param int|Masteriyo\Addons\Assignment\Models\AssignmentReply|WP_Comment $reply Assignment reply id or Assignment reply Model or Post.
	 */
	return apply_filters( 'masteriyo_get_assignment_reply', $reply_obj, $reply );
}

/**
 * Get assignments
 *
 * @since 2.3.5
 *
 * @param array $args Query arguments.
 *
 * @return Masteriyo\Addons\Assignment\Models\AssignmentReply[]
 */
function masteriyo_get_assignment_replys( $args = array() ) {
	$replys = masteriyo( 'query.replys-reply' )->set_args( $args )->get_assignment_replys();

	/**
	 * Filters queried assignment reply objects.
	 *
	 * @since 2.3.5
	 *
	 * @param Masteriyo\Addons\Assignment\Models\AssignmentReply[] $replys Queried replys.
	 * @param array $args Query args.
	 */
	return apply_filters( 'masteriyo_get_assignment_replys', $replys, $args );
}

/**
 * Get assignment.
 *
 * @since 2.3.5
 *
 * @param int|Masteriyo\Addons\Assignment\Models\Assignment|WP_Post $assignment Assignment id or Assignment Model or Comment.
 *
 * @return Masteriyo\Addons\Assignment\Models\Assignment|null
 */
function masteriyo_get_assignment( $assignment ) {
	$assignment_obj   = masteriyo( 'assignment' );
	$assignment_store = masteriyo( 'assignment.store' );

	if ( is_a( $assignment, 'Masteriyo\Addons\Assignment\Models\Assignment' ) ) {
		$id = $assignment->get_id();
	} elseif ( is_a( $assignment, 'WP_Post' ) ) {
		$id = $assignment->ID;
	} else {
		$id = $assignment;
	}

	try {
		$id = absint( $id );
		$assignment_obj->set_id( $id );
		$assignment_store->read( $assignment_obj );
	} catch ( \Exception $e ) {
		return null;
	}

	/**
	 * Filters assignment object.
	 *
	 * @since 2.3.5
	 *
	 * @param Masteriyo\Addons\Assignment\Models\Assignment $assignment_obj Assignment object.
	 * @param int|Masteriyo\Addons\Assignment\Models\Assignment|WP_Post  Assignment id or Assignment Model or Post.
	 */
	return apply_filters( 'masteriyo_get_assignment', $assignment_obj, $assignment );
}

/**
 * Get assignments.
 *
 * @since 2.3.5
 *
 * @param array $args Query arguments.
 *
 * @return Masteriyo\Addons\Assignment\Models\Assignment[]
 */
function masteriyo_get_assignments( $args = array() ) {
	$assignments = masteriyo( 'query.assignment' )->set_args( $args )->get_assignments();

	/**
	 * Filters queried assignment objects.
	 *
	 * @since 2.3.5
	 *
	 * @param Masteriyo\Addons\Assignment\Models\Assignment[] $assignments Queried assignments.
	 * @param array $args Query args.
	 */
	return apply_filters( 'masteriyo_get_assignments', $assignments, $args );
}

if ( ! function_exists( 'masteriyo_get_assignments_ids_for_instructor' ) ) {
	/**
	 * Retrieves the assignment replies associated with a given instructor.
	 *
	 * @since 2.14.0
	 *
	 * @param int|null $instructor_id The ID of the instructor.
	 *
	 * @return array An array of assignment replies associated with the specified instructor.
	 */
	function masteriyo_get_assignments_ids_for_instructor( $instructor_id = null ) {
		$courses = masteriyo_get_instructor_course_ids( $instructor_id );

		if ( empty( $courses ) ) {
			return array(); // No courses found
		}

		// Get assignment IDs associated with the courses.
		$assignments_ids = get_posts(
			array(
				'post_type'      => PostType::ASSIGNMENT,
				'post_status'    => 'publish',
				'posts_per_page' => -1,
				'fields'         => 'ids',
				'meta_query'     => array(
					array(
						'key'     => '_course_id',
						'value'   => $courses,
						'compare' => 'IN',
					),
				),
			)
		);

		/**
		 * Filter the list of assignments Ids for an instructor.
		 *
		 * @since 2.14.0
		 *
		 * @param array $course_ids The array of assignments IDs.
		 * @param int $instructor_id The instructor ID.
		 */
		$assignments_ids = apply_filters( 'masteriyo_get_instructor_assignment_ids', $assignments_ids, $instructor_id );

		return $assignments_ids;
	}
}
