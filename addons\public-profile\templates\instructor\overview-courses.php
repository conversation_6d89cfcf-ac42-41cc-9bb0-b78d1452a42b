<?php

/**
 * The Template for displaying courses section for overview tab in the public profile page.
 *
 * @since 2.6.8
 */

use Masteriyo\Addons\PublicProfile\Svg;

defined( 'ABSPATH' ) || exit; // Exit if accessed directly.

/**
 * Fires before rendering overview courses section in public profile page.
 *
 * @since 2.6.8
 */
do_action( 'masteriyo_before_public_profile_overview_courses' );

?>
<div class="masteriyo-col-right--courses">
	<div class="masteriyo-enrolled-courses--heading">
		<h3 class="title"><?php esc_html_e( 'Courses', 'learning-management-system' ); ?></h3>

		<?php if ( absint( $data['overview_user_courses_count'] ) ) : ?>
			<a href="javascript:;" class="masteriyo-secondary-btn show-all" data-target-tab="masteriyo-courses-offered-main-content">
				<?php esc_html_e( 'Show All', 'learning-management-system' ); ?>
				<?php Svg::get( 'show-all', true ); ?>
			</a>
		<?php endif; ?>

	</div>

	<?php
	echo $data['overview_user_courses_list']; //phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
	?>

</div>

<?php
/**
 * Fires after rendering overview courses section in public profile page.
 *
 * @since 2.6.8
 */
do_action( 'masteriyo_after_public_profile_overview_courses' );
