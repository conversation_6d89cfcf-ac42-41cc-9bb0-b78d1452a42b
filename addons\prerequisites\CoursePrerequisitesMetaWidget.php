<?php
/**
 * Masteriyo course prerequisites elementor widget class.
 *
 * @package Masteriyo\Addons\Prerequisites
 *
 * @since 2.12.2
 */

namespace Masteriyo\Addons\Prerequisites;

use Elementor\Controls_Manager;
use Masteriyo\Addons\ElementorIntegration\Helper;
use Masteriyo\Addons\ElementorIntegration\WidgetBase;
use Masteriyo\Pro\Addons;

defined( 'ABSPATH' ) || exit;

/**
 * Masteriyo course prerequisites elementor widget class.
 *
 * @package Masteriyo\Addons\Prerequisites
 *
 * @since 2.12.2
 */
class CoursePrerequisitesMetaWidget extends WidgetBase {

	/**
	 * Get widget name.
	 *
	 * @since 2.12.2
	 *
	 * @return string
	 */
	public function get_name() {
		return 'masteriyo-course-prerequisites-code';
	}

	/**
	 * Get widget title.
	 *
	 * @since 2.12.2
	 *
	 * @return string
	 */
	public function get_title() {
		return __( 'Course Prerequisites', 'learning-management-system' );
	}

	/**
	 * Get widget icon.
	 *
	 * @since 2.12.2
	 *
	 * @return string Widget icon.
	 */
	public function get_icon() {
		return 'course-prerequisites-widget-icon';
	}

	/**
	 * Get widget keywords.
	 *
	 * @since 2.12.2
	 *
	 * @return string[]
	 */
	public function get_keywords() {
		return array( 'prerequisites', 'course prerequisites', 'course' );
	}

	/**
	 * Register controls configuring widget content.
	 *
	 * @since 2.12.2
	 */
	protected function register_content_controls() {}

	/**
	 * Register controls for customizing widget styles.
	 *
	 * @since 2.12.2
	 */
	protected function register_style_controls() {

		$this->register_course_prerequisites_styles_section();

	}

	/**
	 * Register course prerequisites style controls section.
	 *
	 * @since 2.12.2
	 */
	protected function register_course_prerequisites_styles_section() {

		$this->start_controls_section(
			'course_prerequisites_msg_styles_section',
			array(
				'label' => esc_html__( 'Course Prerequisites Text', 'learning-management-system' ),
				'tab'   => Controls_Manager::TAB_STYLE,
			)
		);

		$this->add_text_region_style_controls(
			'course_prerequisites_msg_',
			'.masteriyo-prerequisites--msg',
			array(
				'custom_selectors' => array(
					'text_color'       => '{{WRAPPER}} .masteriyo-prerequisites--msg *',
					'hover_text_color' => '{{WRAPPER}} .masteriyo-prerequisites--msg:hover *',
					'typography'       => '{{WRAPPER}} .masteriyo-prerequisites--msg *',
					'hover_typography' => '{{WRAPPER}} .masteriyo-prerequisites--msg:hover *',
				),
			)
		);

		$this->end_controls_section();

		$this->start_controls_section(
			'course_prerequisites_list_styles_section',
			array(
				'label' => esc_html__( 'Course Prerequisites List', 'learning-management-system' ),
				'tab'   => Controls_Manager::TAB_STYLE,
			)
		);

		$this->add_text_region_style_controls(
			'course_prerequisites_list_',
			'.masteriyo-prerequisites--list',
			array(
				'disable_align'    => true,
				'custom_selectors' => array(
					'text_color'       => '{{WRAPPER}} .masteriyo-prerequisites--list a',
					'hover_text_color' => '{{WRAPPER}} .masteriyo-prerequisites--list:hover a',
					'typography'       => '{{WRAPPER}} .masteriyo-prerequisites--list a',
					'hover_typography' => '{{WRAPPER}} .masteriyo-prerequisites--list:hover a',
				),
			)
		);
		$this->end_controls_section();

	}

	/**
	 * Render course prerequisites widget output in the editor.
	 *
	 * Written as a Backbone JavaScript template and used to generate the live preview.
	 *
	 * @since 2.12.2
	 */
	protected function content_template() {
		$course = Helper::get_elementor_preview_course();

		if ( ! $course ) {
			return;
		}
		if ( ! ( new Addons() )->is_active( MASTERIYO_PREREQUISITES_ADDON_SLUG ) ) {
			return;
		}

		do_action( 'masteriyo_elementor_course_prerequisites_widget', $course );
	}

	/**
	 * Render the widget output on the frontend.
	 *
	 * @since 2.12.2
	 */
	protected function render() {
		$course = $this->get_course_to_render();
		if ( ! ( new Addons() )->is_active( MASTERIYO_PREREQUISITES_ADDON_SLUG ) ) {
			return;
		}
		if ( $course ) {
			do_action( 'masteriyo_elementor_course_prerequisites_widget', $course );
		}
	}
}
