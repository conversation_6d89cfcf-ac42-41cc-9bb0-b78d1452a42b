<?php

defined( 'ABSPATH' ) || exit;

/**
 * Addon Name: Prerequisites
 * Addon URI: https://masteriyo.com/wordpress-lms/
 * Description: Require students to complete one or more courses before enrolling a certain course.
 * Author: Masteriyo
 * Author URI: https://masteriyo.com
 * Addon Type: enhancement
 * Plan: Starter,Pro,Elite,Growth,Scale
 */

use Masteriyo\Pro\Addons;

define( 'MASTERIYO_PREREQUISITES_ADDON_FILE', __FILE__ );
define( 'MASTERIYO_PREREQUISITES_ADDON_BASENAME', plugin_basename( __FILE__ ) );
define( 'MASTERIYO_PREREQUISITES_ADDON_DIR', __DIR__ );
define( 'MASTERIYO_PREREQUISITES_ASSETS', __DIR__ . '/assets' );
define( 'MASTERIYO_PREREQUISITES_TEMPLATES', __DIR__ . '/templates' );
define( 'MASTERIYO_PREREQUISITES_ADDON_SLUG', 'prerequisites' );

// Bail early if the addon is not active.
if ( ! ( new Addons() )->is_active( MASTERIYO_PREREQUISITES_ADDON_SLUG ) ) {
	return;
}

/**
 * Include service providers for White Label.
 */
add_filter(
	'masteriyo_service_providers',
	function( $providers ) {
		return array_merge( $providers, require_once __DIR__ . '/config/providers.php' );
	}
);

/**
 * Initialize Masteriyo White Label.
 */
add_action(
	'masteriyo_before_init',
	function() {
		masteriyo( 'addons.prerequisites' )->init();
	}
);
