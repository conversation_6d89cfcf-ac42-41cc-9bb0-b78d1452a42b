!function(t){(grecaptcha="undefined"==typeof grecaptcha?{}:grecaptcha).ready=function(e){var t;"object"==typeof grecaptcha?(t="___grecaptcha_cfg",window[t]=window[t]||{},(window[t].fns=window[t].fns||[]).push(e)):e()},grecaptcha.ready(function(){"v3"===_MASTERIYO_RECAPTCHA_.version?grecaptcha.execute(_MASTERIYO_RECAPTCHA_.siteKey,{action:"submit"}).then(function(e){console.log(e),t("#masteriyo-recaptcha").after('<input type="hidden" name="g-recaptcha-response" value="'+e+'">')}):grecaptcha.render("masteriyo-recaptcha",{sitekey:_MASTERIYO_RECAPTCHA_.siteKey,theme:_MASTERIYO_RECAPTCHA_.theme,size:_MASTERIYO_RECAPTCHA_.size})})}(jQuery);