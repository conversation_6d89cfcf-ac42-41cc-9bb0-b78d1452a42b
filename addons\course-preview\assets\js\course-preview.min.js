!function(c){var r={init:function(){this.bindUIActions()},bindUIActions:function(){this.setupPreviewLinkClick(),this.setupModalCloseClick(),this.setupModalOutsideClick(),this.setupPlaylistItemClick(".masteriyo-course-preview-playlist-item","#masteriyo-course-preview-video-player-container")},setupPreviewLinkClick:function(){var i=c(".masteriyo-course-preview-playlist-item"),t=c("#masteriyo-course-preview-video-player-container");c(document.body).on("click",".masteriyo-course-preview-link",function(){var e=c(this).data("preview-id"),e=i.filter('[data-lesson-id="'+e+'"]');i.removeClass("active"),e.addClass("active"),r.updateVideoSource(e,t),c("#masteriyo-course-preview-video-playlist-modal").show()})},setupModalCloseClick:function(){c(document.body).on("click",".masteriyo-course-preview-modal-content .close",function(){r.closeModal()})},setupModalOutsideClick:function(){var i=c("#masteriyo-course-preview-video-playlist-modal");i.on("click",function(e){e.target===i[0]&&r.closeModal()})},closeModal:function(){c("#masteriyo-course-preview-video-playlist-modal").hide(),c("#masteriyo-course-preview-video-player-container").find("iframe").attr("src",""),c("#masteriyo-course-preview-video-player-container").find("video").attr("src","")},setupPlaylistItemClick:function(a,o){c(document.body).on("click",a,function(){var e=c(a),i=c(o),t=c(this);e.removeClass("active"),t.addClass("active"),r.updateVideoSource(t,i)})},updateVideoSource:function(i,e){var t=i.data("video-src"),a=JSON.parse(i.data("iframe")),o=e.find("video"),r=e.find("iframe"),e=i.data("lesson-id"),e=c(".masteriyo-course-preview-link[data-preview-id="+e+"]"),s=e.data("lesson-title"),e=e.data("categories");c(".masteriyo-course-preview__header-title-sub-title").html(e),c(".masteriyo-course-preview__header-title-main-title").html(s),o.attr("src",a?"":t).toggle(!a),r.attr("src",a?t:"").toggle(a),a&&"external"!==i.data("source")&&"embed-video"!==i.data("source")?r.one("load",function(){var e=i.data("video-src")+"?"+c.param({autoplay:1});r.attr("src",e)}):"embed-video"===i.data("source")&&(t=i.data(),r.one("load",function(){var e=new URL(t),e=(e.searchParams.has("autoplay")||e.searchParams.set("autoplay",1),e.searchParams.set("loop",1),e.toString());r.attr("src",e)}))}};r.init()}(jQuery);