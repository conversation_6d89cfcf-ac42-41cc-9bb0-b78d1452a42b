<?php
/**
 * Grade result type.
 *
 * @since 2.5.20
 * @package Masteriyo\Addons\Gradebook
 */

namespace Masteriyo\Addons\Gradebook\Enums;

defined( 'ABSPATH' ) || exit;

/**
 * Grade result type enum class.
 *
 * @since 2.5.20
 */
class GradeResultType {
	/**
	 * Grade result course type.
	 *
	 * @since 2.5.20
	 * @var string
	 */
	const COURSE = 'course';

	/**
	 * Grade result quiz type
	 *
	 * @since 2.5.20
	 * @var string
	 */
	const QUIZ = 'quiz';

	/**
	 * Grade result assignment type
	 *
	 * @since 2.5.20
	 * @var string
	 */
	const ASSIGNMENT = 'assignment';

	/**
	 * Return all Grade result types.
	 *
	 * @since 2.5.20
	 *
	 * @return array
	 */
	public static function all() {
		return array_unique(
			/**
			 * Filters Grade result  types.
			 *
			 * @since 2.5.20
			 *
			 * @param string[] $types Grade result types.
			 */
			apply_filters(
				'masteriyo_pro_grade_result_types',
				array(
					self::ASSIGNMENT,
					self::QUIZ,
					self::COURSE,
				)
			)
		);
	}
}
