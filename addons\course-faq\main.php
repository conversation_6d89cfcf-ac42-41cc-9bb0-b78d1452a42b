<?php

defined( 'ABSPATH' ) || exit;

/**
 * Addon Name: Course FAQ
 * Addon URI: https://masteriyo.com/wordpress-lms/
 * Description: If you have a lot of frequently asked questions for your courses, this feature will come handy. It introduces new FAQ tab in the course page.
 * Author: Masteriyo
 * Author URI: https://masteriyo.com
 * Addon Type: enhancement
 * Plan:Basic,Starter,Pro,Elite,Growth,Scale
 */

use Masteriyo\Pro\Addons;

define( 'MASTERIYO_COURSE_FAQ_ADDON_FILE', __FILE__ );
define( 'MASTERIYO_COURSE_FAQ_ADDON_BASENAME', plugin_basename( __FILE__ ) );
define( 'MASTERIYO_COURSE_FAQ_ADDON_DIR', dirname( __FILE__ ) );
define( 'MASTERIYO_COURSE_FAQ_ASSETS', dirname( __FILE__ ) . '/assets' );
define( 'MASTERIYO_COURSE_FAQ_TEMPLATES', dirname( __FILE__ ) . '/templates' );
define( 'MASTERIYO_COURSE_FAQ_ADDON_SLUG', 'course-faq' );

// Bail early if the addon is not active.
if ( ! ( new Addons() )->is_active( MASTERIYO_COURSE_FAQ_ADDON_SLUG ) ) {
	return;
}

require_once dirname( __FILE__ ) . '/helper.php';
require_once dirname( __FILE__ ) . '/template-hooks.php';

/**
 * Include service providers for course faq.
 */
add_filter(
	'masteriyo_service_providers',
	function( $providers ) {
		return array_merge( $providers, require_once dirname( __FILE__ ) . '/config/providers.php' );
	}
);

/**
 * Initialize Masteriyo course faq.
 */
add_action(
	'masteriyo_before_init',
	function() {
		masteriyo( 'addons.course-faq' )->init();
	}
);
