<?php
/**
 * Current timestamp block builder.
 *
 * @since 2.14.0
 */

namespace Masteriyo\Addons\Certificate\PDF\BlockBuilders;

defined( 'ABSPATH' ) || exit;


class MasteriyoCurrentTimestamp extends BlockBuilder {

	/**
	 * Build and return the block HTML.
	 *
	 * @since 2.14.0
	 *
	 * @return string
	 */
	public function build() {
		$pdf               = $this->get_pdf();
		$current_timestamp = $pdf->is_preview() ? __( 'Current Timestamp', 'learning-management-system' ) : '';
		$block_data        = $this->get_block_data();
		$timestamp_format  = masteriyo_array_get( $block_data, 'attrs.timestampFormat' );
		$timestamp_format  = empty( $timestamp_format ) ? 'F j, Y g:i a' : $timestamp_format;

		$current_timestamp = gmdate( $timestamp_format, strtotime( 'now' ) );

		/**
		 * Filters the current timestamp value for the certificate.
		 *
		 * @since 2.14.0
		 *
		 * @param string $current_timestamp The current timestamp value.
		 * @param string $timestamp_format The timestamp format.
		 * @return string The filtered current timestamp value.
		 */
		$current_timestamp = apply_filters( 'masteriyo_certificate_current_timestamp', $current_timestamp, $timestamp_format );

		$html  = str_replace( '{{masteriyo_current_timestamp}}', $current_timestamp, $block_data['innerHTML'] );
		$html .= '<style>' . masteriyo_array_get( $block_data, 'attrs.blockCSS', '' ) . '</style>';
		return $html;
	}
}
