<?php
/**
 * Currency switching template.
 *
 * @since 2.11.0
 */

defined( 'ABSPATH' ) || exit;

if ( ! isset( $currencies ) || empty( $currencies ) ) {
	return;
}
?>

<div class="masteriyo-checkout-summary-switch-currency">
	<label for="masteriyo-currency-select" class="masteriyo-label">
		<?php esc_html_e( 'Select Currency', 'learning-management-system' ); ?>
	</label>
	<select name="masteriyo_currency" id="masteriyo-currency-select" class="masteriyo-checkout-summary-switch-currency-select">
		<?php if ( ! empty( $currencies ) ) : ?>
			<option value="<?php echo esc_attr( masteriyo_get_currency() ); ?>" <?php selected( masteriyo_get_currency(), masteriyo_get_current_currency() ); ?>>
				<?php echo esc_html( masteriyo_get_currency_from_code( masteriyo_get_currency() ) ); ?>
			</option>
			<?php foreach ( $currencies as $code => $currency ) : ?>
				<option value="<?php echo esc_attr( $code ); ?>" <?php selected( masteriyo_get_current_currency(), $code ); ?>>
					<?php echo esc_html( $currency ); ?>
				</option>
			<?php endforeach; ?>
		<?php endif; ?>
	</select>
	<?php wp_nonce_field( 'masteriyo_switch_currency', 'masteriyo-switch-currency-nonce' ); ?>
</div>

<?php
