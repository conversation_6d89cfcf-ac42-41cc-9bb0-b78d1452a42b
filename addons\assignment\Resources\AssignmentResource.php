<?php
/**
 * Resource handler for Assignment data.
 *
 * @since 2.8.3
 */

namespace Masteriyo\Addons\Assignment\Resources;

use Masteriyo\Helper\Utils;
use Masteriyo\Resources\CourseResource;

defined( 'ABSPATH' ) || exit;

/**
 * Resource handler for Assignment data.
 *
 * @since 2.8.3
 */
class AssignmentResource {


	/**
	 * Transform the resource into an array.
	 *
	 * @since 2.8.3
	 *
	 * @param \Masteriyo\Addons\Assignment\Models\Assignment $assignment Assignment object.
	 *
	 * @return array
	 */
	public static function to_array( $assignment, $context = 'view' ) {

		$course = masteriyo_get_course( $assignment->get_course_id() );
		$user   = masteriyo_get_user( $assignment->get_author_id( $context ) );

		if ( ! is_wp_error( $user ) ) {
			$author = array(
				'id'           => $user->get_id(),
				'display_name' => $user->get_display_name(),
				'avatar_url'   => $user->profile_image_url(),
			);
		}

		$data = array(
			'name'                 => $assignment->get_name(),
			'answer'               => $assignment->get_answer(),
			'menu_order'           => $assignment->get_menu_order(),
			'parent_id'            => $assignment->get_parent_id(),
			'course_id'            => $assignment->get_course_id(),
			'author_id'            => $assignment->get_author_id(),
			'created_at'           => masteriyo_rest_prepare_date_response( $assignment->get_created_at() ),
			'modified_at'          => masteriyo_rest_prepare_date_response( $assignment->get_modified_at() ),
			'status'               => $assignment->get_status(),
			'total_points'         => $assignment->get_total_points(),
			'pass_points'          => $assignment->get_pass_points(),
			'due_date'             => masteriyo_rest_prepare_date_response( $assignment->get_due_date() ),
			'due_timestamp'        => masteriyo_rest_prepare_date_response( $assignment->get_due_timestamp() ),
			'due_timestamp'        => $assignment->get_due_timestamp(),
			'max_file_upload_size' => $assignment->get_max_file_upload_size(),
			'course'               => CourseResource::to_array( $course ),
		);

		/**
		 * Filter assignment data array resource.
		 *
		 * @since 2.8.3
		 *
		 * @param array $data assignment data.
		 * @param \Masteriyo\Addons\Assignment\Models\Assignment $assignment Assignment object.
		 * @param string $context What the value is for. Valid values are view and edit.
		 */
		return apply_filters( 'masteriyo_assignment_resource_array', $data, $assignment, $context );
	}

	/**
	 * Get taxonomy terms if a course.
	 *
	 * @since 2.8.3
	 *
	 * @param \Masteriyo\Addons\Assignment\Models\Assignment $assignment Assignment object.
	 * @param string $taxonomy Taxonomy slug.
	 *
	 * @return array
	 */
	protected static function get_taxonomy_terms( $assignment, $taxonomy = 'cat' ) {
		$terms = Utils::get_object_terms( $assignment->get_id(), 'assignment_' . $taxonomy );

		$terms = array_map(
			function ( $term ) {
				return array(
					'id'   => $term->term_id,
					'name' => $term->name,
					'slug' => $term->slug,
				);
			},
			$terms
		);

		$terms = 'difficulty' === $taxonomy ? array_shift( $terms ) : $terms;

		return $terms;
	}
}
