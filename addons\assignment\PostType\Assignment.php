<?php
/**
 * Assignments class.
 *
 * @since 2.3.5
 *
 * @package Masteriyo\PostType;
 */

namespace Masteriyo\Addons\Assignment\PostType;

defined( 'ABSPATH' ) || exit;


use Masteriyo\PostType\PostType;

/**
 * Assignments class.
 */
class Assignment extends PostType {
	/**
	 * Post slug.
	 *
	 * @since 2.3.5
	 *
	 * @var string
	 */
	protected $slug = 'mto-assignment';

	/**
	 * Constructor.
	 */
	public function __construct() {
		$debug      = masteriyo_is_post_type_debug_enabled();
		$permalinks = masteriyo_get_permalink_structure();

		$this->labels = array(
			'name'                  => _x( 'Assignments', 'Assignment General Name', 'learning-management-system' ),
			'singular_name'         => _x( 'Assignment', 'Assignment Singular Name', 'learning-management-system' ),
			'menu_name'             => __( 'Assignments', 'learning-management-system' ),
			'name_admin_bar'        => __( 'Assignment', 'learning-management-system' ),
			'archives'              => __( 'Assignment Archives', 'learning-management-system' ),
			'attributes'            => __( 'Assignment Attributes', 'learning-management-system' ),
			'parent_item_colon'     => __( 'Parent Assignment:', 'learning-management-system' ),
			'all_items'             => __( 'All Assignments', 'learning-management-system' ),
			'add_new_item'          => __( 'Add New Item', 'learning-management-system' ),
			'add_new'               => __( 'Add New', 'learning-management-system' ),
			'new_item'              => __( 'New Assignment', 'learning-management-system' ),
			'edit_item'             => __( 'Edit Assignment', 'learning-management-system' ),
			'update_item'           => __( 'Update Assignment', 'learning-management-system' ),
			'view_item'             => __( 'View Assignment', 'learning-management-system' ),
			'view_items'            => __( 'View Assignments', 'learning-management-system' ),
			'search_items'          => __( 'Search Assignment', 'learning-management-system' ),
			'not_found'             => __( 'Not found', 'learning-management-system' ),
			'not_found_in_trash'    => __( 'Not found in Trash.', 'learning-management-system' ),
			'featured_image'        => __( 'Featured Image', 'learning-management-system' ),
			'set_featured_image'    => __( 'Set featured image', 'learning-management-system' ),
			'remove_featured_image' => __( 'Remove featured image', 'learning-management-system' ),
			'use_featured_image'    => __( 'Use as featured image', 'learning-management-system' ),
			'insert_into_item'      => __( 'Insert into assignment', 'learning-management-system' ),
			'uploaded_to_this_item' => __( 'Uploaded to this assignment', 'learning-management-system' ),
			'items_list'            => __( 'Assignments list', 'learning-management-system' ),
			'items_list_navigation' => __( 'Assignments list navigation', 'learning-management-system' ),
			'filter_items_list'     => __( 'Filter assignments list', 'learning-management-system' ),
		);

		$this->args = array(
			'label'               => __( 'Assignments', 'learning-management-system' ),
			'description'         => __( 'Assignments Description', 'learning-management-system' ),
			'labels'              => $this->labels,
			'supports'            => array( 'title', 'editor', 'author', 'comments', 'custom-fields', 'post-formats' ),
			'taxonomies'          => array(),
			'hierarchical'        => false,
			'menu_position'       => 5,
			'public'              => true,
			'show_ui'             => true,
			'show_in_menu'        => $debug,
			'show_in_admin_bar'   => $debug,
			'show_in_nav_menus'   => $debug,
			'show_in_rest'        => false,
			'has_archive'         => false,
			'map_meta_cap'        => true,
			'capability_type'     => array( 'assignment', 'assignments' ),
			'exclude_from_search' => true,
			'publicly_queryable'  => false,
			'can_export'          => true,
			'delete_with_user'    => true,
			'rewrite'             => isset( $permalinks['assignment_rewrite_slug'] ) ? array(
				'slug'       => $permalinks['assignment_rewrite_slug'],
				'with_front' => false,
				'feeds'      => true,
			) : false,
		);
	}
}
