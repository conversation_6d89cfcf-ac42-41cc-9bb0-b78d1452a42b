<?php
/**
 * Destroy PMPRO integration.
 *
 * @since 2.6.9
 */

defined( 'ABSPATH' ) || exit;


// Add student role to all the PMPRO levels.
if ( function_exists( 'pmpro_getMembershipLevelsForUser' ) ) {
	$users = get_users(
		array(
			'fields'   => array( 'ID' ),
			'role__in' => array(
				'masteriyo_student',
			),
		)
	);
	$users = array_filter(
		$users,
		function( $user ) {
			$levels           = pmpro_getMembershipLevelsForUser( $user->ID );
			$level_has_course = false;
			if ( $levels ) {
				foreach ( $levels as $level ) {
					$courses_level = get_pmpro_membership_level_meta( $level->id, '_mas_courses', true );
					if ( $courses_level ) {
						$level_has_course = true;
						break;
					}
				}
				return $level_has_course;
			}
			return false;
		}
	);

	if ( $users ) {
		foreach ( $users as $user ) {
			$user = new WP_User( $user->ID );
			$user->remove_role( 'masteriyo_student' );
		}
	}
}
