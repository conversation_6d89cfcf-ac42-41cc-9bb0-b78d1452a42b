<?php
/**
 * Current time block builder.
 *
 * @since 2.14.0
 */

namespace Masteriyo\Addons\Certificate\PDF\BlockBuilders;

defined( 'ABSPATH' ) || exit;


class MasteriyoCurrentTime extends BlockBuilder {

	/**
	 * Build and return the block HTML.
	 *
	 * @since 2.14.0
	 *
	 * @return string
	 */
	public function build() {
		$pdf          = $this->get_pdf();
		$current_time = $pdf->is_preview() ? __( 'Current Time', 'learning-management-system' ) : '';
		$block_data   = $this->get_block_data();
		$time_format  = masteriyo_array_get( $block_data, 'attrs.timeFormat' );
		$time_format  = empty( $time_format ) ? 'g:i a' : $time_format;

		$current_time = gmdate( $time_format, strtotime( 'now' ) );

		/**
		 * Filters the current time displayed on the certificate.
		 *
		 * @since 2.14.0
		 *
		 * @param string $current_time The current time.
		 * @param string $time_format  The time format.
		 *
		 * @return string The filtered current time.
		 */
		$current_time = apply_filters( 'masteriyo_certificate_current_time', $current_time, $time_format );

		$html  = str_replace( '{{masteriyo_current_time}}', $current_time, $block_data['innerHTML'] );
		$html .= '<style>' . masteriyo_array_get( $block_data, 'attrs.blockCSS', '' ) . '</style>';
		return $html;
	}
}
