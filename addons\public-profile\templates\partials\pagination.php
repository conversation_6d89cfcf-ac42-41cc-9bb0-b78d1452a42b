<?php

/**
 * The Template for displaying pagination section in the public profile page.
 *
 * @since 2.6.8
 */

defined( 'ABSPATH' ) || exit; // Exit if accessed directly.
?>

<!-- Pagination -->
<?php if ( absint( $total_pages ) > 1 ) : ?>
	<div class="masteriyo-courses--pagination">
		<ul class="masteriyo-courses--pagination-list">
			<?php

			// Generate "Previous" link.
			$previous_class = ( 1 === $current_page ) ? ' masteriyo-courses--pagination-item--disabled' : '';
			$previous_page  = ( 1 === $current_page ) ? 0 : $current_page - 1;
			?>
			<li class="masteriyo-courses--pagination-item pagination-previous<?php echo esc_attr( $previous_class ); ?>">
				<a href="javascript:;" data-page="<?php echo esc_attr( $previous_page ); ?>">Previous</a>
			</li>

			<?php
			// Generate numbered pagination links.
			for ( $p = 1; $p <= $total_pages; $p++ ) {
				$active_class = ( $current_page === $p ) ? ' masteriyo-courses--pagination-item--active' : '';
				?>
				<li class="masteriyo-courses--pagination-item<?php echo esc_attr( $active_class ); ?>">
					<a href="javascript:;" data-page="<?php echo esc_attr( $p ); ?>"><?php echo esc_html( $p ); ?></a>
				</li>
				<?php
			}
			?>

			<?php
			// Generate "Next" link.
			$next_class = ( $current_page === $total_pages ) ? ' masteriyo-courses--pagination-item--disabled' : '';
			$next_page  = ( $current_page === $total_pages ) ? 0 : $current_page + 1;
			?>
			<li class="masteriyo-courses--pagination-item pagination-next<?php echo esc_attr( $next_class ); ?>">
				<a href="javascript:;" data-page="<?php echo esc_attr( $next_page ); ?>">Next</a>
			</li>
		</ul>
	</div>
<?php endif; ?>
<!-- Pagination End -->
