<?php

/**
 * The Template for displaying gradebook result in single course page
 * @version 2.5.20
 */

use Masteriyo\Addons\Gradebook\Enums\GradeResultStatus;

defined( 'ABSPATH' ) || exit; // Exit if accessed directly.

/**
 * Fires before rendering gradebook section in single course page.
 *
 * @since 2.5.20
 */
do_action( 'masteriyo_before_single_course_gradebook' );

$layout = masteriyo_get_setting( 'single_course.display.template.layout' ) ?? 'default';
?>
<?php if ( 'layout1' === $layout ) : ?>
	<div id="masteriyoSingleCourseGradebookTab" class="masteriyo-single-body__main--review-content masteriyo-hidden">
	<?php else : ?>
		<div class="tab-content gradebook masteriyo-hidden">
		<?php endif; ?>
		<?php
		/**
		 * Action hook for rendering before course gradebook content.
		 *
		 * @since 2.5.20
		 *
		 * @param \Masteriyo\Models\Course $course
		 */
		do_action( 'masteriyo_single_course_before_gradebook_content', $course );
		?>

		<div class="course-grade-result final-grade-result">
			<?php esc_html_e( 'Final Grade:', 'learning-management-system' ); ?>

			<span style="background: <?php echo esc_attr( $final_grade_result->get_grade_color() ); ?>" class="grade-name">
				<?php echo esc_html( $final_grade_result->get_grade_name() ); ?>
			</span>

			<div class="grade-earned-grade-point">
				<?php
				printf(
					'%s/%s',
					esc_html( $final_grade_result->get_earned_grade_point() ),
					esc_html( $final_grade_result->get_grade_point() )
				)
				?>
			</div>

		</div>

		<div class="grade-results-container">
			<table class="grade-results-table">
				<thead>
					<th class="title">
						<?php esc_html_e( 'Title', 'learning-management-system' ); ?>
					</th>
					<th class="total-grade">
						<?php esc_html_e( 'Total Grade', 'learning-management-system' ); ?>
					</th>
					<th class="result">
						<?php esc_html_e( 'Result', 'learning-management-system' ); ?>
					</th>
				</thead>
				<tbody>
					<?php foreach ( $grade_results as $grade_result ) : ?>
						<tr class="single-grade-result">
							<td class="grade-item-name">
								<?php echo esc_html( $grade_result->get_item_name() ); ?>
							</td>
							<td class="grade-earned-grade-point">
								<?php
								if ( GradeResultStatus::PENDING === $grade_result->get_status() ) {
									echo esc_html_e( 'N/A', 'learning-management-system' );
								} else {
									printf(
										'%s/%s',
										esc_html( $grade_result->get_earned_grade_point() ),
										esc_html( $grade_result->get_grade_point() )
									);
								}
								?>
							</td>
							<td class="grade-earned-grade-name">
								<?php if ( GradeResultStatus::PENDING === $grade_result->get_status() ) : ?>
									<span class="grade-name grade-pending">
										<?php echo esc_html_e( 'Pending', 'learning-management-system' ); ?>
									</span>
								<?php else : ?>
									<span style="background: <?php echo esc_attr( $grade_result->get_grade_color() ); ?>" class='grade-name'>
										<?php echo esc_html( $grade_result->get_grade_name() ); ?>
									</span>
								<?php endif; ?>
							</td>
						</tr>
					<?php endforeach; ?>
				</tbody>
			</table>
		</div>

		<?php
		/**
		 * Action hook for rendering course gradebook content.
		 *
		 * @since 2.5.20
		 *
		 * @param \Masteriyo\Models\Course $course
		 */
		do_action( 'masteriyo_single_course_before_gradebook_content', $course );
		?>

		</div>
		<?php

		/**
		 * Fires after rendering gradebook section in single course page.
		 *
		 * @since 2.5.20
		 */
		do_action( 'masteriyo_after_single_course_gradebook' );
