<?php
/**
 * EDD integration service provider.
 *
 * @since 2.6.8
 */

namespace Masteriyo\Addons\EDDIntegration\Providers;

defined( 'ABSPATH' ) || exit;

use League\Container\ServiceProvider\AbstractServiceProvider;
use Masteriyo\Addons\EDDIntegration\EDDIntegrationAddon;

/**
 * EDD integration service provider.
 *
 * @since 2.6.8
 */
class EddIntegrationServiceProvider extends AbstractServiceProvider {
	/**
	 * The provided array is a way to let the container
	 * know that a service is provided by this service
	 * provider. Every service that is registered via
	 * this service provider must have an alias added
	 * to this array or it will be ignored
	 *
	 * @since 2.6.8
	 *
	 * @var array
	 */
	protected $provides = array(
		'addons.edd-integration',
		EDDIntegrationAddon::class,
	);

	/**
	 * This is where the magic happens, within the method you can
	 * access the container and register or retrieve anything
	 * that you need to, but remember, every alias registered
	 * within this method must be declared in the `$provides` array.
	 *
	 * @since 2.6.8
	 */
	public function register() {
		$this->getContainer()->add( 'addons.edd-integration', EDDIntegrationAddon::class, true );
	}
}
