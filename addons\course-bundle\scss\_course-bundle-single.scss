.admin-bar {
	--admin-bar: 32px;
	@media screen and (max-width: 782px) {
		--admin-bar: 46px;
	}
}

.masteriyo-bundle {
	display: flex;
	flex-wrap: wrap;
	margin-left: -15px;
	margin-right: -15px;
	flex-direction: row;

	&__content {
		background-color: var(--masteriyo-color-white);
		border-radius: 8px;
		border: 1px solid var(--masteriyo-color-border);
		height: 100%;
		padding: 0;
	}

	&__featured-img {
		margin-bottom: 20px;

		img {
			width: 100%;
			height: auto;
			border-top-left-radius: 8px;
			border-top-right-radius: 8px;
			margin-bottom: 0;
			padding-bottom: 0;
		}
	}

	&__title {
		font-size: 28px;
		font-weight: 700;
		margin-bottom: 16px;
		padding: 0 32px;
		color: var(--masteriyo-color-heading);
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	&__author-rating {
		display: flex;
		align-items: center;
		padding: 0 32px;
	}

	&__author,
	&__instructor {
		display: flex;
		align-items: center;
		flex: 1 1 50%;

		a {
			display: flex;
			align-items: center;
			flex: 1 1 50%;
		}

		img {
			width: 30px;
			height: 30px;
			-o-object-fit: cover;
			object-fit: cover;
			border-radius: 50%;
			border: 1px solid rgba(0, 0, 0, 0.1);
			margin: 0;
		}

		&-name {
			color: var(--masteriyo-color-text);
			font-size: 14px;
			line-height: 1.5;
			font-weight: 500;
			margin-left: 8px;
			overflow-wrap: break-word;
			text-transform: capitalize;
		}
	}

	&__main {
		.masteriyo-stab {
			padding: 0 32px;
			margin-top: 32px;
		}

		.tab-content {
			padding: 0 32px 32px;
			color: var(--masteriyo-color-text);
		}
	}

	&__courses {
		padding: 24px;

		&__title {
			margin: 0;
			font-size: 18px;
			font-weight: 500;
			padding-bottom: 18px;
			margin-bottom: 24px;
			border-bottom: 1px solid var(--masteriyo-color-border);
		}

		&-list {
			padding: 0;
			margin: 0;
			display: flex;
			flex-direction: column;
		}

		&-item {
			display: flex;
			margin: 0;
			column-gap: 18px;
			align-items: center;

			&:not(:last-child) {
				padding-bottom: 20px;
				margin-bottom: 24px;
				border-bottom: 1px solid var(--masteriyo-color-border);
			}

			&-img {
				max-width: 100px;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
			}

			&-info {
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: center;
			}

			h3 {
				margin: 0;
				font-size: 15px;
				font-weight: 500;
			}

			.masteriyo-course-author {
				font-size: 12px;
				font-weight: 400;
				text-transform: capitalize;
				a {
					color: var(--masteriyo-color-primary);
				}
			}
			&__price {
				font-size: 15px;
				font-weight: 500;
			}
		}
	}

	&__sidebar {
		@media (min-width: 768px) {
			position: sticky;
			top: calc(var(--admin-bar, 0) + 50px);
		}

		> div {
			background: var(--masteriyo-color-white);
			width: 100%;
			height: -moz-fit-content;
			height: fit-content;
			border-radius: 8px;
			display: flex;
			flex-direction: column;
			border: 1px solid var(--masteriyo-color-border);
			padding: 24px;

			&:first-child {
				margin-bottom: 40px;
			}
		}

		.masteriyo-time-btn {
			padding-bottom: 24px;
			border-top: none;
			border-bottom: 1px solid var(--masteriyo-color-border);
			display: flex;
			flex-direction: row;
			flex-wrap: wrap;
			align-items: center;
			justify-content: space-between;

			.masteriyo-btn {
				border-radius: 50px;
				padding: 6px 16px;
			}
		}

		.title {
			font-weight: bold;
			font-size: 16px;
			line-height: 120%;
			color: var(--masteriyo-color-heading);
			margin: 20px 0;
		}
	}

	&__highlights {
		ul {
			list-style: "✓";
			margin: 0;
            padding-left: 12px;
		}

		li {
			font-size: 14px;
			line-height: 1.618;
			padding-inline-start: 4px;
			word-break: break-all;
		}
	}

	@media (max-width: 768px) {
		.masteriyo-col-4 {
			width: 100%;
			flex: 1 1 100%;
			max-width: 100%;
		}
	}

	&__instructor {
		&:not(:last-child) {
			margin-bottom: 16px;
		}
	}
	&__instructors {
		h5 {
			margin: 0;
			font-size: 18px;
			font-weight: 500;
			padding-bottom: 18px;
			border-bottom: 1px solid var(--masteriyo-color-border);
			margin-bottom: 24px;
		}
	}
	&__category {
		display: flex;
		flex-wrap: wrap;
		padding: 0 32px;

		a {
			text-decoration: none;
			font-size: 10px;
			font-weight: 500;
			text-transform: uppercase;
			letter-spacing: 0.5px;
			padding: 2px 8px;
			color: var(--masteriyo-color-primary);
			border-radius: 50px;
			border: 1px solid rgba(0, 0, 0, 0.1);
			margin: 2px;
		}
	}
}
