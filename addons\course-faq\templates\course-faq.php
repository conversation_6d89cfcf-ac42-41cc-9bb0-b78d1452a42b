<?php

defined( 'ABSPATH' ) || exit;

/**
 * Course FAQ template.
 *
 * @since 2.2.7
 * @package Masteriyo\Addons\CourseFaq
 */
$layout = masteriyo_get_setting( 'single_course.display.template.layout' ) ?? 'default';
?>
<?php if ( 'layout1' === $layout ) : ?>
<div id="masteriyoSingleCourseFAQsTab" class="masteriyo-single-body__main--review-content masteriyo-hidden">
<?php else : ?>
	<div class="tab-content course-faqs masteriyo-hidden">
<?php endif; ?>
	<div class="masteriyo-stab--faq">
		<div class="title-container">
			<h3 class="title"><?php esc_html_e( 'FAQs', 'learning-management-system' ); ?></h3>
			<span class="masteriyo-link-primary masteriyo-expand-collapse-all"><?php esc_html_e( 'Expand All', 'learning-management-system' ); ?></span>
		</div>

		<?php foreach ( $course_faqs  as $index => $faq ) : ?>
		<div class="masteriyo-stab--items <?php echo esc_attr( 0 === $index ? 'active' : '' ); ?>">
			<div class="masteriyo-header">
				<h5 class="masteriyo-title"><?php echo esc_attr( $index + 1 ); ?>.&nbsp;&nbsp;<?php echo esc_html( $faq->get_title() ); ?></h5>

				<div class="masteriyo-toggle-icon">
					<span class="masteriyo-plus masteriyo-icon-svg">
						<?php masteriyo_get_svg( 'plus', true ); ?>
					</span>
					<span class="masteriyo-minus masteriyo-icon-svg">
						<?php masteriyo_get_svg( 'minus', true ); ?>
					</span>
				</div>
			</div>

			<?php if ( ! empty( $faq->get_content() ) ) : ?>
			<div class="masteriyo-body">
				<?php echo wp_kses_post( $faq->get_content() ); ?>
			</div>
		<?php endif; ?>
		</div>
		<?php endforeach; ?>

	</div>
</div>
