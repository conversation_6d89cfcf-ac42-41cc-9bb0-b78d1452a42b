<?php

defined( 'ABSPATH' ) || exit;

/**
 * Addon Name: MailerLite Integration
 * Addon URI: https://masteriyo.com/wordpress-lms/
 * Description: Seamlessly integrate MailerLite with Masteriyo LMS for efficient and automated email marketing within your learning management system.
 * Author: Masteriyo
 * Addon Type: integration
 * Author URI: https://masteriyo.com
 * Plan: Starter,Pro,Elite,Growth,Scale
 */

use Masteriyo\Addons\MailerLiteIntegration\MailerLiteIntegrationAddon;
use Masteriyo\Pro\Addons;

define( 'MASTERIYO_MAILERLITE_INTEGRATION_FILE', __FILE__ );
define( 'MASTERIYO_MAILERLITE_INTEGRATION_BASENAME', plugin_basename( __FILE__ ) );
define( 'MASTERIYO_MAILERLITE_INTEGRATION_DIR', __DIR__ );
define( 'MASTERIYO_MAILERLITE_INTEGRATION_TEMPLATES', __DIR__ . '/templates' );
define( 'MASTERIYO_MAILERLITE_INTEGRATION_SLUG', 'mailerlite-integration' );
define( 'MASTERIYO_MAILERLITE_INTEGRATION_BASE_URL', 'https://connect.mailerlite.com/api' );

// Bail early if the addon is not active.
if ( ! ( new Addons() )->is_active( MASTERIYO_MAILERLITE_INTEGRATION_SLUG ) ) {
	return;
}

// Initiate MailerLite Integration addon.
MailerLiteIntegrationAddon::instance()->init();
