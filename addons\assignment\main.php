<?php

defined( 'ABSPATH' ) || exit;

/**
 * Addon Name: Assignments
 * Addon URI: https://masteriyo.com/wordpress-lms/
 * Description: Create and assign tasks to the students with Masteriyo Assignments.
 * Author: Masteriyo
 * Author URI: https://masteriyo.com
 * Addon Type: enhancement
 * Plan: Starter,Pro,Elite,Growth,Scale
 */

use Masteriyo\Pro\Addons;

define( 'MASTERIYO_ASSIGNMENT_ADDON_FILE', __FILE__ );
define( 'MASTERIYO_ASSIGNMENT_ADDON_BASENAME', plugin_basename( __FILE__ ) );
define( 'MASTERIYO_ASSIGNMENT_ADDON_DIR', __DIR__ );
define( 'MASTERIYO_ASSIGNMENT_ADDON_SLUG', 'assignment' );

// Bail early if the addon is not active.
if ( ! ( new Addons() )->is_active( MASTERIYO_ASSIGNMENT_ADDON_SLUG ) ) {
	return;
}

require_once __DIR__ . '/helper.php';

/**
 * Include service providers for assignment
 */
add_filter(
	'masteriyo_service_providers',
	function( $providers ) {
		return array_merge( $providers, require_once __DIR__ . '/config/providers.php' );
	}
);

/**
 * Initialize Masteriyo assignment
 */
add_action(
	'masteriyo_before_init',
	function() {
		masteriyo( 'addons.assignment' )->init();
	}
);
