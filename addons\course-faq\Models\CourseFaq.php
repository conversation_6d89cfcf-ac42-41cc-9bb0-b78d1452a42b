<?php
/**
 * Faq model (comment type).
 *
 * @since 2.2.7
 *
 * @package Masteriyo\Addons\CourseFaqModels;
 */

namespace Masteriyo\Addons\CourseFaq\Models;

use Masteriyo\Database\Model;
use Masteriyo\Addons\CourseFaq\Repository\CourseFaqRepository;

defined( 'ABSPATH' ) || exit;

/**
 * Course Faq model (comment type).
 *
 * @since 2.2.7
 */
class CourseFaq extends Model {

	/**
	 * This is the title of this object type.
	 *
	 * @since 2.2.7
	 *
	 * @var string
	 */
	protected $object_type = 'course_faq';

	/**
	 * Comment type.
	 *
	 * @since 2.2.7
	 *
	 * @var string
	 */
	protected $comment_type = 'mto_course_faq';

	/**
	 * Cache group.
	 *
	 * @since 2.2.7
	 *
	 * @var string
	 */
	protected $cache_group = 'faq';

	/**
	 * Stores Faq data.
	 *
	 * @since 2.2.7
	 *
	 * @var array
	 */
	protected $data = array(
		'course_id'    => 0,
		'author_name'  => '',
		'author_email' => '',
		'author_url'   => '',
		'ip_address'   => '',
		'created_at'   => null,
		'title'        => '',
		'content'      => '',
		'menu_order'   => 0,
		'status'       => 'approve',
		'agent'        => '',
		'type'         => 'mto_course_faq',
		'parent_id'    => 0,
		'author_id'    => 0,
	);

	/**
	 * Get the faq if ID
	 *
	 * @since 2.2.7
	 *
	 * @param CourseFaqRepository $faq_repository Faq Repository.
	 */
	public function __construct( CourseFaqRepository $faq_repository ) {
		$this->repository = $faq_repository;
	}

	/**
	 * Return object type.
	 *
	 * @since 2.2.7
	 */
	public function get_object_type() {
		return $this->object_type;
	}

	/*
	|--------------------------------------------------------------------------
	| Getters
	|--------------------------------------------------------------------------
	*/

	/**
	 * Get Faq title.
	 *
	 * @since  2.2.7
	 *
	 * @param  string $context What the value is for. Valid values are view and edit.
	 *
	 * @return string
	 */
	public function get_title( $context = 'view' ) {
		/**
		 * Filters FAQ title.
		 *
		 * @since 2.2.7
		 *
		 * @param string $title FAQ title.
		 * @param Masteriyo\Models\Faq $faq FAQ object.
		 */
		return apply_filters( 'masteriyo_course_faq_title', $this->get_prop( 'title', $context ), $this );
	}

	/**
	 * Get faq content.
	 *
	 * @since  2.2.7
	 *
	 * @param  string $context What the value is for. Valid values are view and edit.
	 *
	 * @return string
	 */
	public function get_content( $context = 'view' ) {
		return $this->get_prop( 'content', $context );
	}

	/**
	 * Get type.
	 *
	 * @since  2.2.7
	 *
	 * @param  string $context What the value is for. Valid values are view and edit.
	 *
	 * @return string
	 */
	public function get_type( $context = 'view' ) {
		return $this->get_prop( 'type', $context );
	}

	/**
	 * Returns faq course id.
	 *
	 * @since  2.2.7
	 *
	 * @param  string $context What the value is for. Valid values are view and edit.
	 *
	 * @return int
	 */
	public function get_course_id( $context = 'view' ) {
		return $this->get_prop( 'course_id', $context );
	}

	/**
	 * Returns course faq parent id.
	 *
	 * @since  2.2.7
	 *
	 * @param  string $context What the value is for. Valid values are view and edit.
	 *
	 * @return int
	 */
	public function get_parent_id( $context = 'view' ) {
		return $this->get_prop( 'parent_id', $context );
	}

	/**
	 * Get author ID.
	 *
	 * @since 2.2.7
	 *
	 * @param  string $context What the value is for. Valid values are view and edit.
	 *
	 * @return int
	 */
	public function get_author_id( $context = 'view' ) {
		return $this->get_prop( 'author_id', $context );
	}

	/**
	 * Get author name.
	 *
	 * @since 2.2.7
	 *
	 * @param  string $context What the value is for. Valid values are view and edit.
	 *
	 * @return string
	 */
	public function get_author_name( $context = 'view' ) {
		return $this->get_prop( 'author_name', $context );
	}

	/**
	 * Get author email.
	 *
	 * @since 2.2.7
	 *
	 * @param  string $context What the value is for. Valid values are view and edit.
	 *
	 * @return string
	 */
	public function get_author_email( $context = 'view' ) {
		return $this->get_prop( 'author_email', $context );
	}

	/**
	 * Get author url.
	 *
	 * @since 2.2.7
	 *
	 * @param  string $context What the value is for. Valid values are view and edit.
	 *
	 * @return string
	 */
	public function get_author_url( $context = 'view' ) {
		return $this->get_prop( 'author_url', $context );
	}

	/**
	 * Get author ip.
	 *
	 * @since 2.2.7
	 *
	 * @param  string $context What the value is for. Valid values are view and edit.
	 *
	 * @return string
	 */
	public function get_ip_address( $context = 'view' ) {
		return $this->get_prop( 'ip_address', $context );
	}

	/**
	 * Get author agent.
	 *
	 * @since 2.2.7
	 *
	 * @param  string $context What the value is for. Valid values are view and edit.
	 *
	 * @return string
	 */
	public function get_agent( $context = 'view' ) {
		return $this->get_prop( 'agent', $context );
	}

	/**
	 * Get faq status.
	 *
	 * @since 2.2.7
	 *
	 * @param  string $context What the value is for. Valid values are view and edit.
	 *
	 * @return string
	 */
	public function get_status( $context = 'view' ) {
		return $this->get_prop( 'status', $context );
	}

	/**
	 * Get faq created date.
	 *
	 * @since  2.2.7
	 *
	 * @param  string $context What the value is for. Valid values are view and edit.
	 *
	 * @return DateTime|NULL object if the date is set or null if there is no date.
	 */
	public function get_created_at( $context = 'view' ) {
		return $this->get_prop( 'created_at', $context );
	}

	/**
	 * Returns faq menu order.
	 *
	 * @since  2.2.7
	 *
	 * @param  string $context What the value is for. Valid values are view and edit.
	 *
	 * @return string
	 */
	public function get_menu_order( $context = 'view' ) {
		return $this->get_prop( 'menu_order', $context );
	}

	/*
	|--------------------------------------------------------------------------
	| Setters
	|--------------------------------------------------------------------------
	*/

	/**
	 * Set faq title.
	 *
	 * @since 2.2.7
	 *
	 * @param string $title faq title.
	 */
	public function set_title( $title ) {
		$this->set_prop( 'title', $title );
	}


	/**
	 * Set faq author name.
	 *
	 * @since 2.2.7
	 *
	 * @param string $author_name faq author_name.
	 */
	public function set_author_name( $author_name ) {
		$this->set_prop( 'author_name', $author_name );
	}

	/**
	 * Set faq content.
	 *
	 * @since 2.2.7
	 *
	 * @param string $content Faq content.
	 */
	public function set_content( $content ) {
		$this->set_prop( 'content', $content );
	}


	/**
	 * Set type.
	 *
	 * @since 2.2.7
	 *
	 * @param string $type Comment Type.
	 */
	public function set_type( $type ) {
		$this->set_prop( 'type', $type );
	}

	/**
	 * Set the faq course ID.
	 *
	 * @since 2.2.7
	 *
	 * @param int $value Course ID.
	 */
	public function set_course_id( $value ) {
		$this->set_prop( 'course_id', absint( $value ) );
	}

	/**
	 * Set the faq parent ID.
	 *
	 * @since 2.2.7
	 *
	 * @param int $value Course FAQ parent ID.
	 */
	public function set_parent_id( $value ) {
		$this->set_prop( 'parent_id', absint( $value ) );
	}

	/**
	 * Set the faq author ID.
	 *
	 * @since 2.2.7
	 *
	 * @param int $value author ID.
	 */
	public function set_author_id( $value ) {
		$this->set_prop( 'author_id', absint( $value ) );
	}

	/**
	 * Set author's email.
	 *
	 * @since 2.2.7
	 *
	 * @param string $value author's email.
	 */
	public function set_author_email( $value ) {
		$this->set_prop( 'author_email', $value );
	}

	/**
	 * Set author's url.
	 *
	 * @since 2.2.7
	 *
	 * @param string $value author's url.
	 */
	public function set_author_url( $value ) {
		$this->set_prop( 'author_url', $value );
	}

	/**
	 * Set author's ip.
	 *
	 * @since 2.2.7
	 *
	 * @param string $value author's ip.
	 */
	public function set_ip_address( $value ) {
		$this->set_prop( 'ip_address', $value );
	}

	/**
	 * Set author's agent.
	 *
	 * @since 2.2.7
	 *
	 * @param string $value author's agent.
	 */
	public function set_agent( $value ) {
		$this->set_prop( 'agent', $value );
	}

	/**
	 * Set the faq menu order.
	 *
	 * @since 2.2.7
	 *
	 * @param int $menu_order Menu order id.
	 */
	public function set_menu_order( $menu_order ) {
		$this->set_prop( 'menu_order', absint( $menu_order ) );
	}

	/**
	 * Set the faq status.
	 *
	 * @since 2.2.7
	 *
	 * @param string $status Faq status.
	 */
	public function set_status( $status ) {
		$this->set_prop( 'status', $status );
	}

	/**
	 * Set faq created date.
	 *
	 * @since 2.2.7
	 *
	 * @param string|integer|null $date UTC timestamp, or ISO 8601 DateTime. If the DateTime string has no timezone or offset, WordPress site timezone will be assumed. Null if their is no date.
	 */
	public function set_created_at( $date = null ) {
		$this->set_date_prop( 'created_at', $date );
	}
}
