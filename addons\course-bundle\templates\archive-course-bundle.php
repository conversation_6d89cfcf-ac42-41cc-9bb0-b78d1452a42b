<?php

/**
 * The Template for displaying course archives, including the main course bundles page which is a post type archive
 *
 * @package Masteriyo\Addons\CourseBundle\Templates
 * @version 2.12.0
 */

defined( 'ABSPATH' ) || exit;

masteriyo_custom_header( 'course-bundles' );

$enable_custom_template = masteriyo_string_to_bool( masteriyo_get_setting( 'course_archive.display.template.custom_template.enable' ) );
$template_source        = masteriyo_get_setting( 'course_archive.display.template.custom_template.template_source' );
$template_id            = masteriyo_get_setting( 'course_archive.display.template.custom_template.template_id' );

/**
 * Wrapper div opening.
 *
 * @since 2.12.0
 */
echo '<div class="masteriyo-w-100 masteriyo-container">';

/**
 * Fires before rendering header in course bundle archive.
 *
 * @since 2.12.0
 */
do_action( 'masteriyo_before_main_course_bundles_content' );

?>
<header class="masteriyo-bundles-header">
	<?php
	/**
	 * Filters boolean: true if page title should be shown.
	 *
	 * @since 2.12.0
	 *
	 * @param boolean $bool true if page title should be shown.
	 */
	if ( apply_filters( 'masteriyo_show_page_title', true ) ) :
		?>
		<h1 class="masteriyo-bundles-header__title page-title">
			<?php masteriyo_page_title(); ?>
		</h1>
	<?php endif; ?>

	<?php
	/**
	 * Action hook for rendering course bundle archive description.
	 *
	 * @since 2.12.0
	 */
	do_action( 'masteriyo_course_bundle_archive_description' );
	?>
</header>

<?php
/**
 * Fires after rendering header in course bundle archive.
 *
 * @since 2.12.0
 */
do_action( 'masteriyo_after_course_bundle_archive_header' );
?>

<div class="masteriyo-bundle-list-display-section">

	<?php

	/**
	 * Fires before course loop in course bundle archive template.
	 *
	 * Fires regardless of whether there are courses to be displayed or not.
	 *
	 * @since 2.12.0
	 */
	do_action( 'masteriyo_before_course_bundle_archive_loop' );

	if ( masteriyo_course_loop() ) {

		/**
		 * Fires before course loop in course bundle archive template.
		 *
		 * @since 2.12.0
		 */
		do_action( 'masteriyo_before_course_bundles_loop' );

		masteriyo_course_bundle_loop_start();

		if ( masteriyo_get_loop_prop( 'total' ) ) {
			while ( have_posts() ) {
				the_post();

				/**
				 * Fires for each item in course loop before rendering its template.
				 *
				 * @since 2.12.0
				 */
				do_action( 'masteriyo_course_bundles_loop' );

				masteriyo_get_template( 'course-bundle/content-course-bundle.php' );

			}
		}

		masteriyo_course_bundle_loop_end();

		/**
		 * Fires after course loop in course bundle archive template.
		 *
		 * @since 2.12.0
		 */
		do_action( 'masteriyo_after_course_bundles_loop' );
	} else {
		/**
		 * Fires when there is not course to display in course bundle archive.
		 *
		 * @since 2.12.0
		 */
		do_action( 'masteriyo_no_course_bundles_found' );
	}

	echo '</div>';

	/**
	 * Fires after rendering course archive main content.
	 *
	 * @since 2.12.0
	 */
	do_action( 'masteriyo_after_main_course_bundles_content' );

	/**
	 * Action hook for rendering sidebar in course bundle archive.
	 *
	 *
	 * @since 2.12.0
	 */
	do_action( 'masteriyo_sidebar' );

	/**
	 * Wrapper div closing.
	 *
	 * @since 2.12.0
	 */
	echo '</div>';

	masteriyo_custom_footer( 'course-bundles' );
