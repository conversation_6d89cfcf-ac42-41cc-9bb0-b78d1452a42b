<?php

defined( 'ABSPATH' ) || exit;

/**
 * Addon Name: Manual Enrollment
 * Addon URI: https://masteriyo.com/wordpress-lms/
 * Description: Allows to manually enroll students.
 * Author: Masteriyo
 * Author URI: https://masteriyo.com
 * Addon Type: enhancement
 * Plan: Basic,Starter,Pro,Elite,Growth,Scale
 */

use Masteriyo\Addons\ManualEnrollment\ManualEnrollmentAddon;
use Masteriyo\Pro\Addons;

define( 'MASTERIYO_MANUAL_ENROLLMENT_ADDON_FILE', __FILE__ );
define( 'MASTERIYO_MANUAL_ENROLLMENT_ADDON_BASENAME', plugin_basename( __FILE__ ) );
define( 'MASTERIYO_MANUAL_ENROLLMENT_ADDON_DIR', __DIR__ );
define( 'MASTERIYO_MANUAL_ENROLLMENT_ASSETS', __DIR__ . '/assets' );
define( 'MASTERIYO_MANUAL_ENROLLMENT_TEMPLATES', __DIR__ . '/templates' );
define( 'MASTERIYO_MANUAL_ENROLLMENT_ADDON_SLUG', 'manual-enrollment' );

// Bail early if the addon is not active.
if ( ! ( new Addons() )->is_active( MASTERIYO_MANUAL_ENROLLMENT_ADDON_SLUG ) ) {
	return;
}

/**
 * Include service providers for Manual Enrollment.
 */
add_filter(
	'masteriyo_service_providers',
	function( $providers ) {
		return array_merge( $providers, require_once __DIR__ . '/config/providers.php' );
	}
);

/**
 * Initialize Masteriyo Manual Enrollment.
 */
ManualEnrollmentAddon::instance()->init();
