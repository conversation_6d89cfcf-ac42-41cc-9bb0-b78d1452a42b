<?php

defined( 'ABSPATH' ) || exit;

/**
 * Addon Name: EDD Integration
 * Addon URI: https://masteriyo.com/wordpress-lms/
 * Addon Type: integration
 * Description: EDD Integration allows to enroll users using EDD checkout process and payment methods.
 * Author: Masteriyo
 * Author URI: https://masteriyo.com
 * Version: 2.6.8
 * Requires: Easy Digital Downloads
 * Plan: Starter,Pro,Elite,Growth,Scale
 */

use Masteriyo\Pro\Addons;
use Masteriyo\Addons\EDDIntegration\Helper;
use Masteriyo\Addons\EDDIntegration\EDDIntegrationAddon;

define( 'MASTERIYO_EDD_INTEGRATION_ADDON_FILE', __FILE__ );
define( 'MASTERIYO_EDD_INTEGRATION_ADDON_BASENAME', plugin_basename( __FILE__ ) );
define( 'MASTERIYO_EDD_INTEGRATION_ADDON_DIR', __DIR__ );
define( 'MASTERIYO_EDD_INTEGRATION_ASSETS', __DIR__ . '/assets' );
define( 'MASTERIYO_EDD_INTEGRATION_TEMPLATES', __DIR__ . '/templates' );
define( 'MASTERIYO_EDD_INTEGRATION_ADDON_SLUG', 'edd-integration' );


if ( ( new Addons() )->is_active( MASTERIYO_EDD_INTEGRATION_ADDON_SLUG ) && ! Helper::is_edd_active() ) {
	add_action(
		'masteriyo_admin_notices',
		function() {
			printf(
				'<div class="notice notice-warning is-dismissible"><p><strong>%s </strong>%s</p><button type="button" class="notice-dismiss"><span class="screen-reader-text">%s</span></button></div>',
				esc_html( 'Masteriyo PRO:' ),
				wp_kses_post( __( 'Easy Digital Downloads Integration addon requires Easy Digital Downloads to be installed and activated.', 'learning-management-system' ) ),
				esc_html__( 'Dismiss this notice.', 'learning-management-system' )
			);
		}
	);
}

// Bail early if EDD is not activated.
if ( ! Helper::is_edd_active() ) {
	add_filter(
		'masteriyo_pro_addon_edd-integration_activation_requirements',
		function ( $result, $request, $controller ) {
			$result = __( 'Easy Digital Downloads is to be installed and activated for this addon to work properly', 'learning-management-system' );
			return $result;
		},
		10,
		3
	);

	add_filter(
		'masteriyo_pro_addon_data',
		function( $data, $slug ) {
			if ( 'edd-integration' === $slug ) {
				$data['requirement_fulfilled'] = masteriyo_bool_to_string( Helper::is_edd_active() );
			}

			return $data;
		},
		10,
		2
	);
}


// Bail early if the addon is not active.
if ( ! ( new Addons() )->is_active( MASTERIYO_EDD_INTEGRATION_ADDON_SLUG ) ) {
	return;
}

// Initialize edd integration addon.
EDDIntegrationAddon::instance()->init();
