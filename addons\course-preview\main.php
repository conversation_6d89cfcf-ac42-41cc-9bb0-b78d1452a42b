<?php

defined( 'ABSPATH' ) || exit;

/**
 * Addon Name: Course Preview
 * Addon URI: https://masteriyo.com/wordpress-lms/
 * Description: Allow students to have a sneak peek at lessons prior to enrolling in a course.
 * Author: Masteriyo
 * Author URI: https://masteriyo.com
 * Addon Type: Feature
 * Plan: Basic,Starter,Pro,Elite,Growth,Scale
 */

use Masteriyo\Pro\Addons;
use Masteriyo\Addons\CoursePreview\CoursePreviewAddon;

define( 'MASTERIYO_COURSE_PREVIEW_ADDON_FILE', __FILE__ );
define( 'MASTERIYO_COURSE_PREVIEW_ADDON_BASENAME', plugin_basename( __FILE__ ) );
define( 'MASTERIYO_COURSE_PREVIEW_ADDON_DIR', __DIR__ );
define( 'MASTERIYO_COURSE_PREVIEW_ADDON_SLUG', 'course-preview' );
define( 'MASTERIYO_COURSE_PREVIEW_ADDON_TEMPLATES', __DIR__ . '/templates' );
define( 'MASTERIYO_COURSE_PREVIEW_ADDON_ASSETS_URL', plugins_url( 'assets', MASTERIYO_COURSE_PREVIEW_ADDON_FILE ) );

// Bail early if the addon is not active.
if ( ! ( new Addons() )->is_active( MASTERIYO_COURSE_PREVIEW_ADDON_SLUG ) ) {
	return;
}

require_once __DIR__ . '/helper/course-preview.php';

// Initiate public profile addon.
CoursePreviewAddon::instance()->init();
