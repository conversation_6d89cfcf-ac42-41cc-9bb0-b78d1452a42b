<?php

defined( 'ABSPATH' ) || exit;

/**
 * Course bundle helper functions.
 *
 * @since 2.12.0
 */

use Masteriyo\Addons\CourseBundle\Models\CourseBundle;
use Masteriyo\Enums\OrderStatus;
use Masteriyo\Enums\PostStatus;
use Masteriyo\PostType\PostType;
use Master<PERSON>yo\Pro\Addons;

if ( ! function_exists( 'masteriyo_is_single_course_bundle_page' ) ) {
	/**
	 * Is single course bundle page
	 *
	 * @since 2.12.0
	 * @return boolean
	 */
	function masteriyo_is_single_course_bundle_page() {
		return is_singular( PostType::COURSE_BUNDLE );
	}
}

if ( ! function_exists( 'masteriyo_is_archive_course_bundle_page' ) ) {
	/**
	 * Is course bundle archive page
	 *
	 * @since 2.12.0
	 *
	 * @param bool $check_shortcode Check if the page has the shortcode.
	 *
	 * @return boolean
	 */
	function masteriyo_is_archive_course_bundle_page( $check_shortcode = false ) {
		if ( is_post_type_archive( PostType::COURSE_BUNDLE ) || is_page( masteriyo_get_page_id( 'course-bundles' ) ) ) {
			return true;
		}

		if ( $check_shortcode ) {
			global $post;

			if ( ( $post && isset( $post->post_content ) && has_shortcode( $post->post_content, 'masteriyo_course_bundles' ) ) ) {
				return true;

			}
		}

		return false;
	}
}

if ( ! function_exists( 'masteriyo_get_course_bundle' ) ) {
	/**
	 * Get course bundle.
	 *
	 * @since 2.12.0
	 * @param int|CourseBundle|WP_Post $course_bundle Course bundle id or CourseBundle Model or Post.
	 * @return null|CourseBundle
	 */
	function masteriyo_get_course_bundle( $course_bundle ) {
		$course_bundle_object = masteriyo( 'course-bundle' );
		$course_bundle_store  = masteriyo( 'course-bundle.store' );

		if ( is_a( $course_bundle, 'Masteriyo\Addons\CourseBundle\Models\CourseBundle' ) ) {
			$id = $course_bundle->get_id();
		} elseif ( is_a( $course_bundle, 'WP_Post' ) ) {
			$id = $course_bundle->ID;
		} else {
			$id = absint( $course_bundle );
		}
		try {
			$id = absint( $id );
			$course_bundle_object->set_id( $id );
			$course_bundle_store->read( $course_bundle_object );
		} catch ( \Exception $e ) {
			return null;
		}

		/**
		 * Filters course bundle object.
		 *
		 * @since 2.12.0
		 *
		 * @param CourseBundle $course_obj Course bundle object.
		 * @param int|CourseBundle|WP_Post $course Course bundle id or Course Model or Post.
		 */
		return apply_filters( 'masteriyo_get_course_bundle', $course_bundle_object, $course_bundle );
	}
}

if ( ! function_exists( 'masteriyo_get_total_bundle_sold_count' ) ) {
	/**
	 * Get total sold number of a course bundle.
	 *
	 * @since 2.12.0
	 *
	 * @param int $bundle_id course bundle id.
	 *
	 * @return int
	 */
	function masteriyo_get_total_bundle_sold_count( $bundle_id ) {
		global $wpdb;

		$count = $wpdb->get_var(
			$wpdb->prepare(
				"
				SELECT COUNT(DISTINCT oi.order_id)
				FROM {$wpdb->prefix}masteriyo_order_items oi
				INNER JOIN {$wpdb->prefix}masteriyo_order_itemmeta oim ON oi.order_item_id = oim.order_item_id
				INNER JOIN {$wpdb->posts} p ON oi.order_id = p.ID
				WHERE oi.order_item_type = %s
				  AND oim.meta_key = %s
				  AND oim.meta_value = %d
				  AND p.post_type = %s
				  AND p.post_status = %s
				",
				'course-bundle',
				'course_bundle_id',
				$bundle_id,
				PostType::ORDER,
				OrderStatus::COMPLETED
			)
		);

		return absint( $count );
	}
}

if ( ! function_exists( 'masteriyo_get_order_ids_by_course_bundle_id' ) ) {
	/**
	 * Get the order IDs for a given course bundle ID that belong to the currently logged-in user.
	 *
	 * @since 2.12.0
	 *
	 * @param int $course_bundle_id The ID of the course bundle.
	 *
	 * @return array|null The order IDs, or null if no orders are found.
	 */
	function masteriyo_get_order_ids_by_course_bundle_id( $course_bundle_id ) {
		if ( ! is_user_logged_in() ) {
			return null;
		}

		$current_user_id = get_current_user_id();

		global $wpdb;

		$order_item_ids = $wpdb->get_col(
			$wpdb->prepare(
				"SELECT order_item_id
					FROM {$wpdb->prefix}masteriyo_order_itemmeta
					WHERE meta_key = 'course_bundle_id' AND meta_value = %d",
				$course_bundle_id
			)
		);

		if ( empty( $order_item_ids ) ) {
			return null;
		}

		// phpcs:disable
		$order_ids = $wpdb->get_col(
			"SELECT DISTINCT oi.order_id
			FROM {$wpdb->prefix}masteriyo_order_items oi
			INNER JOIN $wpdb->postmeta pm ON oi.order_id = pm.post_id
			WHERE oi.order_item_id IN (" . implode( ',', array_map( 'intval', $order_item_ids ) ) . ")
			AND pm.meta_key = '_customer_id'
			AND pm.meta_value = " . intval( $current_user_id ) . '
			'
		);
		// phpcs:enable

		if ( empty( $order_ids ) ) {
			return null;
		}

		return $order_ids;
	}
}

if ( ! function_exists( 'masteriyo_is_course_bundle_order_completed' ) ) {
	/**
	 * Check if any order is completed for the given order IDs.
	 *
	 * @since 2.12.0
	 *
	 * @param array $order_ids Array of order IDs.
	 *
	 * @return bool True if any order is completed, false otherwise.
	 */
	function masteriyo_is_course_bundle_order_completed( $order_ids = array() ) {
		if ( empty( $order_ids ) ) {
			return false;
		}

		foreach ( $order_ids as $order_id ) {
			$order_status = get_post_status( $order_id );

			if ( OrderStatus::COMPLETED === $order_status ) {
				return true;
			}
		}

		return false;
	}
}

if ( ! function_exists( 'masteriyo_is_course_bundle_active' ) ) {
	/**
	 * Check if masteriyo course bundle addons is active.
	 *
	 * @since 2.14.0
	 *
	 * @return bool True if course bundle addons is active.
	 */
	function masteriyo_is_course_bundle_active() {
		if ( ! ( new Addons() )->is_active( MASTERIYO_COURSE_BUNDLE_ADDON_SLUG ) ) {
			return;
		}
		return true;
	}
}

/**
 * Checks if the course bundle carousel is enabled.
 *
 * This function returns a boolean value indicating whether the course bundle carousel is enabled.
 *
 * @since 2.14.0
 *
 * @return bool True if the course bundle carousel is enabled, false otherwise.
 */
if ( ! function_exists( 'masteriyo_is_course_bundle_carousel_enabled' ) ) {
	/**
	 * Checks if the course bundle carousel is enabled.
	 *
	 * This function returns a boolean value indicating whether the course bundle carousel is enabled.
	 *
	 * @since 2.14.0
	 *
	 * @return bool True if the course bundle carousel is enabled, false otherwise.
	 */
	function masteriyo_is_course_bundle_carousel_enabled() {
		/**
		 * Checks if the course bundle carousel is enabled.
		 *
		 * This function returns a boolean value indicating whether the course bundle carousel is enabled.
		 *
		 * @since 2.14.0
		 *
		 * @return bool True if the course bundle carousel is enabled, false otherwise.
		 */
		return apply_filters( 'masteriyo_is_course_bundle_carousel_enabled', false );
	}
}

	/**
	 * gets the course price for the each course in the course bundle.
	 *
	 * This function returns a total price value for the courses.
	 *
	 * @since 2.14.0
	 *
	 * @return number The courses price.
	 */
if ( ! function_exists( 'masteriyo_courses_of_course_bundle' ) ) {
	/**
	 * gets the course price for the each course in the course bundle.
	 *
	 * This function returns a total price value for the courses.
	 *
	 * @since 2.14.0
	 *
	 * @return number The courses price.
	 */
	function masteriyo_courses_of_course_bundle( $course_bundle, $courses, $is_multiple_currency_enabled ) {

		$courses_price = array_sum(
			array_map(
				function ( $course ) {
					return $course->get_price();
				},
				$courses
			)
		);

		if ( $courses_price && $is_multiple_currency_enabled && ( is_int( $courses_price ) || is_float( $courses_price ) ) ) {
			return apply_filters( 'masteriyo_courses_of_course_bundle', $course_bundle, $courses_price );
		} else {
			return $courses_price;
		}

	}
}


	/**
	 * gets the new filtered new course bundle data.
	 *
	 * @since 2.14.0
	 *
	 * @return number The courses price.
	 */
if ( ! function_exists( 'masteriyo_course_archive_bundle' ) ) {
	/**
	 * gets the new filtered new course bundle data.
	 *
	 *
	 * @since 2.14.0
	 *
	 * @return number The courses price.
	 */
	function masteriyo_course_archive_bundle( $course_bundle, $bundle_rate = false ) {

		$course_bundle = apply_filters( 'masteriyo_course_archive_bundle', $course_bundle, $bundle_rate );

		return $course_bundle;
	}
}

