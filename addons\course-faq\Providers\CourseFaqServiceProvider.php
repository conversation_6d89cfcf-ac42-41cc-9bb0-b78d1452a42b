<?php
/**
 * CourseFaq service provider.
 *
 * @since 2.2.7
 * @package \Masteriyo\Addons\CourseFaq
 */

namespace Masteriyo\Addons\CourseFaq\Providers;

defined( 'ABSPATH' ) || exit;

use Masteriyo\Addons\CourseFaq\CourseFaqAddon;
use Masteriyo\Addons\CourseFaq\Models\CourseFaq;
use League\Container\ServiceProvider\AbstractServiceProvider;
use Masteriyo\Addons\CourseFaq\CourseFaqQuery;
use Masteriyo\Addons\CourseFaq\Repository\CourseFaqRepository;
use Masteriyo\Addons\CourseFaq\RestApi\Controllers\Version1\CourseFaqsController;

/**
 * CourseFaq service provider.
 *
 * @since 2.2.7
 */
class CourseFaqServiceProvider extends AbstractServiceProvider {
	/**
	 * The provided array is a way to let the container
	 * know that a service is provided by this service
	 * provider. Every service that is registered via
	 * this service provider must have an alias added
	 * to this array or it will be ignored
	 *
	 * @since 2.2.7
	 *
	 * @var array
	 */
	protected $provides = array(
		'addons.course-faq',
		'\Masteriyo\Addons\CourseFaq\CourseFaq',
		'course-faq',
		'course-faq.store',
		'course-faq.rest',
		'mto_course_faq',
		'mto_course-faq.store',
		'mto_course-faq.rest',
		'\Masteriyo\RestApi\Controllers\Version1\FaqsController',
	);

	/**
	 * This is where the magic happens, within the method you can
	 * access the container and register or retrieve anything
	 * that you need to, but remember, every alias registered
	 * within this method must be declared in the `$provides` array.
	 *
	 * @since 2.2.7
	 */
	public function register() {
		$this->getContainer()->add( 'addons.course-faq', CourseFaqAddon::class, true );

		$this->getContainer()->add( 'course-faq.store', CourseFaqRepository::class );

		$this->getContainer()->add( 'course-faq.rest', CourseFaqsController::class )
			->addArgument( 'permission' );

		$this->getContainer()->add( '\Masteriyo\Addons\CourseFaq\RestApi\Controllers\Version1\CourseFaqsController' )
			->addArgument( 'permission' );

		$this->getContainer()->add( 'course-faq', CourseFaq::class )
			->addArgument( 'course-faq.store' );

		$this->getContainer()->add( 'mto_course_faq.store', CourseFaqRepository::class );

		$this->getContainer()->add( 'mto_course_faq.rest', CourseFaqsController::class )
			->addArgument( 'permission' );

		$this->getContainer()->add( 'mto_course_faq', CourseFaq::class )
			->addArgument( 'course-faq.store' );

		$this->getContainer()->add( 'query.course-faqs', CourseFaqQuery::class );
	}
}
