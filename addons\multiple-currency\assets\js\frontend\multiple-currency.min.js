jQuery(function(c){if("undefined"==typeof _MASTERIYO_CHECKOUT_)return!1;var s={$form:c("form.masteriyo-checkout"),init:function(){this.$form.on("change",".masteriyo-checkout-summary-switch-currency-select",this.switchCurrencyHandler)},getAjaxURL:function(){return _MASTERIYO_CHECKOUT_.ajaxURL},removeErrorNotices:function(){c(".masteriyo-NoticeGroup-checkout, .masteriyo-error, .masteriyo-message").remove()},showError:function(e){s.$form.prepend('<div class="masteriyo-NoticeGroup masteriyo-NoticeGroup-checkout">'+e+"</div>"),s.$form.find(".input-text, select, input:checkbox").trigger("validate").trigger("blur"),s.scrollToNotices(),c(document.body).trigger("checkout_error",[e])},scrollToNotices:function(){var e=c(".masteriyo-NoticeGroup-updateOrderReview, .masteriyo-NoticeGroup-checkout");(e=e.length?e:c("form.masteriyo-checkout")).length&&c("html, body").animate({scrollTop:e.offset().top-100},1e3)},switchCurrencyHandler:function(e){var t=c(".masteriyo-checkout-summary-switch-currency-select").val().trim();t&&!s.$form.is(".processing")&&c.ajax({type:"POST",url:s.getAjaxURL(),dataType:"json",data:{action:"masteriyo_switch_currency",_wpnonce:c('[name="masteriyo-switch-currency-nonce"]').val(),currency:t},beforeSend:function(e){s.removeErrorNotices(),s.$form.block({message:'<span class="spinner" style="visibility:visible"></span>',css:{border:"",width:"0%"},overlayCSS:{background:"#fff",opacity:.6}})},success:function(e,r,o){e.success?(c.each(e.data.fragments,function(e,r){s.fragments&&s.fragments[e]===r||c(e).replaceWith(r)}),s.fragments=e.data.fragments,c(document.body).trigger("masteriyo_currency_switched",{currency:t,response:e})):s.showError('<div class="masteriyo-error">'+e.data.message+"</div>")},error:function(e,r,o){try{var t=e.responseJSON;s.showError('<div class="masteriyo-error">'+t.data.messages+"</div>")}catch(t){console.log(t)}},complete:function(e,r){s.$form.unblock()}})}};s.init()});