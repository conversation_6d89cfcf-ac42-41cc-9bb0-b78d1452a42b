<?php

defined( 'ABSPATH' ) || exit;

/**
 * Addon Name: Mailchimp Integration
 * Addon URL: https://masteriyo.com/wordpress-lms/
 * Description: Seamlessly integrate Mailchimp with Masteriyo LMS for efficient and automated email marketing within your learning management system.
 * Author: Masteriyo
 * Author URL: https://masteriyo.com
 * Addon Type: integration
 * Plan: Starter,Pro,Elite,Growth,Scale
 */

use Masteriyo\Addons\MailchimpIntegration\MailchimpIntegrationAddon;
use Masteriyo\Pro\Addons;

define( 'MASTERIYO_MAILCHIMP_INTEGRATION_FILE', __FILE__ );
define( 'MASTERIYO_MAILCHIMP_INTEGRATION_SLUG', 'mailchimp-integration' );
define( 'MASTERIYO_MAILCHIMP_INTEGRATION_TEMPLATES', __DIR__ . '/templates' );
define( 'MASTERIYO_MAILCHIMP_INTEGRATION_BASENAME', plugin_basename( __FILE__ ) );
define( 'MASTERIYO_MAILCHIMP_INTEGRATION_DIR', __DIR__ );
define( 'MASTERIYO_MAILCHIMP_INTEGRATION_BASE_URL', 'https://<dc>.api.mailchimp.com/3.0/' );

//Bail early if the addon is not active.
if ( ! ( new Addons() )->is_active( MASTERIYO_MAILCHIMP_INTEGRATION_SLUG ) ) {
	return;
}


// Initiate Mailchimp Integration addon.
MailchimpIntegrationAddon::instance()->init();
