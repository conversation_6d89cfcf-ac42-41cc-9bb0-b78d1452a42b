<?php
/**
 * Destroy/tear down edd integration.
 *
 * @since 2.6.8
 */

defined( 'ABSPATH' ) || exit;

use Masteriyo\Capabilities;

// Remove Masteriyo manager capabilities from EDD shop manager.
$manager = get_role( 'shop_manager' );
if ( $manager ) {
	foreach ( Capabilities::get_manager_capabilities() as $cap => $grant ) {
		$manager->remove_cap( $cap );
	}
}

// Remove student role to all the EDD Customers.
if ( function_exists( 'edd_get_customers' ) ) {
	$customers = edd_get_customers(
		array(
			'number' => -1,
		)
	);

	foreach ( $customers as $customer ) {
		$customer = new WP_User( $customer->id );

		if ( in_array( 'masteriyo_student', $customer->roles, true ) ) {
			$customer->remove_role( 'masteriyo_student' );
		}
	}
}
