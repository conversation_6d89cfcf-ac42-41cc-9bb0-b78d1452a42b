<?php

defined( 'ABSPATH' ) || exit;

use Masteriyo\Addons\Razorpay\Setting;

if ( ! function_exists( 'masteriyo_razorpay_get_key_secret' ) ) {
	/**
	 * Get the Razorpay publishable and secret keys based on the sandbox mode.
	 *
	 * If the sandbox mode is enabled, it returns the test publishable and secret keys.
	 * Otherwise, it returns the live publishable and secret keys.
	 *
	 * @since 2.7.1
	 *
	 * @return array An array containing the publishable and secret keys.
	 */
	function masteriyo_razorpay_get_key_secret() {
		if ( masteriyo_razorpay_test_mode_enabled() ) {
			return array(
				Setting::get( 'test_publishable_key' ),
				Setting::get( 'test_secret_key' ),
			);
		}

		return array(
			Setting::get( 'live_publishable_key' ),
			Setting::get( 'live_secret_key' ),
		);
	}
}

if ( ! function_exists( 'masteriyo_razorpay_test_mode_enabled' ) ) {
	/**
	 * Checks if Razorpay test mode is enabled.
	 *
	 * @since 2.9.2
	 *
	 * @return bool True if test mode is enabled, false otherwise.
	 */
	function masteriyo_razorpay_test_mode_enabled() {
		return masteriyo_string_to_bool( Setting::get( 'sandbox' ) );
	}
}
