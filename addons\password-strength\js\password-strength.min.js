let{__,sprintf}=wp.i18n;!function(r,s){function t(e){r(".masteriyo-password-strength-response").remove();var t=s.calculatePasswordStrength(e);s.highlightPasswordPolicy(e),s.renderPasswordStrengthMeter(".masteriyo-password-strength-meter",e,t.score),0===e.length?r(".masteriyo-password-strength-information").find("li").css("color",""):(e=s.convertStrengthToText(t.score),t=0===t.score||1===t.score?"danger":"success",e=s.createMessage(e,t),t=r("#password"),s.displayMessage(t,e))}void 0===s.policyList&&(s.policyList=function(e){var t=[];return t.push({label:sprintf(__("Minimum of %d characters","learning-management-system"),e.minLength),className:"min-length"}),t.push({label:sprintf(__("Maximum of %d characters","learning-management-system"),e.maxLength),className:"max-length"}),"low"!==e.strength&&"medium"!==e.strength&&"high"!==e.strength||t.push({label:sprintf(__("At least one uppercase letter","learning-management-system")),className:"uppercase"}),"medium"!==e.strength&&"high"!==e.strength||t.push({label:sprintf(__("At least one number","learning-management-system")),className:"number"}),"high"===e.strength&&t.push({label:sprintf(__("At least one special character","learning-management-system")),className:"special-character"}),t}),void 0===s.displayPolicyList&&(s.displayPolicyList=function(e){var t=['<div class="masteriyo-password-strength-information"><ul>'];s.policyList(s).forEach(function(e){t.push('<li class="'+e.className+'">'+e.label+"</li>")}),t.push("</ul></div>"),r(e).after(t.join(""))}),void 0===s.validate&&(s.validate=function(e,t){var s=[];return e.length>=t.minLength?s.push({valid:!0,type:"min-length"}):s.push({valid:!1,type:"min-length"}),e.length<=t.maxLength?s.push({valid:!0,type:"max-length"}):s.push({valid:!1,type:"max-length"}),"low"!==t.strength&&"medium"!==t.strength&&"high"!==t.strength||(e.match(/^(?=.*?[A-Z]).+/)?s.push({valid:!0,type:"uppercase"}):s.push({valid:!1,type:"uppercase"})),"medium"!==t.strength&&"high"!==t.strength||(e.match(/^(?=.*?[\d+]).+/)?s.push({valid:!0,type:"number"}):s.push({valid:!1,type:"number"})),"high"===t.strength&&(e.match(/^(?=.*?[\W+]).+/)?s.push({valid:!0,type:"special-character"}):s.push({valid:!1,type:"special-character"})),{valid:s.reduce(function(e,t){return e&&t.valid},!0),errors:s}}),void 0===s.createMessage&&(s.createMessage=function(e,t){t='<div class="masteriyo-password-strength-response masteriyo-notify-message masteriyo-alert masteriyo-'+t+'-msg">';return t+("<span>"+e+"</span>")+"</div>"}),void 0===s.displayMessage&&(s.displayMessage=function(e,t){s.showStrength&&r(e).after(t)}),void 0===s.calculatePasswordStrength&&(s.calculatePasswordStrength=function(e,t){return zxcvbn(e,t)}),void 0===s.renderPasswordStrengthMeterContainer&&(s.renderPasswordStrengthMeterContainer=function(e){r(e).after('<div class="masteriyo-password-strength-meter"></div>')}),void 0===s.renderPasswordStrengthMeter&&(s.renderPasswordStrengthMeter=function(e,t,s){0===t.length?r(e).attr("class","").addClass("masteriyo-password-strength-meter"):r(e).attr("class","").addClass("masteriyo-password-strength-meter strength-"+s)}),void 0===s.highlightPasswordPolicy&&(s.highlightPasswordPolicy=function(e){s.validate(e,s).errors.forEach(function(e){var t=e.valid?"green":"";r(".masteriyo-password-strength-information ."+e.type).css("color",t)})}),void 0===s.convertStrengthToText&&(s.convertStrengthToText=function(e){var t="";switch(e){case 0:t=__("Very Weak","learning-management-system");break;case 1:t=__("Weak","learning-management-system");break;case 2:t=__("Good","learning-management-system");break;case 3:t=__("Strong","learning-management-system");break;case 4:t=__("Very Strong","learning-management-system");break;default:t=__("Very Weak","learning-management-system")}return t}),r("#masteriyo-signup--form, #masteriyo-instructor-registration--form").on("submit",function(e){var t=r("#password").val();s.validate(t,s).valid||e.preventDefault()}),r(document).ready(function(){var e=r("#masteriyo-signup--form, #masteriyo-instructor-registration--form").find("#password");e.length&&""!==e.val().trim()&&t(e.val().trim())}),r("#masteriyo-signup--form, #masteriyo-instructor-registration--form").on("input","#password",function(){t(r(this).val().trim())}),s.displayPolicyList("#masteriyo-signup--form #password"),s.displayPolicyList("#masteriyo-instructor-registration--form #password"),s.showStrength||s.renderPasswordStrengthMeterContainer("#password")}(jQuery,(window._MASTERIYO_,window._MASTERIYO_PASSWORD_STRENGTH_));