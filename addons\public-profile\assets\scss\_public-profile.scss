body {
	font-style: normal;
}

a {
	color: #3a3a3a;
	transition: all 0.3s ease-in-out;

	&:hover {
		color: #4584ff;
	}
}

a,
a:focus,
a:focus-visible,
a:hover {
	text-decoration: none;
	outline: none;
}

p {
	margin-bottom: 0;
}

figure {
	margin: 0;
}

#masteriyo {
	padding: 60px 0;

	.container {
		max-width: 1120px;
		margin: 0 auto;
	}

	.masteriyo-row {
		display: flex;
		flex-wrap: wrap;
		margin-left: 20px;
		margin-right: 20px;
		position: relative;

		@media (min-width: 1200px) {
			margin-left: 0;
			margin-right: 0;
		}

		.masteriyo-col-left {
			width: 25%;
			padding-right: 20px;

			@media (max-width: 767px) {
				position: absolute;
				// background: #fff;
				background: rgb(145, 145, 145);
				width: 300px;
				padding-right: 0;
				margin-left: -20px;
				z-index: 999999;
				display: none;
			}

			&-wrapper {
				@media (max-width: 767px) {
					padding: 20px;
				}

				.masteriyo-user--title {
					display: inline-block;
					font-weight: 600;
					font-size: 16px;
					line-height: 26px;
					color: #383838;
					padding: 10px 0px 4px;
					border-bottom: 1.5px solid #222222;
					text-transform: uppercase;
					margin-bottom: 16px;
				}
			}

			&--info {
				padding: 28px 20px 32px;
				// background: rgba(244, 244, 244, 0.48);
				// background: #ffffff;
				text-align: center;
				margin-bottom: 24px;
				backdrop-filter: blur(16px) saturate(180%);
				-webkit-backdrop-filter: blur(16px) saturate(180%);
				background: rgba(255, 253, 253, 0.75);
				border-radius: 4px;
				border: 0.4px solid rgba(255, 255, 255, 0.125);

				.masteriyo-user {
					&--profile-pic {
						margin: 0 0 16px;

						img {
							width: 160px;
							height: 160px;
							object-fit: cover;
							border-radius: 50%;

							@media (max-width: 991px) {
								width: 120px;
								height: 120px;
							}
						}
					}

					&--name {
						font-weight: 500;
						font-size: 20px;
						line-height: 150%;
						color: #222222;
						margin-bottom: 4px;
					}

					&--username {
						font-weight: 400;
						font-size: 13px;
						line-height: 120%;
						color: #646464;
					}

					&--role {
						margin-top: 12px;

						span {
							padding: 6px 16px;
							background: rgba(242, 86, 86, 0.1);
							border-radius: 20px;
							font-style: italic;
							font-weight: 400;
							font-size: 12px;
							line-height: 120%;
							color: #f25656;
						}
					}
				}
			}

			&--about {
				margin-bottom: 24px;

				.masteriyo-user {
					&--desc {
						font-weight: 400;
						font-size: 15px;
						line-height: 25px;
						color: #4e4e4e;
						margin-bottom: 16px;
					}

					&--personal-info {
						display: flex;
						flex-direction: column;
						gap: 16px;

						&-lists {
							display: flex;
							align-items: flex-start;
							gap: 10px;

							.masteriyo-icon {
								display: inline-flex;
								padding-top: 3px;

								svg {
									width: 18px;
									height: 18px;
								}
							}

							.masteriyo-user--details {
								p {
									font-weight: 400;
									font-size: 14px;
									line-height: 24px;
									color: #646464;
								}
							}
						}
					}
				}
			}

			&--links {
				margin-bottom: 24px;

				.masteriyo-user {
					&--links {
						display: flex;
						flex-wrap: wrap;
						align-items: center;
						gap: 12px;

						&-list {
							display: flex;
							align-items: center;
							justify-content: center;
							padding: 8px;
							width: 40px;
							height: 40px;
							background: #ffffff;
							border: 1px solid #d3d3d3;
							border-radius: 50%;
							transition: all 0.3s ease-in-out;

							svg {
								width: 20px;
								height: 20px;

								path {
									fill: #646464;
									transition: all 0.3s ease-in-out;
								}
							}

							&:hover {
								background: #4b4b4b;

								svg {
									path {
										fill: #ffffff;
									}
								}
							}
						}
					}
				}
			}

			&--user-joined {
				h3 {
					text-transform: uppercase;
					font-weight: 600;
					font-size: 13px;
					line-height: 23px;
					color: #646464;
				}
			}
		}

		.masteriyo-col-right {
			width: 75%;
			padding-left: 10px;

			@media (max-width: 767px) {
				width: 100%;
				max-width: 100%;
				padding-left: 0;
			}

			&--tabbar {
				display: flex;
				align-items: center;
				border-bottom: 1px solid #e4e4e4;
				margin-bottom: 32px;

				&-list {
					display: flex;
					align-items: center;
					justify-content: center;
					gap: 6px;
					padding: 0px 16px 16px;
					border-bottom: 3px solid transparent;
					transition: all 0.3s ease-in-out;

					&:focus,
					&:focus-visible {
						outline: none;
					}

					.masteriyo-icon {
						display: inline-flex;

						svg {
							width: 24px;
							height: 24px;
						}
					}

					.masteriyo-tabbar-title {
						font-size: 15px;
						line-height: 25px;
						letter-spacing: 2%;
						color: #7a7a7a;
					}

					&.active,
					&:hover {
						border-bottom: 3px solid #4584ff;

						.masteriyo-icon {
							svg {
								path {
									fill: #4584ff;
								}
							}
						}

						.masteriyo-tabbar-title {
							color: #4584ff;
						}
					}
				}
			}

			.masteriyo-enrolled-courses--heading {
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-bottom: 26px;

				.title {
					font-weight: 600;
					font-size: 22px;
					line-height: 150%;
					color: #222222;
				}

				.masteriyo-secondary-btn {
					padding: 4px 16px 4px 22px;
					border: 1px solid #a0aec0;
					border-radius: 50px;
					font-weight: 500;
					font-size: 12px;
					line-height: 180%;
					color: #718096;
					display: flex;
					align-items: center;
					gap: 8px;
					text-transform: uppercase;
					transition: all 0.3s ease-in-out;

					svg {
						width: 10px;
						height: 10px;

						path {
							stroke: #718096;
							transition: all 0.3s ease-in-out;
						}
					}

					&:hover {
						background: #7d8ca2;
						border-color: #7d8ca2;
						color: #fff;

						svg {
							path {
								stroke: #fff;
							}
						}
					}
				}
			}

			&--cards {
				display: grid;
				grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
				gap: 16px;
				margin-bottom: 40px;

				&-single {
					display: flex;
					align-items: center;
					gap: 16px;
					padding: 20px;
					padding-right: 8px;
					background: #ffffff;
					border: 0.4px solid #e2e8f0;

					.masteriyo-icon {
						display: grid;
						place-items: center;
						width: 60px;
						height: 60px;
						background: #fff;
						border-radius: 11px;

						svg {
							width: 20px;
							height: 20px;
							fill: #7a7a7a;
						}
					}

					.masteriyo-card-content {
						.title {
							font-weight: 600;
							font-size: 15px;
							line-height: 150%;
							color: #222222;
							margin: auto;
						}

						.count {
							font-weight: 500;
							font-size: 14px;
							line-height: 150%;
							color: #7a7a7a;
						}
					}

					&.masteriyo-enrolled-courses {
						.masteriyo-icon {
							background: #c4f1f9;

							svg {
								path {
									fill: #00b5d8;
								}
							}
						}

						.masteriyo-card-content {
							.count {
								color: #0987a0;
							}
						}
					}

					&.masteriyo-in-progress-courses {
						.masteriyo-icon {
							background: #ccd4ff;

							svg {
								path {
									fill: #6f80da;
								}
							}
						}

						.masteriyo-card-content {
							.count {
								color: #6f80da;
							}
						}
					}

					&.masteriyo-completed-courses {
						.masteriyo-icon {
							background: #c6f6d5;

							svg {
								path {
									fill: #38a169;
								}
							}
						}

						.masteriyo-card-content {
							.count {
								color: #276749;
							}
						}
					}

					&.masteriyo-offered-courses {
						.masteriyo-icon {
							background: #fcf3e5;

							svg {
								path {
									fill: #e38c00;
								}
							}
						}

						.masteriyo-card-content {
							.count {
								color: #e38c00;
							}
						}
					}

					&.masteriyo-enrolled-students {
						.masteriyo-icon {
							background: #ffeaf0;

							svg {
								path {
									fill: #fd739c;
								}
							}
						}

						.masteriyo-card-content {
							.count {
								color: #fd739c;
							}
						}
					}
				}
			}

			&--courses {
				margin-bottom: 40px;

				.title {
					font-weight: 600;
					font-size: 22px;
					line-height: 150%;
					color: #222222;
				}
			}

			.masteriyo-enrolled-courses {
				&--content {
					&-listcard,
					&-gridcard {
						padding: 16px 24px 16px 16px;
						background: #ffffff;
						border: 0.4px solid #e2e8f0;
						border-radius: 4px;
						display: flex;
						align-items: center;
						gap: 16px;
						margin-bottom: 20px;
						overflow: hidden;

						&:last-child {
							margin-bottom: 0;
						}

						@media (max-width: 500px) {
							flex-direction: column;
							padding-right: 16px;
						}

						.masteriyo-course-thumbnail {
							min-width: 150px;
							height: 150px;

							@media (max-width: 500px) {
								width: 100%;
							}

							img {
								width: 150px;
								height: 150px;
								object-fit: cover;

								@media (max-width: 500px) {
									width: 100%;
									height: 200px;
								}

								@media (max-width: 400px) {
									height: 170px;
								}
							}
						}

						.masteriyo-enrolled-course-desc {
							width: 100%;
							max-width: 100%;

							.masteriyo-course-category {
								padding: 4px 8px;
								border: 1px solid rgba(0, 0, 0, 0.1);
								border-radius: 20px;
								font-weight: 500;
								font-size: 10px;
								line-height: 120%;
								color: #7963e0;
								display: inline-block;
								margin-bottom: 8px;
							}

							.masteriyo-course-title {
								font-weight: 600;
								font-size: 18px;
								line-height: 150%;
								color: #222222;
								margin-bottom: 12px;
							}

							.masteriyo-course-duration-percent-wrapper {
								display: flex;
								flex-wrap: wrap;
								align-items: center;
								justify-content: space-between;
								margin-bottom: 10px;

								.masteriyo-course-duration {
									display: flex;
									align-items: center;
									gap: 6px;

									svg {
										width: 20px;
										height: 20px;

										path {
											fill: #7c7d8f;
										}
									}

									span {
										font-weight: 500;
										font-size: 13px;
										line-height: 23px;
										color: #7c7d8f;
									}
								}

								.masteriyo-course-percent {
									font-weight: 500;
									font-size: 13px;
									line-height: 23px;
									text-align: right;
									color: #424360;
								}
							}

							.masteriyo-course-progress {
								justify-content: flex-start;
								border-radius: 100px;
								align-items: center;
								position: relative;
								display: flex;
								height: 6px;
								max-width: 100%;
								background: #c8dbff;

								.masteriyo-course-progress-value {
									animation: load 3s normal forwards;
									border-radius: 100px;
									background: #4584ff;
									height: 6px;
									width: 0;
								}
							}

							.masteriyo-course-started-date {
								margin-top: 10px;

								span {
									font-weight: 400;
									font-size: 12px;
									line-height: 14px;
									color: #7c7d8f;
								}
							}
						}
					}

					&-gridcard {
						flex-direction: column;
						margin-bottom: 0;
						padding: 0;
						border-color: #ebecf2;
						height: max-content;
					}
				}
			}

			.masteriyo-col-right--tab-content {
				&.hidden {
					display: none;
				}
			}

			#masteriyo-courses-offered-main-content,
			#masteriyo-enrolled-courses-main-content {
				.masteriyo-courses--pagination {
					&-list {
						display: flex;
						list-style-type: none;
						align-items: center;
						gap: 8px;
						justify-content: flex-end;
						margin-top: 60px;
						margin-left: 0;
						margin-bottom: 0;

						.masteriyo-courses--pagination-item {
							color: #222;

							a {
								padding: 8px 14px;
								background: #edf2f7;
								border-radius: 2px;
								color: #4e4e4e;
								transition: all 0.3s ease-in-out;

								&:hover {
									background: #4584ff;
									color: #fff;
								}
							}

							&.pagination-previous,
							&.pagination-next {
								display: flex;
								align-items: center;
								justify-content: center;

								a {
									font-size: 0;
									position: relative;
									padding: 0;
									background: transparent;
									display: contents;

									&::before {
										content: '';
										display: block;
										background-repeat: no-repeat;
										background-position: center;
										background-size: 100%;
										width: 18px;
										height: 18px;
									}
								}
							}

							&.pagination-previous {
								a {
									&::before {
										background-image: url('../img/svgs/pagination-left-arrow.svg');
									}
								}
							}

							&.pagination-next {
								a {
									&::before {
										background-image: url('../img/svgs/pagination-right-arrow.svg');
									}
								}
							}
						}

						.masteriyo-courses--pagination-item--disabled {
							opacity: 0.5;
						}

						.masteriyo-courses--pagination-item--active {
							a {
								background: #4584ff;
								color: #fff;
							}
						}
					}
				}
			}

			#masteriyo-enrolled-courses-main-content {
				.masteriyo-enrolled-courses--content {
					display: grid;
					grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
					gap: 20px;

					&-gridcard {
						.masteriyo-course-thumbnail {
							width: 100%;
							height: 170px;
							position: relative;

							img {
								width: 100%;
								height: 170px;
							}

							.masteriyo-course-level {
								padding: 8px 12px;
								position: absolute;
								left: 16px;
								top: 16px;
								border-radius: 20px;
								font-weight: 500;
								font-size: 12px;
								line-height: 120%;
								color: #ffffff;
								letter-spacing: 0.2px;

								&.masteriyo-beginner {
									background: #69db7c;
									border: 1px solid #69db7c;
								}

								&.masteriyo-intermediate {
									background: #ffa94d;
									border: 1px solid #ffa94d;
								}

								&.masteriyo-expert {
									background: #fd739c;
									border: 1px solid #fd739c;
								}
							}
						}

						.masteriyo-enrolled-course-desc {
							padding: 0 16px 16px;

							.masteriyo-course-author-rating-wrapper {
								display: flex;
								flex-wrap: wrap;
								align-items: center;
								justify-content: space-between;
								margin-bottom: 12px;

								.masteriyo-course-author {
									display: flex;
									align-items: center;
									gap: 8px;

									figure {
										width: 24px;
										height: 24px;

										img {
											width: 24px;
											height: 24px;
											object-fit: cover;
											border-radius: 50%;
										}
									}

									&-name {
										font-weight: 500;
										font-size: 13px;
										line-height: 24px;
										color: #494949;
									}
								}

								.masteriyo-course-rating {
									display: flex;
									align-items: center;
									gap: 6px;

									svg {
										width: 16px;
										height: 16px;

										path {
											fill: #fcb33b;
											stroke: #fcb33b;
										}
									}

									&-count {
										font-weight: 500;
										font-size: 13px;
										line-height: 24px;
										color: #424360;
										transition: all 0.3s ease-in-out;

										&:hover {
											color: #4584ff;
										}
									}
								}
							}
						}
					}
				}

				.masteriyo-enrolled-courses--pagination {
					&-list {
						display: flex;
						list-style-type: none;
						align-items: center;
						gap: 8px;
						justify-content: flex-end;
						margin-top: 60px;

						.masteriyo-enrolled-courses--pagination-item {
							color: #222;

							a {
								padding: 8px 14px;
								background: #edf2f7;
								border-radius: 2px;
								color: #4e4e4e;
								transition: all 0.3s ease-in-out;

								&:hover {
									background: #4584ff;
									color: #fff;
								}
							}

							&.pagination-previous,
							&.pagination-next {
								display: flex;
								align-items: center;
								justify-content: center;

								a {
									font-size: 0;
									position: relative;
									padding: 0;
									background: transparent;

									&::before {
										content: '';
										display: block;
										background-repeat: no-repeat;
										background-position: center;
										background-size: 100%;
										width: 18px;
										height: 18px;
									}
								}
							}

							&.pagination-previous {
								a {
									&::before {
										background-image: url('../images/pagination-left-arrow.svg');
									}
								}
							}

							&.pagination-next {
								a {
									&::before {
										background-image: url('../images/pagination-right-arrow.svg');
									}
								}
							}
						}

						.masteriyo-enrolled-courses--pagination-item--disabled {
							opacity: 0.5;
						}

						.masteriyo-enrolled-courses--pagination-item--active {
							a {
								background: #4584ff;
								color: #fff;
							}
						}
					}
				}
			}

			#masteriyo-certificates-main-content {
				.masteriyo-certificates--content {
					display: grid;
					grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
					gap: 20px;

					&-gridcard {
						background: #ffffff;
						border: 1px solid #ebebeb;
						border-radius: $border_radius_4;
						overflow: hidden;
						height: max-content;

						.masteriyo-certificates-thumbnail {
							width: 100%;
							height: 170px;

							img {
								width: 100%;
								height: 170px;
								object-fit: cover;
							}
						}

						.masteriyo-certificates-desc {
							padding: 16px;

							.masteriyo-certificate-title {
								font-size: 14px;
								font-weight: 500;
								line-height: 24px;
								color: #383838;
								margin-bottom: 4px;
							}

							.masteriyo-certificate-date {
								display: flex;
								align-items: center;
								gap: 4px;
								margin-bottom: 12px;

								p {
									font-weight: 400;
									font-size: 13px;
									line-height: 23px;
									color: #646464;
								}

								span {
									font-weight: 400;
									font-size: 13px;
									line-height: 23px;
									color: #646464;
									margin-top: auto;
								}
							}

							.masteriyo-certificate-view-button {
								padding: 12px 0px 0px;
								border-top: 0.4px solid #ebebeb;
								text-align: center;

								.masteriyo-view-btn {
									font-weight: 400;
									font-size: 13px;
									line-height: 23px;
									color: #4584ff;
								}
							}
						}
					}
				}
			}
		}
	}
}

@keyframes load {
	0% {
		width: 0;
	}

	100% {
		width: var(--value);
	}
}
