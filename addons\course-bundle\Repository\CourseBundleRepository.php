<?php

namespace Masteriyo\Addons\CourseBundle\Repository;

defined( 'ABSPATH' ) || exit;


use Masteriyo\Database\Model;
use Masteriyo\Enums\PostStatus;
use Masteriyo\PostType\PostType;
use Masteriyo\Repository\CourseRepository;

class CourseBundleRepository extends CourseRepository {

	/**
	 * Create a course bundle in the database.
	 *
	 * @since 2.12.0
	 *
	 * @param \Masteriyo\Addons\CourseBundle\Models\CourseBundle $course_bundle Course bundle object.
	 */
	public function create( Model &$course_bundle ) {
		if ( ! $course_bundle->get_date_created( 'edit' ) ) {
			$course_bundle->set_date_created( time() );
		}

		if ( empty( $course_bundle->get_author_id() ) ) {
			$course_bundle->set_author_id( get_current_user_id() );
		}

		$id = wp_insert_post(
			/**
			 * Filters new course bundle data before creating.
			 *
			 * @since 2.12.0
			 *
			 * @param array $data New course bundle data.
			 * @param \Masteriyo\Addons\CourseBundle\Models\CourseBundle $course Course bundle object.
			 */
			apply_filters(
				'masteriyo_new_course_bundle_data',
				array(
					'post_type'      => PostType::COURSE_BUNDLE,
					'post_status'    => $course_bundle->get_status() ? $course_bundle->get_status() : PostStatus::PUBLISH,
					'post_author'    => $course_bundle->get_author_id( 'edit' ),
					'post_title'     => $course_bundle->get_name() ? $course_bundle->get_name() : __( 'Course', 'learning-management-system' ),
					'post_content'   => $course_bundle->get_description(),
					'post_excerpt'   => $course_bundle->get_short_description(),
					'post_parent'    => $course_bundle->get_parent_id(),
					'comment_status' => $course_bundle->get_reviews_allowed() ? 'open' : 'closed',
					'ping_status'    => 'closed',
					'menu_order'     => $course_bundle->get_menu_order(),
					'post_password'  => $course_bundle->get_post_password( 'edit' ),
					'post_name'      => $course_bundle->get_slug( 'edit' ),
					'post_date'      => gmdate( 'Y-m-d H:i:s', $course_bundle->get_date_created( 'edit' )->getOffsetTimestamp() ),
					'post_date_gmt'  => gmdate( 'Y-m-d H:i:s', $course_bundle->get_date_created( 'edit' )->getTimestamp() ),
				),
				$course_bundle
			)
		);

		if ( $id && ! is_wp_error( $id ) ) {
			$course_bundle->set_id( $id );

			$this->update_post_meta( $course_bundle, true );
			$this->handle_updated_props( $course_bundle );
			$this->update_visibility( $course_bundle, true );
			// TODO Invalidate caches.

			$course_bundle->save_meta_data();
			$course_bundle->apply_changes();

			/**
			 * Fires after creating a course.
			 *
			 * @since 2.12.0
			 *
			 * @param integer $id The course bundle ID.
			 * @param \Masteriyo\Addons\CourseBundle\Models\CourseBundle $object The course bundle object.
			 */
			do_action( 'masteriyo_new_course_bundle', $id, $course_bundle );
		}
	}

	/**
	 * Read a course bundle.
	 *
	 * @since 2.12.0
	 *
	 * @throws Exception If invalid course.
	 *
	 * @param \Masteriyo\Addons\CourseBundle\Models\CourseBundle $course_bundle Course object.
	 */
	public function read( Model &$course_bundle ) {
		$course_bundle_post = get_post( $course_bundle->get_id() );

		if ( ! $course_bundle->get_id() || ! $course_bundle_post || PostType::COURSE_BUNDLE !== $course_bundle_post->post_type ) {
			throw new \Exception( __( 'Invalid course.', 'learning-management-system' ) );
		}

		$course_bundle->set_props(
			array(
				'name'              => $course_bundle_post->post_title,
				'slug'              => $course_bundle_post->post_name,
				'date_created'      => $this->string_to_timestamp( $course_bundle_post->post_date_gmt ) ? $this->string_to_timestamp( $course_bundle_post->post_date_gmt ) : $this->string_to_timestamp( $course_bundle_post->post_date ),
				'date_modified'     => $this->string_to_timestamp( $course_bundle_post->post_modified_gmt ) ? $this->string_to_timestamp( $course_bundle_post->post_modified_gmt ) : $this->string_to_timestamp( $course_bundle_post->post_modified ),
				'status'            => $course_bundle_post->post_status,
				'description'       => $course_bundle_post->post_content,
				'short_description' => $course_bundle_post->post_excerpt,
				'parent_id'         => $course_bundle_post->post_parent,
				'author_id'         => $course_bundle_post->post_author,
				'menu_order'        => $course_bundle_post->menu_order,
				'post_password'     => $course_bundle_post->post_password,
				'reviews_allowed'   => 'open' === $course_bundle_post->comment_status,
			)
		);

		$this->read_course_bundle_data( $course_bundle );
		$this->read_visibility( $course_bundle );
		$this->read_extra_data( $course_bundle );
		$course_bundle->set_object_read( true );

		/**
		 * Fires after reading a course bundle from database.
		 *
		 * @since 2.12.0
		 *
		 * @param integer $id The course bundle ID.
		 * @param \Masteriyo\Addons\CourseBundle\Models\CourseBundle $object The new course bundle object.
		 */
		do_action( 'masteriyo_course_bundle_read', $course_bundle->get_id(), $course_bundle );
	}

	/**
	 * Update a course bundle in the database.
	 *
	 * @since 2.12.0
	 *
	 * @param \Masteriyo\Addons\CourseBundle\Models\CourseBundle $course_bundle Course bundle object.
	 *
	 * @return void
	 */
	public function update( Model &$course_bundle ) {
		$changes = $course_bundle->get_changes();

		$post_data_keys = array(
			'description',
			'short_description',
			'name',
			'parent_id',
			'reviews_allowed',
			'status',
			'menu_order',
			'date_created',
			'date_modified',
			'slug',
			'author_id',
		);

		// Only update the post when the post data changes.
		if ( array_intersect( $post_data_keys, array_keys( $changes ) ) ) {
			$post_data = array(
				'post_content'   => $course_bundle->get_description( 'edit' ),
				'post_excerpt'   => $course_bundle->get_short_description( 'edit' ),
				'post_title'     => $course_bundle->get_name( 'edit' ),
				'post_parent'    => $course_bundle->get_parent_id( 'edit' ),
				'comment_status' => $course_bundle->get_reviews_allowed( 'edit' ) ? 'open' : 'closed',
				'post_status'    => $course_bundle->get_status( 'edit' ) ? $course_bundle->get_status( 'edit' ) : PostStatus::PUBLISH,
				'menu_order'     => $course_bundle->get_menu_order( 'edit' ),
				'post_password'  => $course_bundle->get_post_password( 'edit' ),
				'post_name'      => $course_bundle->get_slug( 'edit' ),
				'post_author'    => $course_bundle->get_author_id( 'edit' ),
				'post_type'      => PostType::COURSE_BUNDLE,
			);

			/**
			 * When updating this object, to prevent infinite loops, use $wpdb
			 * to update data, since wp_update_post spawns more calls to the
			 * save_post action.
			 *
			 * This ensures hooks are fired by either WP itself (admin screen save),
			 * or an update purely from CRUD.
			 */
			if ( doing_action( 'save_post' ) ) {
				// TODO Abstract the $wpdb WordPress class.
				$GLOBALS['wpdb']->update( $GLOBALS['wpdb']->posts, $post_data, array( 'ID' => $course_bundle->get_id() ) );
				clean_post_cache( $course_bundle->get_id() );
			} else {
				wp_update_post( array_merge( array( 'ID' => $course_bundle->get_id() ), $post_data ) );
			}

			$course_bundle->read_meta_data( true ); // Refresh internal meta data, in case things were hooked into `save_post` or another WP hook.
		} else { // Only update post modified time to record this save event.
			$GLOBALS['wpdb']->update(
				$GLOBALS['wpdb']->posts,
				array(
					'post_modified'     => current_time( 'mysql' ),
					'post_modified_gmt' => current_time( 'mysql', true ),
				),
				array(
					'ID' => $course_bundle->get_id(),
				)
			);
			clean_post_cache( $course_bundle->get_id() );
		}

		$this->update_post_meta( $course_bundle );
		$this->handle_updated_props( $course_bundle );
		$this->update_visibility( $course_bundle );

		$course_bundle->apply_changes();

		/**
		 * Fires after updating a course bundle in database.
		 *
		 * @since 2.12.0
		 *
		 * @param integer $id The course bundle ID.
		 * @param \Masteriyo\Addons\CourseBundle\Models\CourseBundle $object The new course bundle object.
		 */
		do_action( 'masteriyo_update_course_bundle', $course_bundle->get_id(), $course_bundle );
	}

	/**
	 * Delete a course from the database.
	 *
	 * @since 2.12.0
	 *
	 * @param \Masteriyo\Addons\CourseBundle\Models\CourseBundle $course_bundle Course object.
	 * @param array $args   Array of args to pass.alert-danger
	 */
	public function delete( Model &$course_bundle, $args = array() ) {
		$id          = $course_bundle->get_id();
		$object_type = $course_bundle->get_object_type();
		$args        = array_merge(
			array(
				'force_delete' => false,
			),
			$args
		);

		if ( ! $id ) {
			return;
		}

		if ( $args['force_delete'] ) {
			/**
			 * Fires before deleting a course from database.
			 *
			 * @since 2.12.0
			 *
			 * @param integer $id The course bundle ID.
			 * @param \Masteriyo\Addons\CourseBundle\Models\CourseBundle $course The deleted course object.
			 */
			do_action( 'masteriyo_before_delete_' . $object_type, $id, $course_bundle );

			wp_delete_post( $id, true );
			$course_bundle->set_id( 0 );

			/**
			 * Fires after deleting a course from database.
			 *
			 * @since 2.12.0
			 *
			 * @param integer $id The course bundle ID.
			 * @param \Masteriyo\Addons\CourseBundle\Models\CourseBundle $course The deleted course object.
			 */
			do_action( 'masteriyo_after_delete_' . $object_type, $id, $course_bundle );
		} else {
			/**
			 * Fires before moving a course to trash in database.
			 *
			 * @since 2.12.0
			 *
			 * @param integer $id The course bundle ID.
			 * @param \Masteriyo\Addons\CourseBundle\Models\CourseBundle $course The trashed course object.
			 */
			do_action( 'masteriyo_before_trash_' . $object_type, $id, $course_bundle );

			wp_trash_post( $id );
			$course_bundle->set_status( 'trash' );

			/**
			 * Fires after moving a course to trash in database.
			 *
			 * @since 1.5.2
			 *
			 * @param integer $id The course bundle ID.
			 * @param \Masteriyo\Addons\CourseBundle\Models\CourseBundle $course The trashed course object.
			 */
			do_action( 'masteriyo_after_trash_' . $object_type, $id, $course_bundle );
		}
	}

	/**
	 * Read extra data associated with the course bundle.
	 *
	 * @since 2.12.0
	 *
	 * @param \Masteriyo\Addons\CourseBundle\Models\CourseBundle $course course object.
	 */
	protected function read_extra_data( &$course_bundle ) {
		$meta_values = $this->read_meta( $course_bundle );

		foreach ( $course_bundle->get_extra_data_keys() as $key ) {
			$function = 'set_' . $key;
			if ( is_callable( array( $course_bundle, $function ) )
				&& isset( $meta_values[ '_' . $key ] ) ) {
				$course_bundle->{$function}( $meta_values[ '_' . $key ] );
			}
		}
	}

	/**
	 * Read course bundle data. Can be overridden by child classes to load other props.
	 *
	 * @since 2.12.0
	 *
	 * @param \Masteriyo\Addons\CourseBundle\Models\CourseBundle $course_bundle Course bundle object.
	 */
	protected function read_course_bundle_data( &$course_bundle ) {
		$id          = $course_bundle->get_id();
		$meta_values = $this->read_meta( $course_bundle );

		$set_props = array();

		$meta_values = array_reduce(
			$meta_values,
			function ( $result, $meta_value ) {
				$result[ $meta_value->key ][] = $meta_value->value;
				return $result;
			},
			array()
		);

		foreach ( $this->internal_meta_keys as $prop => $meta_key ) {
			$meta_value         = isset( $meta_values[ $meta_key ][0] ) ? $meta_values[ $meta_key ][0] : null;
			$set_props[ $prop ] = maybe_unserialize( $meta_value ); // get_post_meta only unserializes single values.
		}

		$course_bundle->set_props( $set_props );
	}

	/**
	 * Fetch courses.
	 *
	 * @since 2.12.0
	 *
	 * @param array $query_vars Query vars.
	 * @return Course[]
	 */
	public function query( $query_vars ) {
		$args = $this->get_wp_query_args( $query_vars );

		if ( ! empty( $args['errors'] ) ) {
			$query = (object) array(
				'posts'         => array(),
				'found_posts'   => 0,
				'max_num_pages' => 0,
			);
		} else {
			$query = new \WP_Query( $args );
		}

		if ( isset( $query_vars['return'] ) && 'objects' === $query_vars['return'] && ! empty( $query->posts ) ) {
			// Prime caches before grabbing objects.
			update_post_caches( $query->posts, array( PostType::COURSE_BUNDLE ) );
		}

		$course_bundles = ( isset( $query_vars['return'] ) && 'ids' === $query_vars['return'] ) ? $query->posts : array_filter( array_map( 'masteriyo_get_course_bundle', $query->posts ) );

		if ( isset( $query_vars['paginate'] ) && $query_vars['paginate'] ) {
			return (object) array(
				'course_bundles' => $course_bundles,
				'total'          => $query->found_posts,
				'max_num_pages'  => $query->max_num_pages,
			);
		}

		return $course_bundles;
	}

	/**
	 * Get valid WP_Query args from a CourseBundleQuery's query variables.
	 *
	 * @since 2.12.0
	 * @param array $query_vars Query vars from a CourseQuery.
	 * @return array
	 */
	protected function get_wp_query_args( $query_vars ) {
		$wp_query_args = parent::get_wp_query_args( $query_vars );

		if ( ! isset( $wp_query_args['date_query'] ) ) {
			$wp_query_args['date_query'] = array();
		}
		if ( ! isset( $wp_query_args['meta_query'] ) ) {
			$wp_query_args['meta_query'] = array(); // phpcs:ignore WordPress.DB.SlowDBQuery.slow_db_query_meta_query
		}

		// Handle date queries.
		$date_queries = array(
			'date_created'      => 'post_date',
			'date_modified'     => 'post_modified',
			'date_on_sale_from' => '_sale_price_dates_from',
			'date_on_sale_to'   => '_sale_price_dates_to',
		);
		foreach ( $date_queries as $query_var_key => $db_key ) {
			if ( isset( $query_vars[ $query_var_key ] ) && '' !== $query_vars[ $query_var_key ] ) {

				// Remove any existing meta queries for the same keys to prevent conflicts.
				$existing_queries = wp_list_pluck( $wp_query_args['meta_query'], 'key', true );
				foreach ( $existing_queries as $query_index => $query_contents ) {
					unset( $wp_query_args['meta_query'][ $query_index ] );
				}

				$wp_query_args = $this->parse_date_for_wp_query( $query_vars[ $query_var_key ], $db_key, $wp_query_args );
			}
		}

		if ( isset( $wp_query_vars['meta_query'] ) ) {
			$wp_query_args['meta_query'][] = array( 'relation' => 'AND' );
		}

		if ( ! empty( $query_vars['course'] ) ) {
			$wp_query_args['meta_query'][] = array(
				'key'     => '_courses',
				'value'   => (string) $query_vars['course'],
				'compare' => 'LIKE',
			);
		}

		// Handle paginate.
		if ( ! isset( $query_vars['paginate'] ) || ! $query_vars['paginate'] ) {
			$wp_query_args['no_found_rows'] = true;
		}

		// Handle orderby.
		if ( isset( $query_vars['orderby'] ) && 'include' === $query_vars['orderby'] ) {
			$wp_query_args['orderby'] = 'post__in';
		}

		/**
		 * Filters WP Query args for course post type query.
		 *
		 * @since 2.12.0
		 *
		 * @param array $wp_query_args WP Query args.
		 * @param array $query_vars Query vars.
		 * @param \Masteriyo\Repository\CourseRepository $repository Course repository object.
		 */
		return apply_filters( 'masteriyo_course_bundle_data_store_cpt_get_course_bundles_query', $wp_query_args, $query_vars, $this );
	}
}
