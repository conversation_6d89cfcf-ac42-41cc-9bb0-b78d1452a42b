<?php
/**
 * New Assignment Reply webhook event listener class.
 *
 * @since 2.8.3
 */

namespace Masteriyo\Addons\Assignment\Listener;

use Masteriyo\Abstracts\Listener;
use Masteriyo\Addons\Assignment\Resources\AssignmentReplyResource;
use Masteriyo\Resources\WebhookResource;

defined( 'ABSPATH' ) || exit;

/**
 * New student enrollment webhook event listener class.
 *
 * @since 2.8.3
 */
class NewAssignmentReplyListener extends Listener {

	/**
	 * Event name the listener is listening to.
	 *
	 * @since 2.8.3
	 */
	protected $event_name = 'assignment_reply.created';

	/**
	 * Get event label.
	 *
	 * @since 2.8.3
	 *
	 * @return string
	 */
	public function get_label() {
		return __( 'New Assignment Reply', 'learning-management-system' );
	}

	/**
	 * Setup the webhook event.
	 *
	 * @since 2.8.3
	 *
	 * @param callable $deliver_callback
	 * @param \Masteriyo\Models\Webhook $webhook
	 */
	public function setup( $deliver_callback, $webhook ) {
		add_action(
			'masteriyo_new_assignment_reply',
			function( $id, $assignment_reply ) use ( $deliver_callback, $webhook ) {
				if ( ! $this->can_deliver( $webhook, $assignment_reply->get_id() ) ) {
					return;
				}

				call_user_func_array(
					$deliver_callback,
					array(
						WebhookResource::to_array( $webhook ),
						$this->get_payload( $assignment_reply, $webhook ),
					)
				);
			},
			10,
			2
		);
	}

	/**
	 * Get payload data for the currently triggered webhook.
	 *
	 * @since 2.8.3
	 *
	 * @param \Masteriyo\Models\AssignmentReply $assignment_reply
	 * @param \Masteriyo\Models\Webhook $webhook
	 *
	 * @return array
	 */
	protected function get_payload( $assignment_reply, $webhook ) {
		$data = AssignmentReplyResource::to_array( $assignment_reply );

		/**
		 * Filters the payload data for the currently triggered webhook.
		 *
		 * @since 2.8.3
		 *
		 * @param array $data The payload data.
		 * @param \Masteriyo\Models\Webhook $webhook The webhook instance.
		 * @param \Masteriyo\Addons\Assignment\Listener\NewAssignmentReplyListener $this The webhook event listener instance.
		 */
		return apply_filters( "masteriyo_webhook_payload_for_{$this->event_name}", $data, $webhook );
	}
}
