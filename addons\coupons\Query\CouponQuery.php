<?php
/**
 * Class for parameter-based Coupon querying
 *
 * @package  Masteriyo\Addons\Coupons
 * @version 2.5.12
 * @since   2.5.12
 */

namespace Masteriyo\Addons\Coupons\Query;

use Masteriyo\PostType\PostType;
use Masteriyo\Abstracts\ObjectQuery;
use Masteriyo\Addons\Coupons\Enums\CouponStatus;

defined( 'ABSPATH' ) || exit;

/**
 * Coupon query class.
 */
class CouponQuery extends ObjectQuery {

	/**
	 * Valid query vars for coupons.
	 *
	 * @since 2.5.12
	 *
	 * @return array
	 */
	protected function get_default_query_vars() {
		return array_merge(
			parent::get_default_query_vars(),
			array(
				'type'   => PostType::COUPON,
				'status' => array( CouponStatus::ACTIVE, CouponStatus::DRAFT ),
			)
		);
	}

	/**
	 * Get coupons matching the current query vars.
	 *
	 * @since 2.5.12
	 *
	 * @return Masteriyo\Addons\Coupons\Models\Coupon[] Coupon objects.
	 */
	public function get_coupons() {
		/**
		 * Filters coupon object query args.
		 *
		 * @since 2.5.12
		 *
		 * @param array $query_args The object query args.
		 */
		$args    = apply_filters( 'masteriyo_coupon_object_query_args', $this->get_query_vars() );
		$results = masteriyo( 'coupon.store' )->query( $args );

		/**
		 * Filters coupon object query results.
		 *
		 * @since 2.5.12
		 *
		 * @param Masteriyo\Addons\Coupons\Models\Coupon[] $results The query results.
		 * @param array $query_args The object query args.
		 */
		return apply_filters( 'masteriyo_coupon_object_query', $results, $args );
	}
}
