<?php

defined( 'ABSPATH' ) || exit;

/**
 * Addon Name: Multiple Instructors
 * Addon URI: https://masteriyo.com/wordpress-lms/
 * Description: Allow more than one instructors to take control of the course content.
 * Author: Masteriyo
 * Author URI: https://masteriyo.com
 * Addon Type: feature
 * Plan: Starter,Pro,Elite,Growth,Scale
 */

use Masteriyo\Pro\Addons;

define( 'MASTERIYO_MULTIPLE_INSTRUCTORS_ADDON_FILE', __FILE__ );
define( 'MASTERIYO_MULTIPLE_INSTRUCTORS_ADDON_BASENAME', plugin_basename( __FILE__ ) );
define( 'MASTERIYO_MULTIPLE_INSTRUCTORS_ADDON_DIR', __DIR__ );
define( 'MASTERIYO_MULTIPLE_INSTRUCTORS_ASSETS', __DIR__ . '/assets' );
define( 'MASTERIYO_MULTIPLE_INSTRUCTORS_TEMPLATES', __DIR__ . '/templates' );
define( 'MASTERIYO_MULTIPLE_INSTRUCTORS_ADDON_SLUG', 'multiple-instructors' );

// Bail early if the addon is not active.
if ( ! ( new Addons() )->is_active( MASTERIYO_MULTIPLE_INSTRUCTORS_ADDON_SLUG ) ) {
	return;
}

/**
 * Include service providers for Multiple Instructors.
 */
add_filter(
	'masteriyo_service_providers',
	function( $providers ) {
		return array_merge( $providers, require_once __DIR__ . '/config/providers.php' );
	}
);

/**
 * Initialize Masteriyo Multiple Instructors.
 */
add_action(
	'masteriyo_before_init',
	function() {
		masteriyo( 'addons.multiple-instructors' )->init();
	}
);
