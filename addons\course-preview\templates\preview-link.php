<?php

/**
 * The Template for displaying preview link for course lesson.
 *
 * @since 2.6.7
 */

use Masteriyo\Addons\CoursePreview\Svg;

defined( 'ABSPATH' ) || exit; // Exit if accessed directly.

/**
 * Fires before rendering preview link.
 *
 * @since 2.6.7
 */
do_action( 'masteriyo_before_course_preview_link' );

?>
<div class="masteriyo-course-preview-wrapper">
	<?php if ( masteriyo_course_preview_is_lesson_preview_enabled( $object ) ) : ?>
		<a href="<?php echo esc_attr( $learn_url ); ?>" class="masteriyo-lesson-preview-link" target="_blank" style="margin-left: 100px; color: #ff0000; font-weight: bold; cursor: pointer;">
			<?php esc_html_e( 'Preview', 'learning-management-system' ); ?>
		</a>
	<?php endif; ?>

	<?php if ( masteriyo_course_preview_is_lesson_video_preview_enabled( $object ) ) : ?>
		<span class="masteriyo-course-preview-link" style="margin-left: 100px; color: #ff0000; font-weight: bold; cursor: pointer;" data-preview-id="<?php echo esc_attr( $object->get_id() ); ?>" data-lesson-title="<?php echo esc_attr( $object->get_name() ); ?>" data-categories="<?php echo esc_attr( $categories_string ); ?>">
			<?php Svg::get( 'eye', true ); ?>
		</span>
	<?php endif; ?>
</div>


<?php

/**
 * Fires after rendering preview link.
 *
 * @since 2.6.7
 */
do_action( 'masteriyo_after_course_preview_link' );
