<?php

/**
 * Gradebook service provider.
 *
 * @since 2.5.20
 * @package \Masteriyo\Addons\Gradebook
 */

namespace Masteriyo\Addons\Gradebook\Providers;

defined( 'ABSPATH' ) || exit;

use Masteriyo\Addons\Gradebook\GradebookAddon;
use Masteriyo\Addons\Gradebook\PostType\Gradebook;
use League\Container\ServiceProvider\AbstractServiceProvider;
use Masteriyo\Addons\Gradebook\GradeCalculator;
use Masteriyo\Addons\Gradebook\Query\GradeQuery;
use Masteriyo\Addons\Gradebook\Repository\GradebookRepository;

/**
 * Gradebook service provider.
 *
 * @since 2.5.20
 */
class GradebookServiceProvider extends AbstractServiceProvider {
	/**
	 * The provided array is a way to let the container
	 * know that a service is provided by this service
	 * provider. Every service that is registered via
	 * this service provider must have an alias added
	 * to this array or it will be ignored
	 *
	 * @since 2.5.20
	 *
	 * @var array
	 */
	protected $provides = array(
		'addons.gradebook',
		GradebookAddon::class,
		'query.grade',
	);

	/**
	 * This is where the magic happens, within the method you can
	 * access the container and register or retrieve anything
	 * that you need to, but remember, every alias registered
	 * within this method must be declared in the `$provides` array.
	 *
	 * @since 2.5.20
	 */
	public function register() {
		$this->getContainer()->add( 'query.grade', GradeQuery::class );

		$this->getContainer()->add( 'addons.gradebook', GradebookAddon::class, true );
	}
}
