!function(e,t){"function"==typeof define&&define.amd?define([],t):"object"==typeof module&&module.exports?module.exports=t():e.pipwerks=t()}(this,function(){var l={UTILS:{},debug:{isActive:!0},SCORM:{version:null,handleCompletionStatus:!0,handleExitMode:!0,API:{handle:null,isFound:!1},connection:{isActive:!1},data:{completionStatus:null,exitStatus:null},debug:{}}};return l.SCORM.isAvailable=function(){return!0},l.SCORM.API.find=function(e){for(var t=null,n=0,i="SCORM.API.find",a=l.UTILS.trace,o=l.SCORM;!e.API&&!e.API_1484_11&&e.parent&&e.parent!=e&&n<=500;)n++,e=e.parent;if(o.version)switch(o.version){case"2004":e.API_1484_11?t=e.API_1484_11:a(i+": SCORM version 2004 was specified by user, but API_1484_11 cannot be found.");break;case"1.2":e.API?t=e.API:a(i+": SCORM version 1.2 was specified by user, but API cannot be found.")}else e.API_1484_11?(o.version="2004",t=e.API_1484_11):e.API&&(o.version="1.2",t=e.API);return t?(a(i+": API found. Version: "+o.version),a("API: "+t)):a(i+": Error finding API. \nFind attempts: "+n+". \nFind attempt limit: 500"),t},l.SCORM.API.get=function(){var e=null,t=window,n=l.SCORM,i=n.API.find,a=l.UTILS.trace;return(e=!(e=!(e=!(e=i(t))&&t.parent&&t.parent!=t?i(t.parent):e)&&t.top&&t.top.opener?i(t.top.opener):e)&&t.top&&t.top.opener&&t.top.opener.document?i(t.top.opener.document):e)?n.API.isFound=!0:a("API.get failed: Can't find the API!"),e},l.SCORM.API.getHandle=function(){var e=l.SCORM.API;return e.handle||e.isFound||(e.handle=e.get()),e.handle},l.SCORM.connection.initialize=function(){var e,t=!1,n=l.SCORM,i=(n.data.completionStatus,l.UTILS.trace),a=l.UTILS.StringToBoolean,o=n.debug,s="SCORM.connection.initialize ";if(i("connection.initialize called."),n.connection.isActive)i(s+"aborted: Connection already active.");else{var r=n.API.getHandle(),c=0;if(r){switch(n.version){case"1.2":t=a(r.LMSInitialize(""));break;case"2004":t=a(r.Initialize(""))}if(t)if(null!==(c=o.getCode())&&0===c){if(n.connection.isActive=!0,n.handleCompletionStatus&&(e=n.status("get"))){switch(e){case"not attempted":case"unknown":n.status("set","incomplete")}n.save()}}else t=!1,i(s+"failed. \nError code: "+c+" \nError info: "+o.getInfo(c));else i(null!==(c=o.getCode())&&0!==c?s+"failed. \nError code: "+c+" \nError info: "+o.getInfo(c):s+"failed: No response from server.")}else i(s+"failed: API is null.")}return t},l.SCORM.connection.terminate=function(){var e=!1,t=l.SCORM,n=t.data.exitStatus,i=t.data.completionStatus,a=l.UTILS.trace,o=l.UTILS.StringToBoolean,s=t.debug,r="SCORM.connection.terminate ";if(t.connection.isActive){var c=t.API.getHandle();if(c){if(t.handleExitMode&&!n)if("completed"!==i&&"passed"!==i)switch(t.version){case"1.2":e=t.set("cmi.core.exit","suspend");break;case"2004":e=t.set("cmi.exit","suspend")}else switch(t.version){case"1.2":e=t.set("cmi.core.exit","logout");break;case"2004":e=t.set("cmi.exit","normal")}if(e="1.2"!==t.version||t.save()){switch(t.version){case"1.2":e=o(c.LMSFinish(""));break;case"2004":e=o(c.Terminate(""))}e?t.connection.isActive=!1:a(r+"failed. \nError code: "+(n=s.getCode())+" \nError info: "+s.getInfo(n))}}else a(r+"failed: API is null.")}else a(r+"aborted: Connection already terminated.");return e},l.SCORM.data.get=function(e){var t=null,n=l.SCORM,i=l.UTILS.trace,a=n.debug,o="SCORM.data.get('"+e+"') ";if(n.connection.isActive){var s,r=n.API.getHandle();if(r){switch(n.version){case"1.2":t=r.LMSGetValue(e);break;case"2004":t=r.GetValue(e)}if(s=a.getCode(),""!==t||0===s)switch(e){case"cmi.core.lesson_status":case"cmi.completion_status":n.data.completionStatus=t;break;case"cmi.core.exit":case"cmi.exit":n.data.exitStatus=t}else i(o+"failed. \nError code: "+s+"\nError info: "+a.getInfo(s))}else i(o+"failed: API is null.")}else i(o+"failed: API connection is inactive.");return i(o+" value: "+t),String(t)},l.SCORM.data.set=function(e,t){var n=!1,i=l.SCORM,a=l.UTILS.trace,o=l.UTILS.StringToBoolean,s=i.debug,r="SCORM.data.set('"+e+"') ";if(i.connection.isActive){var c=i.API.getHandle();if(c){switch(i.version){case"1.2":n=o(c.LMSSetValue(e,t));break;case"2004":n=o(c.SetValue(e,t))}n?"cmi.core.lesson_status"!==e&&"cmi.completion_status"!==e||(i.data.completionStatus=t):a(r+"failed. \nError code: "+(i=s.getCode())+". \nError info: "+s.getInfo(i))}else a(r+"failed: API is null.")}else a(r+"failed: API connection is inactive.");return a(r+" value: "+t),n},l.SCORM.data.save=function(){var e=!1,t=l.SCORM,n=l.UTILS.trace,i=l.UTILS.StringToBoolean,a="SCORM.data.save failed";if(t.connection.isActive){var o=t.API.getHandle();if(o)switch(t.version){case"1.2":e=i(o.LMSCommit(""));break;case"2004":e=i(o.Commit(""))}else n(a+": API is null.")}else n(a+": API connection is inactive.");return e},l.SCORM.status=function(e,t){var n=!1,i=l.SCORM,a=l.UTILS.trace,o="SCORM.getStatus failed",s="";if(null!==e){switch(i.version){case"1.2":s="cmi.core.lesson_status";break;case"2004":s="cmi.completion_status"}switch(e){case"get":n=i.data.get(s);break;case"set":null!==t?n=i.data.set(s,t):(n=!1,a(o+": status was not specified."));break;default:n=!1,a(o+": no valid action was specified.")}}else a(o+": action was not specified.");return n},l.SCORM.debug.getCode=function(){var e=l.SCORM,t=e.API.getHandle(),n=l.UTILS.trace,i=0;if(t)switch(e.version){case"1.2":i=parseInt(t.LMSGetLastError(),10);break;case"2004":i=parseInt(t.GetLastError(),10)}else n("SCORM.debug.getCode failed: API is null.");return i},l.SCORM.debug.getInfo=function(e){var t=l.SCORM,n=t.API.getHandle(),i=l.UTILS.trace,a="";if(n)switch(t.version){case"1.2":a=n.LMSGetErrorString(e.toString());break;case"2004":a=n.GetErrorString(e.toString())}else i("SCORM.debug.getInfo failed: API is null.");return String(a)},l.SCORM.debug.getDiagnosticInfo=function(e){var t=l.SCORM,n=t.API.getHandle(),i=l.UTILS.trace,a="";if(n)switch(t.version){case"1.2":a=n.LMSGetDiagnostic(e);break;case"2004":a=n.GetDiagnostic(e)}else i("SCORM.debug.getDiagnosticInfo failed: API is null.");return String(a)},l.SCORM.init=l.SCORM.connection.initialize,l.SCORM.get=l.SCORM.data.get,l.SCORM.set=l.SCORM.data.set,l.SCORM.save=l.SCORM.data.save,l.SCORM.quit=l.SCORM.connection.terminate,l.UTILS.StringToBoolean=function(e){switch(typeof e){case"object":case"string":return/(true|1)/i.test(e);case"number":return!!e;case"boolean":return e;case"undefined":return null;default:return!1}},l.UTILS.trace=function(e){l.debug.isActive&&window.console&&window.console.log&&window.console.log(e)},l});