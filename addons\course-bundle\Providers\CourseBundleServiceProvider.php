<?php
/**
 * Course bundle service provider.
 *
 * @since 2.12.0
 * @package \Masteriyo\Addons\CourseBundle\Providers
 */

namespace Masteriyo\Addons\CourseBundle\Providers;

defined( 'ABSPATH' ) || exit;

use League\Container\ServiceProvider\AbstractServiceProvider;
use League\Container\ServiceProvider\BootableServiceProviderInterface;
use Masteriyo\Addons\CourseBundle\Controllers\CourseBundlesController;
use Masteriyo\Addons\CourseBundle\CourseBundleAddon;
use Masteriyo\Addons\CourseBundle\Models\CourseBundle;
use Masteriyo\Addons\CourseBundle\Models\OrderItemCourseBundle;
use Masteriyo\Addons\CourseBundle\Repository\CourseBundleRepository;
use Masteriyo\Addons\CourseBundle\Repository\OrderItemCourseBundleRepository;

/**
 * Course bundle service provider.
 *
 * @since 2.12.0
 */
class CourseBundleServiceProvider extends AbstractServiceProvider implements BootableServiceProviderInterface {
	/**
	 * The provided array is a way to let the container
	 * know that a service is provided by this service
	 * provider. Every service that is registered via
	 * this service provider must have an alias added
	 * to this array or it will be ignored
	 *
	 * @since 2.12.0
	 *
	 * @var array
	 */
	protected $provides = array(
		'addons.course-bundle',
		'course-bundle.store',
		'course-bundle.rest',
		'order-item.course-bundle',
		'order-item.course-bundle.store',
		'\Masteriyo\Addons\CourseBundle\Controllers\CourseBundlesController',
		'\Masteriyo\Addons\CourseBundle\CourseBundleAddon',
	);

	/**
	 * This is where the magic happens, within the method you can
	 * access the container and register or retrieve anything
	 * that you need to, but remember, every alias registered
	 * within this method must be declared in the `$provides` array.
	 *
	 * @since 2.12.0
	 */
	public function register() {
		$this->getLeagueContainer()->add( 'addons.course-bundle', CourseBundleAddon::class );
		$this->getLeagueContainer()->add( 'course-bundle.store', CourseBundleRepository::class );
		$this->getLeagueContainer()->add( 'course-bundle.rest', CourseBundlesController::class )
			->addArgument( 'permission' );
		$this->getLeagueContainer()->add( 'course-bundle', CourseBundle::class )
			->addArgument( 'course-bundle.store' );
		$this->getLeagueContainer()->add( 'order-item.course-bundle.store', OrderItemCourseBundleRepository::class );
		$this->getLeagueContainer()->add( 'order-item.course-bundle', OrderItemCourseBundle::class )
			->addArgument( 'order-item.course-bundle.store' );
	}


	/**
	 * In much the same way, this method has access to the container
	 * itself and can interact with it however you wish, the difference
	 * is that the boot method is invoked as soon as you register
	 * the service provider with the container meaning that everything
	 * in this method is eagerly loaded.
	 *
	 * If you wish to apply inflectors or register further service providers
	 * from this one, it must be from a bootable service provider like
	 * this one, otherwise they will be ignored.
	 *
	 * @since 2.12.0
	 */
	public function boot() {}
}
