<?php

/**
 * MailerLite integration settings class.
 *
 * @package Masteriyo\Addons\MailerLiteIntegration
 *
 * @since 2.14.4
 */

namespace Masteriyo\Addons\MailerLiteIntegration;

defined( 'ABSPATH' ) || exit;


use Masteriyo\EmailMarketingAndCRM\IntegrationSettings;

/**
 * MailerLite integration settings class.
 *
 * @since 2.14.4
 */
class MailerLiteIntegrationSettings extends IntegrationSettings {

	/**
	 * The settings data.
	 *
	 * @since 2.14.4
	 *
	 * @var array
	 */
	protected static $data = array(
		'enable_forced_email_subscription' => false,
		'is_connected'                     => false,
		'api_key'                          => '',
		'group'                            => '',
		'subscriber_consent_message'       => 'I would like to receive the newsletters.',
	);


	/**
	 * Get the option name for the settings.
	 *
	 * @since 2.14.4
	 *
	 * @return string
	 */
	protected static function get_option_name() {
		return 'masteriyo_mailerlite_integration_settings';
	}

	/**
	 * Get the MailerLite API key.
	 *
	 * @since 2.14.4
	 *
	 * @return string The MailerLite API key.
	 */
	public static function get_api_key() {
		return static::get( 'api_key' );
	}
}
