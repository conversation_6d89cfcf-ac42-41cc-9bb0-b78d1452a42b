<?php
/**
 * Order Line Item (coupon)
 *
 * @since   2.5.12
 */

namespace Masteriyo\Addons\Coupons\Models;

defined( 'ABSPATH' ) || exit;

use Masteriyo\Addons\Coupons\Repository\OrderItemCouponRepository;
use Masteriyo\Models\Order\OrderItem;

class OrderItemCoupon extends OrderItem {

	/**
	 * Stores order item data.
	 *
	 * @since 2.5.12
	 *
	 * @var array
	 */
	protected $extra_data = array(
		'code'     => '',
		'discount' => 0,
	);

	/**
	 * Get the order item if ID
	 *
	 * @since 2.5.12
	 *
	 * @param OrderItemCouponRepository $repository Order Repository.
	 */
	public function __construct( OrderItemCouponRepository $repository ) {
		parent::__construct();
		$this->repository = $repository;
	}


	/*
	|--------------------------------------------------------------------------
	| Getters
	|--------------------------------------------------------------------------
	*/

	/**
	 * Get order item name.
	 *
	 * @since 2.5.12
	 *
	 * @param  string $context What the value is for. Valid values are 'view' and 'edit'.
	 * @return string
	 */
	public function get_name( $context = 'view' ) {
		return $this->get_code( $context );
	}

	/**
	 * Get coupon code.
	 *
	 * @since 2.5.12
	 *
	 * @param  string $context What the value is for. Valid values are 'view' and 'edit'.
	 * @return string
	 */
	public function get_code( $context = 'view' ) {
		return $this->get_prop( 'code', $context );
	}

	/**
	 * Get discount amount.
	 *
	 * @since 2.5.12
	 *
	 * @param  string $context What the value is for. Valid values are 'view' and 'edit'.
	 * @return string
	 */
	public function get_discount( $context = 'view' ) {
		return $this->get_prop( 'discount', $context );
	}

	/**
	 * Get the order item type.
	 *
	 * @since  2.5.12
	 *
	 * @param  string $context What the value is for. Valid values are view and edit.
	 *
	 * @return string
	 */
	public function get_type( $context = 'view' ) {
		return 'coupon';
	}

	/*
	|--------------------------------------------------------------------------
	| Setters
	|--------------------------------------------------------------------------
	*/

	/**
	 * Set order item name.
	 *
	 * @since 2.5.12
	 *
	 * @param string $value Coupon code.
	 */
	public function set_name( $value ) {
		return $this->set_code( $value );
	}

	/**
	 * Set code.
	 *
	 * @since 2.5.12
	 *
	 * @param string $value Coupon code.
	 */
	public function set_code( $value ) {
		$this->set_prop( 'code', masteriyo_format_coupon_code( $value ) );
	}

	/**
	 * Set discount amount.
	 *
	 * @since 2.5.12
	 *
	 * @param string $value Discount.
	 */
	public function set_discount( $value ) {
		$this->set_prop( 'discount', masteriyo_format_decimal( $value ) );
	}
}
