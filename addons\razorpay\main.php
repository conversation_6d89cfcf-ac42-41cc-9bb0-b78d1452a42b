<?php

defined( 'ABSPATH' ) || exit;

/**
 * Addon Name: Ra<PERSON>pay
 * Addon URI: https://masteriyo.com/wordpress-lms/
 * Description: The "Razorpay" addon by Masteriyo enables secure and seamless transactions on your site using Razorpay payment gateway.
 * Author: Masteriyo
 * Author URI: https://masteriyo.com
 * Addon Type: Feature
 * Plan: Starter,Pro,Elite,Growth,Scale
 */

use Masteriyo\Pro\Addons;
use Masteriyo\Addons\Razorpay\RazorpayAddon;

define( 'MASTERIYO_RAZORPAY_ADDON_FILE', __FILE__ );
define( 'MASTERIYO_RAZORPAY_ADDON_BASENAME', plugin_basename( __FILE__ ) );
define( 'MASTERIYO_RAZORPAY_ADDON_DIR', __DIR__ );
define( 'MASTERIYO_RAZORPAY_ADDON_SLUG', 'razorpay' );
define( 'MASTERIYO_RAZORPAY_ADDON_TEMPLATES', __DIR__ . '/templates' );
define( 'MASTERIYO_RAZORPAY_ADDON_ASSETS_URL', plugins_url( 'assets', MASTERIYO_RAZORPAY_ADDON_FILE ) );

// Bail early if the addon is not active.
if ( ! ( new Addons() )->is_active( MASTERIYO_RAZORPAY_ADDON_SLUG ) ) {
	return;
}

// Include the Razorpay helper file that possibly contains necessary configurations, functions, and setups for the Razorpay integration.
require_once __DIR__ . '/helper/razorpay.php';

// Initialize the Razorpay addon.
RazorpayAddon::instance()->init();
