<?php

defined( 'ABSPATH' ) || exit;

/**
 * Addon Name: Public Profile
 * Addon URI: https://masteriyo.com/wordpress-lms/
 * Description: The "Public Profile" addon by Masteriyo provides instructors or students with a public profile page visible to others.
 * Author: Masteriyo
 * Author URI: https://masteriyo.com
 * Addon Type: Feature
 * Plan: Basic,Starter,Pro,Elite,Growth,Scale
 */

use Masteriyo\Pro\Addons;
use Masteriyo\Addons\PublicProfile\PublicProfileAddon;

define( 'MASTERIYO_PUBLIC_PROFILE_ADDON_FILE', __FILE__ );
define( 'MASTERIYO_PUBLIC_PROFILE_ADDON_BASENAME', plugin_basename( __FILE__ ) );
define( 'MASTERIYO_PUBLIC_PROFILE_ADDON_DIR', __DIR__ );
define( 'MASTERIYO_PUBLIC_PROFILE_ADDON_SLUG', 'public-profile' );
define( 'MASTERIYO_PUBLIC_PROFILE_ADDON_TEMPLATES', __DIR__ . '/templates' );
define( 'MASTERIYO_PUBLIC_PROFILE_ADDON_ASSETS_URL', plugins_url( 'assets', MASTERIYO_PUBLIC_PROFILE_ADDON_FILE ) );




// Bail early if the addon is not active.
if ( ! ( new Addons() )->is_active( MASTERIYO_PUBLIC_PROFILE_ADDON_SLUG ) ) {
	return;
}

require_once __DIR__ . '/helper/public-profile.php';
require_once __DIR__ . '/template-functions.php';
require_once __DIR__ . '/template-hooks.php';

// Initiate public profile addon.
PublicProfileAddon::instance()->init();
